import{c as m}from"./index-114510d6.js";const y=r=>{const o=[...r];for(let e=o.length-1;e>0;e--){const t=Math.floor(Math.random()*(e+1));[o[e],o[t]]=[o[t],o[e]]}return o},w=m((r,o)=>({currentSession:null,studySetContent:null,studySets:[],sessions:[],isLoading:!1,error:null,actionHistory:[],currentActionIndex:-1,canUndo:!1,canRedo:!1,fetchStudySetContent:async(e,t=!1)=>{var s;const{studySetContent:n}=o();if(t||!n||((s=n.studySet)==null?void 0:s.id)!==e){r({isLoading:!0,error:null});try{const a=localStorage.getItem("auth_token"),i=await fetch(`/api/study-sets/${e}/content`,{headers:{Authorization:`Bearer ${a}`}});if(!i.ok){const u=await i.json();throw new Error(u.error||"Failed to fetch study set content")}const c=await i.json();if(c.success)r({studySetContent:{studySet:c.data.studySet,flashcards:c.data.flashcards||[],questions:c.data.questions||[]},isLoading:!1});else throw new Error(c.error)}catch(a){throw r({error:a.message||"Failed to fetch study set content",isLoading:!1}),a}}},startStudySession:async(e,t,n=!1)=>{var f,S,h;const{studySetContent:s,fetchStudySetContent:a}=o();(!s||((f=s.studySet)==null?void 0:f.id)!==e)&&await a(e);const i=o().studySetContent;if(!i)throw new Error("Failed to load study set content");const c=t==="flashcards"?((S=i.flashcards)==null?void 0:S.length)||0:((h=i.questions)==null?void 0:h.length)||0;if(c===0)throw new Error("No study materials found in this set");let u;if(n){if(u=Array.from({length:c},(l,d)=>d),t==="flashcards"&&i.flashcards){const l=y(i.flashcards);r(d=>({studySetContent:{...d.studySetContent,flashcards:l}}))}else if(t==="quiz"&&i.questions){const l=y(i.questions);r(d=>({studySetContent:{...d.studySetContent,questions:l}}))}}r({currentSession:{studySetId:e,type:t,startTime:new Date,currentIndex:0,totalItems:c,reviewedItems:[],flaggedItems:[],correctAnswers:t==="quiz"?0:void 0,timeSpent:0,isShuffled:n,originalOrder:u}})},endStudySession:()=>{r({currentSession:null})},nextItem:()=>{const{currentSession:e,addToHistory:t}=o();if(!e)return;const n=e.currentIndex===e.totalItems-1?0:e.currentIndex+1;t({type:"NEXT_ITEM",payload:{fromIndex:e.currentIndex,toIndex:n},previousState:{currentIndex:e.currentIndex},timestamp:Date.now()}),r({currentSession:{...e,currentIndex:n}})},previousItem:()=>{const{currentSession:e,addToHistory:t}=o();if(!e)return;const n=e.currentIndex===0?e.totalItems-1:e.currentIndex-1;t({type:"PREVIOUS_ITEM",payload:{fromIndex:e.currentIndex,toIndex:n},previousState:{currentIndex:e.currentIndex},timestamp:Date.now()}),r({currentSession:{...e,currentIndex:n}})},goToItem:e=>{const{currentSession:t}=o();if(!t)return;const n=Math.max(0,Math.min(e,t.totalItems-1));r({currentSession:{...t,currentIndex:n}})},toggleFlag:e=>{const{currentSession:t,addToHistory:n}=o();if(!t)return;const s=t.flaggedItems.includes(e),a=s?t.flaggedItems.filter(i=>i!==e):[...t.flaggedItems,e];n({type:"TOGGLE_FLAG",payload:{itemId:e,wasFlagged:s},previousState:{flaggedItems:t.flaggedItems},timestamp:Date.now()}),r({currentSession:{...t,flaggedItems:a}})},markReviewed:e=>{const{currentSession:t}=o();t&&(t.reviewedItems.includes(t.currentIndex)||r({currentSession:{...t,reviewedItems:[...t.reviewedItems,t.currentIndex]}}))},submitQuizAnswer:(e,t,n)=>{const{currentSession:s,markReviewed:a}=o();!s||s.type!=="quiz"||(a(e),n&&r({currentSession:{...s,correctAnswers:(s.correctAnswers||0)+1}}))},updateTimeSpent:e=>{const{currentSession:t}=o();t&&r({currentSession:{...t,timeSpent:t.timeSpent+e}})},addToHistory:e=>{const{actionHistory:t,currentActionIndex:n}=o(),s=t.slice(0,n+1);s.push(e);const a=s.slice(-50);r({actionHistory:a,currentActionIndex:a.length-1,canUndo:a.length>0,canRedo:!1})},undo:()=>{const{actionHistory:e,currentActionIndex:t,currentSession:n}=o();if(t<0||!n)return;const s=e[t];r({currentSession:{...n,...s.previousState},currentActionIndex:t-1,canUndo:t>0,canRedo:!0})},redo:()=>{const{actionHistory:e,currentActionIndex:t,currentSession:n}=o();if(t>=e.length-1||!n)return;const s=t+1,a=e[s];switch(a.type){case"NEXT_ITEM":o().nextItem();break;case"PREVIOUS_ITEM":o().previousItem();break;case"TOGGLE_FLAG":o().toggleFlag(a.payload.itemId);break;case"MARK_REVIEWED":o().markReviewed(a.payload.itemId);break}r({currentActionIndex:s,canUndo:!0,canRedo:s<e.length-1})},clearHistory:()=>{r({actionHistory:[],currentActionIndex:-1,canUndo:!1,canRedo:!1})},fetchStudySets:async()=>{r({isLoading:!0,error:null});try{const e=localStorage.getItem("auth_token"),t=await fetch("/api/study-sets",{headers:{Authorization:`Bearer ${e}`}});if(!t.ok){const s=await t.json();throw new Error(s.error||"Failed to fetch study sets")}const n=await t.json();if(n.success)r({studySets:n.data,isLoading:!1});else throw new Error(n.error)}catch(e){throw r({error:e.message||"Failed to fetch study sets",isLoading:!1}),e}},fetchStudySessions:async(e="30d")=>{r({isLoading:!0,error:null});try{const t=localStorage.getItem("auth_token"),n=await fetch(`/api/study-sessions?timeRange=${e}`,{headers:{Authorization:`Bearer ${t}`}});if(!n.ok){const a=await n.json();throw new Error(a.error||"Failed to fetch study sessions")}const s=await n.json();if(s.success){const a=s.data.map(i=>({...i,startTime:new Date(i.startTime),endTime:i.endTime?new Date(i.endTime):void 0}));r({sessions:a,isLoading:!1})}else throw new Error(s.error)}catch(t){throw r({error:t.message||"Failed to fetch study sessions",isLoading:!1}),t}},invalidateStudySetContent:e=>{var n;const{studySetContent:t}=o();e?((n=t==null?void 0:t.studySet)==null?void 0:n.id)===e&&r({studySetContent:null}):r({studySetContent:null})},refreshStudySetContent:async e=>{await o().fetchStudySetContent(e,!0)},invalidateStudySets:()=>{r({studySets:[]})}}));export{w as useStudyStore};
