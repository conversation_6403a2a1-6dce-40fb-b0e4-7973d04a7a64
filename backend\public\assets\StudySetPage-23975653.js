import{r as u,j as e,R as oe,c as ze,_ as se,B as I,a as Ne,b as ce,H as Ce,d as Ee,C as Z,e as Ge,u as Le}from"./index-114510d6.js";import{useStudyStore as ke}from"./studyStore-ba57596c.js";import{u as Re}from"./documentStore-9ee8a311.js";import{C as B,D as Ae,a as $e,d as de,n as Fe,b as Te}from"./DifficultySelector-2668bf1d.js";import{u as qe}from"./useUserSettings-b025a135.js";const Pe=({value:s,onChange:d,label:o,min:m=1,max:i=100,placeholder:c="Enter a number",error:n,className:S="",disabled:b=!1})=>{const[w,C]=u.useState(s.toString()),[v,k]=u.useState("");u.useEffect(()=>{s.toString()!==w&&C(s.toString())},[s]);const q=j=>{if(C(j),k(""),j.trim()==="")return;if(!/^\d+$/.test(j.trim())){k("Please enter a whole number");return}const D=parseInt(j.trim(),10);if(D<m){k(`Number must be at least ${m}`);return}if(D>i){k(`Number must be at most ${i}`);return}d(D)},l=j=>{const D=j.target.value;(D===""||/^\d+$/.test(D))&&q(D)},_=()=>{(w.trim()===""||v)&&(C(s.toString()),k(""))},y=j=>{!["Backspace","Delete","Tab","Escape","Enter","ArrowLeft","ArrowRight","ArrowUp","ArrowDown"].includes(j.key)&&!(j.key>="0"&&j.key<="9")&&!(j.ctrlKey&&["a","c","v","x","z"].includes(j.key.toLowerCase()))&&j.preventDefault()},A=()=>{const j=Math.min(s+1,i);d(j)},E=()=>{const j=Math.max(s-1,m);d(j)},$=n||v;return e.jsxs("div",{className:S,children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:o}),e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"text",inputMode:"numeric",value:w,onChange:l,onBlur:_,onKeyDown:y,placeholder:c,disabled:b,className:`
            w-full px-3 py-2 pr-20 bg-background-primary border rounded-lg text-white 
            placeholder-gray-400 focus:outline-none focus:ring-2 transition-colors
            ${$?"border-red-500 focus:border-red-500 focus:ring-red-500/50":"border-gray-600 focus:border-primary-500 focus:ring-primary-500/50"}
            ${b?"opacity-50 cursor-not-allowed":""}
          `}),e.jsxs("div",{className:"absolute right-1 top-1 bottom-1 flex flex-col",children:[e.jsx("button",{type:"button",onClick:A,disabled:b||s>=i,className:`\r
              flex-1 px-2 text-gray-400 hover:text-white hover:bg-gray-600 \r
              rounded-tr-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed\r
              focus:outline-none focus:bg-gray-600\r
            `,tabIndex:-1,children:e.jsx("svg",{className:"w-3 h-3",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z",clipRule:"evenodd"})})}),e.jsx("button",{type:"button",onClick:E,disabled:b||s<=m,className:`\r
              flex-1 px-2 text-gray-400 hover:text-white hover:bg-gray-600 \r
              rounded-br-md transition-colors disabled:opacity-50 disabled:cursor-not-allowed\r
              focus:outline-none focus:bg-gray-600\r
            `,tabIndex:-1,children:e.jsx("svg",{className:"w-3 h-3",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z",clipRule:"evenodd"})})})]})]}),$&&e.jsx("p",{className:"mt-1 text-sm text-red-400",children:$}),!$&&e.jsxs("p",{className:"mt-1 text-xs text-gray-500",children:["Enter a number between ",m," and ",i]})]})},Be=({progress:s=0,isIndeterminate:d=!1,label:o,className:m="",size:i="md",variant:c="primary"})=>{const n={sm:"h-1.5",md:"h-2",lg:"h-3"},S={primary:"bg-primary-500",success:"bg-green-500",warning:"bg-yellow-500",error:"bg-red-500"},b={sm:"text-xs",md:"text-sm",lg:"text-base"};return e.jsxs("div",{className:`w-full ${m}`,children:[o&&e.jsxs("div",{className:`flex justify-between items-center mb-2 ${b[i]}`,children:[e.jsx("span",{className:"text-gray-300 font-medium",children:o}),!d&&e.jsxs("span",{className:"text-gray-400",children:[Math.round(s),"%"]})]}),e.jsx("div",{className:`w-full bg-gray-700 rounded-full overflow-hidden ${n[i]}`,children:e.jsx("div",{className:`${S[c]} transition-all duration-300 ease-out rounded-full ${n[i]} ${d?"animate-pulse w-full":"transition-[width] duration-500"}`,style:d?void 0:{width:`${Math.min(100,Math.max(0,s))}%`}})})]})},De=({isGenerating:s,stage:d,estimatedTime:o,className:m=""})=>{const[i,c]=oe.useState(0),[n,S]=oe.useState(0);if(oe.useEffect(()=>{let w,C;return s?(S(0),c(0),w=setInterval(()=>{S(v=>v+1)},1e3),C=setInterval(()=>{c(v=>{const k=v<30?3:v<60?2:v<85?1:.2;return Math.min(90,v+k)})},1e3)):(c(0),S(0)),()=>{w&&clearInterval(w),C&&clearInterval(C)}},[s]),oe.useEffect(()=>{!s&&i>0&&(c(100),setTimeout(()=>c(0),1e3))},[s,i]),!s&&i===0)return null;const b=w=>{if(w<60)return`${w}s`;const C=Math.floor(w/60),v=w%60;return`${C}m ${v}s`};return e.jsx("div",{className:`bg-background-secondary rounded-lg p-4 border border-gray-600 ${m}`,children:e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"animate-spin rounded-full h-5 w-5 border-2 border-primary-500 border-t-transparent"}),e.jsx("span",{className:"text-white font-medium",children:d||"Generating with AI..."})]}),e.jsxs("div",{className:"text-sm text-gray-400",children:[n>0&&b(n),o&&n===0&&` (Est. ${b(o)})`]})]}),e.jsx(Be,{progress:i,isIndeterminate:i===0,variant:"primary",size:"md"}),e.jsx("div",{className:"text-xs text-gray-500 text-center",children:s?"Please wait while we generate your content...":"Generation complete!"})]})})},Se=({value:s,onChange:d,min:o,max:m,label:i,placeholder:c})=>{const[n,S]=u.useState(s.toString()),[b,w]=u.useState("");u.useEffect(()=>{S(s.toString())},[s]);const C=k=>{if(S(k),b&&w(""),k==="")return;const q=parseInt(k);if(isNaN(q)){w("Must be a number");return}if(q<o){w(`Minimum is ${o}`);return}if(q>m){w(`Maximum is ${m}`);return}d(q)},v=()=>{const k=parseInt(n);(isNaN(k)||n==="")&&(S(s.toString()),w(""))};return e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("label",{className:"text-xs text-gray-400 whitespace-nowrap",children:[i,":"]}),e.jsxs("div",{className:"relative",children:[e.jsx("input",{type:"text",value:n,onChange:k=>C(k.target.value),onBlur:v,placeholder:c,className:`w-16 px-2 py-1 text-xs rounded text-white text-center focus:outline-none focus:ring-1 focus:ring-primary-500 ${b?"bg-red-900/20 border border-red-500":"bg-background-primary border border-gray-600"}`}),b&&e.jsx("div",{className:"absolute top-full left-0 mt-1 text-xs text-red-400 whitespace-nowrap z-10",children:b})]})]})},Ie=({selectedDocuments:s,onSelectionChange:d,documentPageRanges:o,onPageRangeChange:m,maxSelection:i=5})=>{const{documents:c,fetchDocuments:n,isLoading:S}=Re(),[b,w]=u.useState("");u.useEffect(()=>{c.length===0&&n()},[c.length,n]);const C=c.filter(l=>l.is_processed&&l.filename.toLowerCase().includes(b.toLowerCase())),v=l=>{if(s.includes(l))d(s.filter(y=>y!==l));else if(s.length<i){d([...s,l]);const y=c.find(A=>A.id===l);y!=null&&y.page_count&&y.page_count>0&&m(l,{startPage:1,endPage:y.page_count})}},k=()=>c.filter(l=>s.includes(l.id)),q=(l,_,y)=>{const A=o[l]||{startPage:1,endPage:1},E=c.find(D=>D.id===l),$=(E==null?void 0:E.page_count)||1;let j={...A};_==="startPage"?j.startPage=Math.max(1,Math.min(y,$,j.endPage)):j.endPage=Math.max(j.startPage,Math.min(y,$)),m(l,j)};return S?e.jsx("div",{className:"flex items-center justify-center py-8",children:e.jsx("div",{className:"text-gray-400",children:"Loading documents..."})}):c.length===0?e.jsxs("div",{className:"text-center py-8",children:[e.jsx("div",{className:"text-gray-400 mb-4",children:"No documents found"}),e.jsx("p",{className:"text-sm text-gray-500",children:"Upload some documents first to generate study materials."})]}):e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{children:e.jsx("input",{type:"text",placeholder:"Search documents...",value:b,onChange:l=>w(l.target.value),className:"w-full px-3 py-2 bg-background-secondary border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500"})}),s.length>0&&e.jsxs("div",{className:"bg-primary-500/10 border border-primary-500/30 rounded-lg p-3",children:[e.jsxs("div",{className:"text-sm text-primary-400 mb-2",children:["Selected ",s.length," of ",i," documents:"]}),e.jsx("div",{className:"space-y-3",children:k().map(l=>{var A;const _=l.page_count&&l.page_count>0,y=o[l.id];return e.jsxs("div",{className:"bg-background-secondary/50 rounded-lg p-3",children:[e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("span",{className:"text-sm text-gray-300 truncate flex-1 mr-2",children:l.filename}),e.jsx("button",{onClick:()=>v(l.id),className:"text-red-400 hover:text-red-300 text-sm px-2 py-1 rounded hover:bg-red-400/10 transition-colors",children:"Remove"})]}),_&&e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"text-xs text-gray-400",children:["📄 ",l.page_count," ",l.file_type==="pptx"?"slides":"pages"," ","available"]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx(Se,{label:"From",value:(y==null?void 0:y.startPage)||1,onChange:E=>q(l.id,"startPage",E),min:1,max:l.page_count||1,placeholder:"1"}),e.jsx(Se,{label:"To",value:(y==null?void 0:y.endPage)||l.page_count||1,onChange:E=>q(l.id,"endPage",E),min:(y==null?void 0:y.startPage)||1,max:l.page_count||1,placeholder:((A=l.page_count)==null?void 0:A.toString())||"1"})]}),e.jsx("button",{onClick:()=>m(l.id,{startPage:1,endPage:l.page_count||1}),className:"text-xs text-primary-400 hover:text-primary-300 px-2 py-1 rounded hover:bg-primary-400/10 transition-colors whitespace-nowrap",children:"Use All"})]}),y&&e.jsxs("div",{className:"text-xs text-gray-500",children:["Using ",y.endPage-y.startPage+1," of"," ",l.page_count," ",l.file_type==="pptx"?"slides":"pages"]})]}),!_&&e.jsxs("div",{className:"text-xs text-gray-500",children:["📄 ",l.file_type.toUpperCase()," • Full document will be used"]})]},l.id)})})]}),e.jsx("div",{className:"max-h-64 overflow-y-auto space-y-2",children:C.map(l=>{const _=s.includes(l.id),y=!_&&s.length<i,A=l.page_count&&l.page_count>0;return e.jsx("div",{className:`
                p-3 rounded-lg border cursor-pointer transition-all
                ${_?"bg-primary-500/20 border-primary-500":y?"bg-background-secondary border-gray-600 hover:border-gray-500":"bg-gray-800 border-gray-700 opacity-50 cursor-not-allowed"}
              `,onClick:()=>y||_?v(l.id):null,children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("div",{className:"flex-1 min-w-0",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-lg",children:l.file_type==="pdf"?"📄":l.file_type==="docx"?"📝":l.file_type==="txt"?"📃":"📊"}),e.jsxs("div",{className:"min-w-0 flex-1",children:[e.jsx("p",{className:"text-white font-medium truncate",children:l.filename}),e.jsxs("div",{className:"flex items-center space-x-2 text-sm text-gray-400",children:[e.jsxs("span",{children:[l.file_type.toUpperCase()," •"," ",Math.round(l.file_size/1024)," KB"]}),A&&e.jsxs("span",{children:["• ",l.page_count," ",l.file_type==="pptx"?"slides":"pages"]})]})]})]})}),e.jsx("div",{className:`
                  w-5 h-5 rounded border-2 flex items-center justify-center
                  ${_?"bg-primary-500 border-primary-500":"border-gray-500"}
                `,children:_&&e.jsx("svg",{className:"w-3 h-3 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})})]})},l.id)})}),C.length===0&&b&&e.jsx("div",{className:"text-center py-4 text-gray-400",children:"No documents match your search."})]})},Oe=[{value:B.SHORT,label:"Short",description:"Concise answers (1-2 sentences)",icon:"📝"},{value:B.MEDIUM,label:"Medium",description:"Balanced detail (2-3 sentences)",icon:"📄"},{value:B.LONG,label:"Long",description:"Comprehensive answers (3-5 sentences)",icon:"📋"}],Ve=s=>{switch(s){case B.SHORT:return"bg-background-secondary text-text-primary border-emerald-500/30 hover:bg-emerald-500/10 hover:border-emerald-500/50";case B.MEDIUM:return"bg-background-secondary text-text-primary border-primary-500/30 hover:bg-primary-500/10 hover:border-primary-500/50";case B.LONG:return"bg-background-secondary text-text-primary border-indigo-500/30 hover:bg-indigo-500/10 hover:border-indigo-500/50";default:return"bg-background-secondary text-text-primary border-primary-500/30 hover:bg-primary-500/10 hover:border-primary-500/50"}},Ue=s=>{switch(s){case B.SHORT:return"bg-emerald-500/20 text-emerald-300 border-emerald-500 shadow-lg shadow-emerald-500/20";case B.MEDIUM:return"bg-primary-500/20 text-primary-300 border-primary-500 shadow-lg shadow-primary-500/20";case B.LONG:return"bg-indigo-500/20 text-indigo-300 border-indigo-500 shadow-lg shadow-indigo-500/20";default:return"bg-primary-500/20 text-primary-300 border-primary-500 shadow-lg shadow-primary-500/20"}},Me=({value:s,onChange:d,className:o="",disabled:m=!1,label:i="Content Length"})=>e.jsxs("div",{className:`space-y-3 ${o}`,children:[e.jsx("label",{className:"block text-sm font-medium text-text-primary",children:i}),e.jsx("div",{className:"grid grid-cols-3 gap-3",children:Oe.map(c=>{const n=s===c.value,S=n?Ue(c.value):Ve(c.value);return e.jsxs("button",{type:"button",onClick:()=>!m&&d(c.value),disabled:m,className:`
                relative p-4 rounded-lg border-2 text-sm font-medium transition-all duration-200 transform-gpu
                ${S}
                ${m?"opacity-50 cursor-not-allowed":"cursor-pointer hover:scale-105"}
                ${n?"ring-2 ring-offset-2 ring-primary-500 ring-offset-background-primary":""}
                focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 focus:ring-offset-background-primary
              `,title:c.description,"aria-pressed":n,children:[e.jsxs("div",{className:"text-center space-y-2",children:[e.jsx("div",{className:"text-2xl",children:c.icon}),e.jsx("div",{className:"font-semibold",children:c.label}),e.jsx("div",{className:`text-xs ${n?"text-white/90":"text-text-secondary"}`,children:c.description})]}),n&&e.jsx("div",{className:"absolute top-2 right-2",children:e.jsx("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",children:e.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})})]},c.value)})}),e.jsx("p",{className:"text-xs text-text-muted",children:"Choose how detailed you want the flashcard answers to be. This affects the length and depth of explanations."})]}),He=ze(s=>({isGenerating:!1,generationProgress:"",lastGenerated:null,generateFlashcards:async d=>{s({isGenerating:!0,generationProgress:"Preparing documents..."});try{const o=localStorage.getItem("auth_token");s({generationProgress:"Generating flashcards with AI..."});const m=await fetch("/api/ai/generate-flashcards",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${o}`},body:JSON.stringify(d)});if(!m.ok){const c=await m.json();throw new Error(c.error||"Generation failed")}const i=await m.json();if(i.success){s({lastGenerated:{studySet:i.data.studySet,content:i.data.flashcards,type:"flashcards"},isGenerating:!1,generationProgress:""});try{const{useStudyStore:c}=await se(()=>import("./studyStore-ba57596c.js"),["assets/studyStore-ba57596c.js","assets/index-114510d6.js","assets/index-caeb64d5.css"]),n=c.getState();n.refreshStudySetContent(i.data.studySet.id),n.invalidateStudySets()}catch(c){console.warn("Failed to refresh study set cache:",c)}return{studySet:i.data.studySet,flashcards:i.data.flashcards,creditsRemaining:i.data.creditsRemaining}}else throw new Error(i.error)}catch(o){throw s({isGenerating:!1,generationProgress:""}),o}},generateQuiz:async d=>{s({isGenerating:!0,generationProgress:"Preparing documents..."});try{const o=localStorage.getItem("auth_token");s({generationProgress:"Generating quiz questions with AI..."});const m=await fetch("/api/ai/generate-quiz",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${o}`},body:JSON.stringify(d)});if(!m.ok){const c=await m.json();throw new Error(c.error||"Generation failed")}const i=await m.json();if(i.success){s({lastGenerated:{studySet:i.data.studySet,content:i.data.questions,type:"quiz"},isGenerating:!1,generationProgress:""});try{const{useStudyStore:c}=await se(()=>import("./studyStore-ba57596c.js"),["assets/studyStore-ba57596c.js","assets/index-114510d6.js","assets/index-caeb64d5.css"]),n=c.getState();n.refreshStudySetContent(i.data.studySet.id),n.invalidateStudySets()}catch(c){console.warn("Failed to refresh study set cache:",c)}return{studySet:i.data.studySet,questions:i.data.questions,creditsRemaining:i.data.creditsRemaining}}else throw new Error(i.error)}catch(o){throw s({isGenerating:!1,generationProgress:""}),o}},generateMoreFlashcards:async d=>{s({isGenerating:!0,generationProgress:"Preparing documents..."});try{const o=localStorage.getItem("auth_token");s({generationProgress:"Generating additional flashcards with AI..."});const m=await fetch("/api/ai/generate-more-flashcards",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${o}`},body:JSON.stringify(d)});if(!m.ok){const c=await m.json();throw new Error(c.error||"Generation failed")}const i=await m.json();if(i.success){s({isGenerating:!1,generationProgress:""});try{const{useStudyStore:c}=await se(()=>import("./studyStore-ba57596c.js"),["assets/studyStore-ba57596c.js","assets/index-114510d6.js","assets/index-caeb64d5.css"]);c.getState().refreshStudySetContent(d.studySetId)}catch(c){console.warn("Failed to refresh study set cache:",c)}return{flashcards:i.data.flashcards,creditsRemaining:i.data.creditsRemaining}}else throw new Error(i.error)}catch(o){throw s({isGenerating:!1,generationProgress:""}),o}},generateMoreQuizQuestions:async d=>{s({isGenerating:!0,generationProgress:"Preparing documents..."});try{const o=localStorage.getItem("auth_token");s({generationProgress:"Generating additional quiz questions with AI..."});const m=await fetch("/api/ai/generate-more-quiz-questions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${o}`},body:JSON.stringify(d)});if(!m.ok){const c=await m.json();throw new Error(c.error||"Generation failed")}const i=await m.json();if(i.success)return s({isGenerating:!1,generationProgress:""}),{questions:i.data.questions,creditsRemaining:i.data.creditsRemaining};throw new Error(i.error)}catch(o){throw s({isGenerating:!1,generationProgress:""}),o}},clearLastGenerated:()=>{s({lastGenerated:null})}})),Qe=({selectedCount:s,totalCount:d,onDeleteSelected:o,onClearSelection:m,isLoading:i=!1,className:c="",itemType:n="item"})=>s===0?null:e.jsx("div",{className:`bg-gray-800 border border-gray-700 rounded-lg p-4 mb-4 ${c}`,children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("span",{className:"text-white font-medium",children:[s," of ",d," ",n,s!==1?"s":""," selected"]}),e.jsx("button",{onClick:m,className:"text-gray-400 hover:text-white text-sm underline",disabled:i,children:"Clear selection"})]}),e.jsx("div",{className:"flex items-center space-x-3",children:e.jsx(I,{onClick:o,variant:"danger",size:"sm",isLoading:i,disabled:i,className:"px-4 py-2",children:"Delete Selected"})})]})}),Je=({studySetId:s,flashcards:d,onFlashcardAdded:o,onFlashcardUpdated:m,onFlashcardDeleted:i,onFlashcardsGenerated:c})=>{const{alert:n,confirm:S}=Ne(),{user:b}=ce(),{generateMoreFlashcards:w}=He(),{settings:C,updateSettings:v}=qe(),[k,q]=u.useState(!0),[l,_]=u.useState([]),[y,A]=u.useState({}),[E,$]=u.useState(25),[j,D]=u.useState(""),[Y,L]=u.useState(Ae.MEDIUM),[W,U]=u.useState(B.MEDIUM),[O,J]=u.useState(!1),[K,f]=u.useState(!1),[T,V]=u.useState({front:"",back:"",difficulty_level:3}),[H,z]=u.useState(null),[F,G]=u.useState({front:"",back:"",difficulty_level:3}),[M,p]=u.useState([]),[N,h]=u.useState(!1),[Q,ee]=u.useState(!1),[X,te]=u.useState(new Set),ue=r=>{te(g=>{const t=new Set(g);return t.has(r)?t.delete(r):t.add(r),t})},me=()=>{const r=new Set(d.map(g=>g.id));te(r)},he=()=>{te(new Set)},re=(r,g)=>{A(t=>({...t,[r]:g}))},xe=(r,g)=>{g?p(t=>[...t,r]):(p(t=>t.filter(a=>a!==r)),h(!1))},ge=r=>{h(r),p(r?d.map(g=>g.id):[])},ae=()=>{p([]),h(!1)},pe=async()=>{if(M.length!==0){if(!(C!=null&&C.skip_delete_confirmations)){let r=!1;if(!await S({title:"Delete Flashcards",message:`Are you sure you want to delete ${M.length} flashcard${M.length!==1?"s":""}?`,variant:"danger",confirmText:"Delete",cancelText:"Cancel",buttonLayout:"corners",showNeverAskAgain:!0,onNeverAskAgainChange:t=>{r=t}}))return;if(r)try{await v({skip_delete_confirmations:!0})}catch(t){console.error("Failed to update user settings:",t)}}await ye()}},ye=async()=>{ee(!0);try{const r=await fetch("/api/flashcards/bulk-delete",{method:"POST",headers:{Authorization:`Bearer ${localStorage.getItem("auth_token")}`,"Content-Type":"application/json"},body:JSON.stringify({flashcardIds:M})});if(!r.ok){const a=await r.json();throw new Error(a.error||"Failed to delete flashcards")}const g=await r.json(),{deletedCount:t}=g.data;M.forEach(a=>i(a)),ae(),await n({title:"Success",message:`${t} flashcard${t!==1?"s":""} deleted successfully!`,variant:"success"})}catch(r){await n({title:"Error",message:r.message||"Failed to delete flashcards",variant:"error"})}finally{ee(!1)}},fe=async()=>{if(!T.front.trim()||!T.back.trim()){await n({title:"Validation Error",message:"Both front and back content are required.",variant:"error"});return}try{const r=await fetch(`/api/flashcards/study-set/${s}`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("auth_token")}`},body:JSON.stringify({front:T.front.trim(),back:T.back.trim(),difficulty_level:T.difficulty_level,is_ai_generated:!1})});if(!r.ok)throw new Error("Failed to create flashcard");const g=await r.json();o(g.data),V({front:"",back:"",difficulty_level:3}),f(!1),await n({title:"Success",message:"Flashcard added successfully!",variant:"success"})}catch(r){await n({title:"Error",message:r.message||"Failed to add flashcard",variant:"error"})}},be=r=>{z(r),G({front:r.front,back:r.back,difficulty_level:typeof r.difficulty_level=="string"?Te(r.difficulty_level):r.difficulty_level||3})},ve=async()=>{if(H){if(!F.front.trim()||!F.back.trim()){await n({title:"Validation Error",message:"Both front and back content are required.",variant:"error"});return}try{const r=await fetch(`/api/flashcards/${H.id}`,{method:"PUT",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("auth_token")}`},body:JSON.stringify({front:F.front.trim(),back:F.back.trim(),difficulty_level:F.difficulty_level})});if(!r.ok)throw new Error("Failed to update flashcard");const g=await r.json();m(g.data),z(null),G({front:"",back:"",difficulty_level:3}),await n({title:"Success",message:"Flashcard updated successfully!",variant:"success"})}catch(r){await n({title:"Error",message:r.message||"Failed to update flashcard",variant:"error"})}}},je=()=>{z(null),G({front:"",back:"",difficulty_level:3})},ne=async r=>{if(C!=null&&C.skip_delete_confirmations){await ie(r);return}let g=!1;if(await S({title:"Delete Flashcard",message:`Are you sure you want to delete this flashcard?

Front: ${r.front.substring(0,50)}${r.front.length>50?"...":""}`,variant:"danger",confirmText:"Delete",cancelText:"Cancel",buttonLayout:"corners",showNeverAskAgain:!0,onNeverAskAgainChange:a=>{g=a}})){if(g)try{await v({skip_delete_confirmations:!0})}catch(a){console.error("Failed to update user settings:",a)}await ie(r)}},ie=async r=>{try{if(!(await fetch(`/api/flashcards/${r.id}`,{method:"DELETE",headers:{Authorization:`Bearer ${localStorage.getItem("auth_token")}`}})).ok)throw new Error("Failed to delete flashcard");i(r.id),await n({title:"Success",message:"Flashcard deleted successfully!",variant:"success"})}catch(g){await n({title:"Error",message:g.message||"Failed to delete flashcard",variant:"error"})}},le=()=>Math.ceil(E/5),we=async()=>{if(l.length===0){await n({title:"No Documents Selected",message:"Please select at least one document to generate flashcards from.",variant:"warning"});return}const r=le();if(b&&b.credits_remaining<r){await n({title:"Insufficient Credits",message:`You need ${r} credits to generate ${E} flashcards, but you only have ${b.credits_remaining} credits remaining.`,variant:"error"});return}if(await S({title:"Generate Flashcards",message:`Generate ${E} flashcards from ${l.length} document(s)?

This will cost ${r} credits.`,confirmText:"Generate",cancelText:"Cancel"})){J(!0);try{const t=await w({studySetId:s,documentIds:l,documentPageRanges:y,count:E,customPrompt:j.trim()||void 0,difficultyLevel:Y,contentLength:W,existingContent:d.map(a=>a.front)});c(t.flashcards),b&&ce.getState().updateUser({credits_remaining:t.creditsRemaining});try{const{useStudyStore:a}=await se(()=>import("./studyStore-ba57596c.js"),["assets/studyStore-ba57596c.js","assets/index-114510d6.js","assets/index-caeb64d5.css"]);await a.getState().refreshStudySetContent(s)}catch(a){console.warn("Failed to refresh study set cache:",a)}await n({title:"Success",message:`Generated ${t.flashcards.length} flashcards successfully!`,variant:"success"}),_([]),D(""),q(!1)}catch(t){await n({title:"Generation Error",message:t.message||"Failed to generate flashcards",variant:"error"})}finally{J(!1)}}};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-medium text-white",children:"Manage Flashcards"}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(I,{onClick:()=>f(!K),variant:"secondary",size:"sm",children:"➕ Add Flashcard"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-sm text-gray-400",children:"AI Mode"}),e.jsx("button",{onClick:()=>q(!k),className:`
                relative inline-flex h-6 w-11 items-center rounded-full transition-colors
                ${k?"bg-primary-500":"bg-gray-600"}
              `,children:e.jsx("span",{className:`
                  inline-block h-4 w-4 transform rounded-full bg-white transition-transform
                  ${k?"translate-x-6":"translate-x-1"}
                `})})]})]})]}),K&&e.jsxs("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600",children:[e.jsx("h4",{className:"text-md font-medium text-white mb-4",children:"Add New Flashcard"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Front (Question/Term)"}),e.jsx("textarea",{value:T.front,onChange:r=>V(g=>({...g,front:r.target.value})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Enter the front content..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Back (Answer/Definition)"}),e.jsx("textarea",{value:T.back,onChange:r=>V(g=>({...g,back:r.target.value})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Enter the back content..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Difficulty Level"}),e.jsxs("select",{value:T.difficulty_level,onChange:r=>V(g=>({...g,difficulty_level:parseInt(r.target.value)})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500",children:[e.jsx("option",{value:1,children:"1 - Very Easy"}),e.jsx("option",{value:2,children:"2 - Easy"}),e.jsx("option",{value:3,children:"3 - Medium"}),e.jsx("option",{value:4,children:"4 - Hard"}),e.jsx("option",{value:5,children:"5 - Very Hard"})]})]}),e.jsxs("div",{className:"flex space-x-3",children:[e.jsx(I,{onClick:fe,variant:"primary",children:"Add Flashcard"}),e.jsx(I,{onClick:()=>f(!1),variant:"secondary",children:"Cancel"})]})]})]}),H&&e.jsxs("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600",children:[e.jsx("h4",{className:"text-md font-medium text-white mb-4",children:"Edit Flashcard"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Front (Question/Term)"}),e.jsx("textarea",{value:F.front,onChange:r=>G(g=>({...g,front:r.target.value})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Enter the front content..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Back (Answer/Definition)"}),e.jsx("textarea",{value:F.back,onChange:r=>G(g=>({...g,back:r.target.value})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Enter the back content..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Difficulty Level"}),e.jsxs("select",{value:F.difficulty_level,onChange:r=>G(g=>({...g,difficulty_level:parseInt(r.target.value)})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500",children:[e.jsx("option",{value:1,children:"1 - Very Easy"}),e.jsx("option",{value:2,children:"2 - Easy"}),e.jsx("option",{value:3,children:"3 - Medium"}),e.jsx("option",{value:4,children:"4 - Hard"}),e.jsx("option",{value:5,children:"5 - Very Hard"})]})]}),e.jsxs("div",{className:"flex space-x-3",children:[e.jsx(I,{onClick:ve,variant:"primary",children:"Save Changes"}),e.jsx(I,{onClick:je,variant:"secondary",children:"Cancel"})]})]})]}),k&&e.jsxs("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600",children:[e.jsx("h4",{className:"text-md font-medium text-white mb-4",children:"AI Flashcard Generation"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Select Documents"}),e.jsx(Ie,{selectedDocuments:l,onSelectionChange:_,documentPageRanges:y,onPageRangeChange:re,maxSelection:5})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsx(Pe,{label:"Number of Flashcards",value:E,onChange:$,min:1,max:100,placeholder:"Enter number (1-100)",disabled:O}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Credit Cost"}),e.jsxs("div",{className:"px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-primary-400 font-medium",children:[le()," credits"]})]})]}),e.jsx($e,{value:Y,onChange:L}),e.jsx(Me,{value:W,onChange:U}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Custom Instructions (Optional)"}),e.jsx("textarea",{value:j,onChange:r=>D(r.target.value),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Add specific instructions for flashcard generation..."})]}),e.jsx(I,{onClick:we,disabled:l.length===0||O,className:"w-full",variant:"primary",children:O?"Generating...":`Generate ${E} Flashcards`})]})]}),e.jsx(De,{isGenerating:O,stage:O?"Generating flashcards with AI...":void 0,estimatedTime:Math.ceil(E/10)}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("h4",{className:"text-md font-medium text-white",children:["Current Flashcards (",d.length,")"]}),d.length>0&&e.jsx("div",{className:"flex items-center space-x-3",children:e.jsxs("label",{className:"flex items-center space-x-2 text-sm text-gray-300",children:[e.jsx("input",{type:"checkbox",checked:N,onChange:r=>ge(r.target.checked),className:"rounded border-gray-600 bg-gray-700 text-primary-500 focus:ring-primary-500 focus:ring-offset-gray-800"}),e.jsx("span",{children:"Select All"})]})})]}),e.jsx(Qe,{selectedCount:M.length,totalCount:d.length,onDeleteSelected:pe,onClearSelection:ae,isLoading:Q}),d.length>0&&e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("p",{className:"text-sm text-gray-400",children:"💡 Click on any flashcard to reveal/hide its answer"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("button",{onClick:me,className:"flex items-center space-x-1 px-3 py-1 bg-background-tertiary border border-gray-600 rounded-md text-gray-300 hover:text-white hover:border-primary-500 transition-colors text-sm",children:[e.jsx(Ce,{className:"w-4 h-4"}),e.jsx("span",{children:"Show All"})]}),e.jsxs("button",{onClick:he,className:"flex items-center space-x-1 px-3 py-1 bg-background-tertiary border border-gray-600 rounded-md text-gray-300 hover:text-white hover:border-primary-500 transition-colors text-sm",children:[e.jsx(Ee,{className:"w-4 h-4"}),e.jsx("span",{children:"Hide All"})]})]})]}),d.length===0?e.jsx("div",{className:"text-center py-8 text-gray-400",children:"No flashcards yet. Add some manually or generate them with AI."}):e.jsx("div",{className:"space-y-2",children:d.map(r=>e.jsx("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600 hover:border-gray-500 transition-colors",children:e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx("div",{className:"flex-shrink-0 pt-1",children:e.jsx("input",{type:"checkbox",checked:M.includes(r.id),onChange:g=>xe(r.id,g.target.checked),className:"rounded border-gray-600 bg-gray-700 text-primary-500 focus:ring-primary-500 focus:ring-offset-gray-800"})}),e.jsx("div",{className:"flex-1 min-w-0",children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsxs("div",{className:"flex-1 min-w-0 cursor-pointer group",onClick:()=>ue(r.id),title:X.has(r.id)?"Click to hide answer":"Click to reveal answer",children:[e.jsxs("div",{className:"mb-2",children:[e.jsx("span",{className:"text-xs text-gray-400 uppercase tracking-wide",children:"Front"}),e.jsx("p",{className:"text-white font-medium group-hover:text-primary-300 transition-colors",children:r.front})]}),X.has(r.id)?e.jsxs("div",{className:"mb-2",children:[e.jsx("span",{className:"text-xs text-gray-400 uppercase tracking-wide",children:"Back"}),e.jsx("p",{className:"text-gray-300 group-hover:text-gray-200 transition-colors",children:r.back})]}):e.jsx("div",{className:"mb-2",children:e.jsx("p",{className:"text-gray-500 italic text-sm group-hover:text-primary-400 transition-colors",children:"Click to reveal answer..."})}),e.jsxs("div",{className:"flex items-center space-x-4 text-xs text-gray-400",children:[r.is_ai_generated&&e.jsx("span",{className:"bg-primary-500/20 text-primary-400 px-2 py-1 rounded",children:"AI Generated"}),r.difficulty_level&&e.jsxs("span",{children:["Difficulty:"," ",typeof r.difficulty_level=="string"?de(r.difficulty_level):de(Fe(r.difficulty_level))]}),e.jsxs("span",{children:["Reviewed: ",r.times_reviewed||0," times"]})]})]}),e.jsxs("div",{className:"flex items-center space-x-2 ml-4",children:[e.jsx("button",{onClick:()=>be(r),className:"text-gray-400 hover:text-white p-1",title:"Edit flashcard",children:"✏️"}),e.jsx("button",{onClick:()=>ne(r),className:"text-gray-400 hover:text-red-400 p-1",title:"Delete flashcard",children:"🗑️"})]})]})})]})},r.id))})]})]})},Ke=({studySetId:s,questions:d,onQuestionAdded:o,onQuestionUpdated:m,onQuestionDeleted:i,onQuestionsGenerated:c})=>{const{alert:n,confirm:S}=Ne(),{user:b}=ce(),[w,C]=u.useState(!0),[v,k]=u.useState([]),[q,l]=u.useState({}),[_,y]=u.useState(25),[A,E]=u.useState(""),[$,j]=u.useState(Ae.MEDIUM),[D,Y]=u.useState(B.MEDIUM),[L,W]=u.useState(["multiple_choice","select_all","true_false","short_answer"]),[U,O]=u.useState(!1),[J,K]=u.useState(!1),[f,T]=u.useState({question_text:"",question_type:"multiple_choice",options:["","","",""],correct_answers:[],explanation:"",difficulty_level:3}),[V,H]=u.useState(null),[z,F]=u.useState([]),[G,M]=u.useState(!1),[p,N]=u.useState(!1),[h,Q]=u.useState({question_text:"",question_type:"multiple_choice",options:["","","",""],correct_answers:[],explanation:"",difficulty_level:3}),[ee,X]=u.useState(new Set),te=t=>{X(a=>{const x=new Set(a);return x.has(t)?x.delete(t):x.add(t),x})},ue=()=>{const t=new Set(d.map(a=>a.id));X(t)},me=(t,a)=>{a?F(x=>[...x,t]):(F(x=>x.filter(P=>P!==t)),M(!1))},he=t=>{M(t),F(t?d.map(a=>a.id):[])},re=()=>{F([]),M(!1)},xe=async()=>{try{await Promise.all(z.map(t=>fetch(`/api/quiz-questions/${t}`,{method:"DELETE",headers:{Authorization:`Bearer ${localStorage.getItem("auth_token")}`}}))),z.forEach(t=>i(t)),re()}catch(t){console.error("Bulk deletion failed",t),await n({title:"Error",message:"Failed to delete selected questions.",variant:"error"})}finally{N(!1)}},ge=async()=>{z.length===0||!await S({title:"Delete Questions",message:`Are you sure you want to delete ${z.length} question$${z.length!==1?"s":""}?`,variant:"danger",confirmText:"Delete",cancelText:"Cancel"})||(N(!0),await xe())},ae=()=>{X(new Set)},pe=(t,a)=>{l(x=>({...x,[t]:a}))},ye=async()=>{if(!f.question_text.trim()){await n({title:"Validation Error",message:"Question text is required.",variant:"error"});return}if(f.correct_answers.length===0){await n({title:"Validation Error",message:"At least one correct answer is required.",variant:"error"});return}if((f.question_type==="multiple_choice"||f.question_type==="select_all")&&f.options.filter(a=>a.trim().length>0).length<2){await n({title:"Validation Error",message:"Multiple choice and select all questions require at least 2 options.",variant:"error"});return}try{const t=await fetch(`/api/quiz-questions/study-set/${s}`,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("auth_token")}`},body:JSON.stringify({question_text:f.question_text.trim(),question_type:f.question_type,options:f.question_type==="multiple_choice"||f.question_type==="select_all"?f.options.filter(x=>x.trim().length>0):null,correct_answers:f.correct_answers,explanation:f.explanation.trim()||null,difficulty_level:f.difficulty_level})});if(!t.ok)throw new Error("Failed to create question");const a=await t.json();o(a.data),T({question_text:"",question_type:"multiple_choice",options:["","","",""],correct_answers:[],explanation:"",difficulty_level:3}),K(!1),await n({title:"Success",message:"Question added successfully!",variant:"success"})}catch(t){await n({title:"Error",message:t.message||"Failed to add question",variant:"error"})}},fe=async t=>{if(await S({title:"Delete Question",message:`Are you sure you want to delete this question?

${t.question_text.substring(0,100)}${t.question_text.length>100?"...":""}`,variant:"danger",confirmText:"Delete",cancelText:"Cancel"}))try{if(!(await fetch(`/api/quiz-questions/${t.id}`,{method:"DELETE",headers:{Authorization:`Bearer ${localStorage.getItem("auth_token")}`}})).ok)throw new Error("Failed to delete question");i(t.id),await n({title:"Success",message:"Question deleted successfully!",variant:"success"})}catch(x){await n({title:"Error",message:x.message||"Failed to delete question",variant:"error"})}},be=t=>{H(t),Q({question_text:t.question_text,question_type:t.question_type,options:t.options||["","","",""],correct_answers:t.correct_answers,explanation:t.explanation||"",difficulty_level:typeof t.difficulty_level=="string"?Te(t.difficulty_level):t.difficulty_level||3})},ve=async()=>{if(V){if(!h.question_text.trim()){await n({title:"Validation Error",message:"Question text is required.",variant:"error"});return}if(h.correct_answers.length===0){await n({title:"Validation Error",message:"At least one correct answer is required.",variant:"error"});return}if((h.question_type==="multiple_choice"||h.question_type==="select_all")&&h.options.filter(a=>a.trim().length>0).length<2){await n({title:"Validation Error",message:"Multiple choice and select all questions require at least 2 options.",variant:"error"});return}try{const t=await fetch(`/api/quiz-questions/${V.id}`,{method:"PUT",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("auth_token")}`},body:JSON.stringify({question_text:h.question_text.trim(),question_type:h.question_type,options:h.question_type==="multiple_choice"||h.question_type==="select_all"?h.options.filter(x=>x.trim().length>0):null,correct_answers:h.correct_answers,explanation:h.explanation.trim()||null,difficulty_level:h.difficulty_level})});if(!t.ok)throw new Error("Failed to update question");const a=await t.json();m(a.data),H(null),await n({title:"Success",message:"Question updated successfully!",variant:"success"})}catch(t){await n({title:"Error",message:t.message||"Failed to update question",variant:"error"})}}},je=()=>{H(null),Q({question_text:"",question_type:"multiple_choice",options:["","","",""],correct_answers:[],explanation:"",difficulty_level:3})},ne=()=>Math.ceil(_/5),ie=async()=>{if(v.length===0){await n({title:"No Documents Selected",message:"Please select at least one document to generate questions from.",variant:"warning"});return}if(L.length===0){await n({title:"No Question Types Selected",message:"Please select at least one question type to generate.",variant:"warning"});return}const t=ne();if(b&&b.credits_remaining<t){await n({title:"Insufficient Credits",message:`You need ${t} credits to generate ${_} questions, but you only have ${b.credits_remaining} credits remaining.`,variant:"warning"});return}if(await S({title:"Generate Questions",message:`Generate ${_} questions for ${t} credits?`,confirmText:"Generate",cancelText:"Cancel"})){O(!0);try{const x=await fetch("/api/ai/generate-more-quiz-questions",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("auth_token")}`},body:JSON.stringify({studySetId:s,documentIds:v,documentPageRanges:q,count:_,customPrompt:A.trim()||void 0,difficultyLevel:$,contentLength:D,questionTypes:L,existingContent:d.map(R=>R.question_text)})});if(!x.ok)throw new Error("Failed to generate questions");const P=await x.json();c(P.data.questions),b&&ce.getState().updateUser({credits_remaining:P.data.creditsRemaining});try{const{useStudyStore:R}=await se(()=>import("./studyStore-ba57596c.js"),["assets/studyStore-ba57596c.js","assets/index-114510d6.js","assets/index-caeb64d5.css"]);await R.getState().refreshStudySetContent(s)}catch(R){console.warn("Failed to refresh study set cache:",R)}await n({title:"Success",message:`Generated ${P.data.questions.length} questions successfully!`,variant:"success"}),k([]),E(""),C(!1)}catch(x){await n({title:"Error",message:x.message||"Failed to generate questions",variant:"error"})}finally{O(!1)}}},le=t=>{T(a=>({...a,question_type:t,options:t==="multiple_choice"||t==="select_all"?["","","",""]:[],correct_answers:[]}))},we=(t,a)=>{T(x=>({...x,options:x.options.map((P,R)=>R===t?a:P)}))},r=t=>{T(a=>{const x=a.correct_answers.includes(t);return a.question_type==="multiple_choice"?{...a,correct_answers:x?[]:[t]}:{...a,correct_answers:x?a.correct_answers.filter(P=>P!==t):[...a.correct_answers,t]}})},g=t=>{W(a=>a.includes(t)?a.filter(x=>x!==t):[...a,t])};return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row gap-4 items-start sm:items-center justify-between",children:[e.jsx("h3",{className:"text-lg font-semibold text-white",children:"Question Management"}),e.jsxs("div",{className:"flex flex-wrap gap-2",children:[e.jsx(I,{onClick:()=>K(!J),variant:"secondary",size:"sm",children:J?"Cancel":"Add Question"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-sm text-gray-300",children:"AI Mode"}),e.jsx("button",{onClick:()=>C(!w),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${w?"bg-primary-500":"bg-gray-600"}`,children:e.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${w?"translate-x-6":"translate-x-1"}`})})]})]})]}),J&&e.jsxs("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600",children:[e.jsx("h4",{className:"text-md font-medium text-white mb-4",children:"Add New Question"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Question Text"}),e.jsx("textarea",{value:f.question_text,onChange:t=>T(a=>({...a,question_text:t.target.value})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Enter your question..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Question Type"}),e.jsxs("select",{value:f.question_type,onChange:t=>le(t.target.value),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500",children:[e.jsx("option",{value:"multiple_choice",children:"Multiple Choice"}),e.jsx("option",{value:"select_all",children:"Select All That Apply"}),e.jsx("option",{value:"true_false",children:"True/False"}),e.jsx("option",{value:"short_answer",children:"Short Answer"})]})]}),(f.question_type==="multiple_choice"||f.question_type==="select_all")&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Answer Options"}),e.jsx("div",{className:"space-y-2",children:f.options.map((t,a)=>e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("button",{type:"button",onClick:()=>r(t),className:`flex-shrink-0 w-5 h-5 border-2 ${f.question_type==="multiple_choice"?"rounded-full":"rounded"} ${f.correct_answers.includes(t)?"bg-primary-500 border-primary-500":"border-gray-400"} flex items-center justify-center`,disabled:!t.trim(),children:f.correct_answers.includes(t)&&e.jsx("span",{className:"text-white text-xs",children:"✓"})}),e.jsx("input",{type:"text",value:t,onChange:x=>we(a,x.target.value),className:"flex-1 px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",placeholder:`Option ${a+1}`})]},a))}),e.jsx("p",{className:"text-xs text-gray-400 mt-1",children:f.question_type==="multiple_choice"?"Click the circle to mark the correct answer":"Click the squares to mark all correct answers"})]}),f.question_type==="true_false"&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Correct Answer"}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx("button",{type:"button",onClick:()=>T(t=>({...t,correct_answers:["True"]})),className:`px-4 py-2 rounded-lg border ${f.correct_answers.includes("True")?"bg-primary-500 border-primary-500 text-white":"border-gray-600 text-gray-300 hover:border-gray-500"}`,children:"True"}),e.jsx("button",{type:"button",onClick:()=>T(t=>({...t,correct_answers:["False"]})),className:`px-4 py-2 rounded-lg border ${f.correct_answers.includes("False")?"bg-primary-500 border-primary-500 text-white":"border-gray-600 text-gray-300 hover:border-gray-500"}`,children:"False"})]})]}),f.question_type==="short_answer"&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Acceptable Answers (one per line)"}),e.jsx("textarea",{value:f.correct_answers.join(`
`),onChange:t=>T(a=>({...a,correct_answers:t.target.value.split(`
`).filter(x=>x.trim())})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Enter acceptable answers, one per line..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Explanation (Optional)"}),e.jsx("textarea",{value:f.explanation,onChange:t=>T(a=>({...a,explanation:t.target.value})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:2,placeholder:"Explain the correct answer..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Difficulty Level"}),e.jsxs("select",{value:f.difficulty_level,onChange:t=>T(a=>({...a,difficulty_level:parseInt(t.target.value)})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500",children:[e.jsx("option",{value:1,children:"1 - Very Easy"}),e.jsx("option",{value:2,children:"2 - Easy"}),e.jsx("option",{value:3,children:"3 - Medium"}),e.jsx("option",{value:4,children:"4 - Hard"}),e.jsx("option",{value:5,children:"5 - Very Hard"})]})]}),e.jsxs("div",{className:"flex space-x-3",children:[e.jsx(I,{onClick:ye,variant:"primary",children:"Add Question"}),e.jsx(I,{onClick:()=>K(!1),variant:"secondary",children:"Cancel"})]})]})]}),V&&e.jsxs("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600",children:[e.jsx("h4",{className:"text-md font-medium text-white mb-4",children:"Edit Question"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Question Text"}),e.jsx("textarea",{value:h.question_text,onChange:t=>Q(a=>({...a,question_text:t.target.value})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Enter your question..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Question Type"}),e.jsxs("select",{value:h.question_type,onChange:t=>Q(a=>({...a,question_type:t.target.value,options:t.target.value==="multiple_choice"||t.target.value==="select_all"?["","","",""]:[],correct_answers:[]})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500",children:[e.jsx("option",{value:"multiple_choice",children:"Multiple Choice"}),e.jsx("option",{value:"select_all",children:"Select All That Apply"}),e.jsx("option",{value:"true_false",children:"True/False"}),e.jsx("option",{value:"short_answer",children:"Short Answer"})]})]}),(h.question_type==="multiple_choice"||h.question_type==="select_all")&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Answer Options"}),e.jsx("div",{className:"space-y-2",children:h.options.map((t,a)=>e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("button",{type:"button",onClick:()=>{const x=h.correct_answers.includes(t);h.question_type==="multiple_choice"?Q(P=>({...P,correct_answers:x?[]:[t]})):Q(P=>({...P,correct_answers:x?P.correct_answers.filter(R=>R!==t):[...P.correct_answers,t]}))},className:`flex-shrink-0 w-5 h-5 border-2 ${h.question_type==="multiple_choice"?"rounded-full":"rounded"} ${h.correct_answers.includes(t)?"bg-primary-500 border-primary-500":"border-gray-400"} flex items-center justify-center`,disabled:!t.trim(),children:h.correct_answers.includes(t)&&e.jsx("span",{className:"text-white text-xs",children:"✓"})}),e.jsx("input",{type:"text",value:t,onChange:x=>Q(P=>({...P,options:P.options.map((R,_e)=>_e===a?x.target.value:R)})),className:"flex-1 px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",placeholder:`Option ${a+1}`})]},a))}),e.jsx("p",{className:"text-xs text-gray-400 mt-1",children:h.question_type==="multiple_choice"?"Click the circle to mark the correct answer":"Click the squares to mark all correct answers"})]}),h.question_type==="true_false"&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Correct Answer"}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx("button",{type:"button",onClick:()=>Q(t=>({...t,correct_answers:["True"]})),className:`px-4 py-2 rounded-lg border ${h.correct_answers.includes("True")?"bg-primary-500 border-primary-500 text-white":"border-gray-600 text-gray-300 hover:border-gray-500"}`,children:"True"}),e.jsx("button",{type:"button",onClick:()=>Q(t=>({...t,correct_answers:["False"]})),className:`px-4 py-2 rounded-lg border ${h.correct_answers.includes("False")?"bg-primary-500 border-primary-500 text-white":"border-gray-600 text-gray-300 hover:border-gray-500"}`,children:"False"})]})]}),h.question_type==="short_answer"&&e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Acceptable Answers (one per line)"}),e.jsx("textarea",{value:h.correct_answers.join(`
`),onChange:t=>Q(a=>({...a,correct_answers:t.target.value.split(`
`).filter(x=>x.trim())})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Enter acceptable answers, one per line..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Explanation (Optional)"}),e.jsx("textarea",{value:h.explanation,onChange:t=>Q(a=>({...a,explanation:t.target.value})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:2,placeholder:"Explain the correct answer..."})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Difficulty Level"}),e.jsxs("select",{value:h.difficulty_level,onChange:t=>Q(a=>({...a,difficulty_level:parseInt(t.target.value)})),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white focus:outline-none focus:border-primary-500",children:[e.jsx("option",{value:1,children:"1 - Very Easy"}),e.jsx("option",{value:2,children:"2 - Easy"}),e.jsx("option",{value:3,children:"3 - Medium"}),e.jsx("option",{value:4,children:"4 - Hard"}),e.jsx("option",{value:5,children:"5 - Very Hard"})]})]}),e.jsxs("div",{className:"flex space-x-3",children:[e.jsx(I,{onClick:ve,variant:"primary",children:"Save Changes"}),e.jsx(I,{onClick:je,variant:"secondary",children:"Cancel"})]})]})]}),w&&e.jsxs("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600",children:[e.jsx("h4",{className:"text-md font-medium text-white mb-4",children:"AI Question Generation"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Select Documents"}),e.jsx(Ie,{selectedDocuments:v,onSelectionChange:k,documentPageRanges:q,onPageRangeChange:pe,maxSelection:5})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsx(Pe,{label:"Number of Questions",value:_,onChange:y,min:1,max:100,placeholder:"Enter number (1-100)",disabled:U}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Credit Cost"}),e.jsxs("div",{className:"px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-primary-400 font-medium",children:[ne()," credits"]})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Question Types"}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-2",children:[e.jsx(Z,{label:"Multiple Choice",checked:L.includes("multiple_choice"),onChange:()=>g("multiple_choice"),size:"sm"}),e.jsx(Z,{label:"Select All",checked:L.includes("select_all"),onChange:()=>g("select_all"),size:"sm"}),e.jsx(Z,{label:"True False",checked:L.includes("true_false"),onChange:()=>g("true_false"),size:"sm"}),e.jsx(Z,{label:"Short Answer",checked:L.includes("short_answer"),onChange:()=>g("short_answer"),size:"sm"})]})]}),e.jsx($e,{value:$,onChange:j}),e.jsx(Me,{value:D,onChange:Y}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Custom Instructions (Optional)"}),e.jsx("textarea",{value:A,onChange:t=>E(t.target.value),className:"w-full px-3 py-2 bg-background-primary border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:border-primary-500",rows:3,placeholder:"Add specific instructions for question generation..."})]}),e.jsx(I,{onClick:ie,disabled:v.length===0||L.length===0||U,className:"w-full",variant:"primary",children:U?"Generating...":`Generate ${_} Questions`})]})]}),e.jsx(De,{isGenerating:U,stage:U?"Generating quiz questions with AI...":void 0,estimatedTime:Math.ceil(_/8)}),e.jsx(Qe,{selectedCount:z.length,totalCount:d.length,onDeleteSelected:ge,onClearSelection:re,isLoading:p,className:"mb-4",itemType:"question"}),e.jsxs("div",{className:"space-y-3",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(Z,{checked:G,onChange:t=>he(t)}),e.jsxs("h4",{className:"text-md font-medium text-white",children:["Current Questions (",d.length,")"]})]}),d.length>0&&e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("p",{className:"text-sm text-gray-400",children:"💡 Click on any question to reveal/hide its answer"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("button",{onClick:ue,className:"flex items-center space-x-1 px-3 py-1 bg-background-tertiary border border-gray-600 rounded-md text-gray-300 hover:text-white hover:border-primary-500 transition-colors text-sm",children:[e.jsx(Ce,{className:"w-4 h-4"}),e.jsx("span",{children:"Show All"})]}),e.jsxs("button",{onClick:ae,className:"flex items-center space-x-1 px-3 py-1 bg-background-tertiary border border-gray-600 rounded-md text-gray-300 hover:text-white hover:border-primary-500 transition-colors text-sm",children:[e.jsx(Ee,{className:"w-4 h-4"}),e.jsx("span",{children:"Hide All"})]})]})]})]}),d.length===0?e.jsx("div",{className:"text-center py-8 text-gray-400",children:"No questions yet. Add some manually or generate them with AI."}):e.jsx("div",{className:"space-y-2",children:d.map(t=>e.jsx("div",{className:"bg-background-secondary rounded-lg p-4 border border-gray-600 hover:border-gray-500 transition-colors",children:e.jsxs("div",{className:"flex items-start justify-between",children:[e.jsx(Z,{checked:z.includes(t.id),onChange:a=>me(t.id,a),className:"mr-3 mt-1"}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsxs("div",{className:"cursor-pointer group",onClick:()=>te(t.id),title:ee.has(t.id)?"Click to hide answer":"Click to reveal answer",children:[e.jsxs("div",{className:"mb-2",children:[e.jsx("span",{className:"text-xs text-gray-400 uppercase tracking-wide",children:"Question"}),e.jsx("p",{className:"text-white font-medium group-hover:text-primary-300 transition-colors",children:t.question_text})]}),e.jsxs("div",{className:"mb-2",children:[e.jsx("span",{className:"text-xs text-gray-400 uppercase tracking-wide",children:"Type"}),e.jsx("p",{className:"text-gray-300 capitalize group-hover:text-gray-200 transition-colors",children:t.question_type.replace("_"," ")})]}),ee.has(t.id)?e.jsxs(e.Fragment,{children:[t.question_type==="true_false"&&e.jsxs("div",{className:"mb-2",children:[e.jsx("span",{className:"text-xs text-gray-400 uppercase tracking-wide",children:"Correct Answer"}),e.jsx("p",{className:"text-green-400 text-sm font-medium",children:t.correct_answers[0]})]}),t.options&&e.jsxs("div",{className:"mb-2",children:[e.jsx("span",{className:"text-xs text-gray-400 uppercase tracking-wide",children:"Options"}),e.jsx("ul",{className:"text-gray-300 text-sm group-hover:text-gray-200 transition-colors",children:t.options.map((a,x)=>e.jsxs("li",{className:`${t.correct_answers.includes(a)?"text-green-400 font-medium":""}`,children:[x+1,". ",a]},`${t.id}-option-${x}`))})]}),t.explanation&&e.jsxs("div",{className:"mb-2",children:[e.jsx("span",{className:"text-xs text-gray-400 uppercase tracking-wide",children:"Explanation"}),e.jsx("p",{className:"text-gray-300 text-sm group-hover:text-gray-200 transition-colors",children:t.explanation})]})]}):e.jsx("div",{className:"mb-2",children:e.jsx("p",{className:"text-gray-500 italic text-sm group-hover:text-primary-400 transition-colors",children:"Click to reveal answer details..."})})]}),e.jsxs("div",{className:"flex items-center space-x-4 text-xs text-gray-400",children:[t.is_ai_generated&&e.jsx("span",{className:"bg-primary-500/20 text-primary-400 px-2 py-1 rounded",children:"AI Generated"}),t.difficulty_level&&e.jsxs("span",{children:["Difficulty:"," ",typeof t.difficulty_level=="string"?de(t.difficulty_level):de(Fe(t.difficulty_level))]}),e.jsxs("span",{children:["Attempted: ",t.times_attempted||0," times"]}),e.jsxs("span",{children:["Correct: ",t.times_correct||0," times"]})]})]}),e.jsxs("div",{className:"flex items-center space-x-2 ml-4",children:[e.jsx("button",{onClick:()=>be(t),className:"text-gray-400 hover:text-primary-400 p-1",title:"Edit question",children:"✏️"}),e.jsx("button",{onClick:()=>fe(t),className:"text-gray-400 hover:text-red-400 p-1",title:"Delete question",children:"🗑️"})]})]})},t.id))})]})]})},Ye=({enabled:s,onChange:d,disabled:o=!1,className:m="",label:i="Shuffle Cards",description:c="Randomize the order of flashcards during study sessions"})=>{const n=()=>{o||d(!s)},S=b=>{(b.key===" "||b.key==="Enter")&&(b.preventDefault(),n())};return e.jsxs("div",{className:`flex items-center justify-between ${m}`,children:[e.jsx("div",{className:"flex-1",children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("span",{className:"text-sm font-medium text-white",children:i}),c&&e.jsx("span",{className:"text-xs text-gray-400",children:c})]})}),e.jsx("button",{type:"button",role:"switch","aria-checked":s,"aria-label":`${s?"Disable":"Enable"} ${i.toLowerCase()}`,onClick:n,onKeyDown:S,disabled:o,className:`
          relative inline-flex h-6 w-11 items-center rounded-full transition-colors duration-200 ease-in-out
          focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2 focus:ring-offset-gray-800
          ${o?"opacity-50 cursor-not-allowed bg-gray-600":s?"bg-primary-500 hover:bg-primary-600":"bg-gray-600 hover:bg-gray-500"}
        `,children:e.jsx("span",{className:`
            inline-block h-4 w-4 transform rounded-full bg-white transition-transform duration-200 ease-in-out
            ${s?"translate-x-6":"translate-x-1"}
          `})})]})},st=()=>{const{id:s}=Ge(),d=Le(),{studySetContent:o,isLoading:m,error:i,fetchStudySetContent:c}=ke(),{alert:n,confirm:S,prompt:b}=Ne(),{settings:w,updateSettings:C}=qe(),[v,k]=u.useState(null),[q,l]=u.useState("study"),[_,y]=u.useState("flashcards"),[A,E]=u.useState([]),[$,j]=u.useState([]),[D,Y]=u.useState("");u.useEffect(()=>{s&&c(s).catch(console.error)},[s,c]),u.useEffect(()=>{o!=null&&o.studySet&&(Y(o.studySet.name),E(o.flashcards||[]),j(o.questions||[]))},[o]);const L=async()=>{if(!(!s||!v))try{const p=(w==null?void 0:w.shuffle_flashcards)||!1;await ke.getState().startStudySession(s,v,p),d(`/study/${s}/${v}`)}catch(p){await n({title:"Error",message:p.message||"Failed to start study session",variant:"error"})}},W=async()=>{if(!s||!(o!=null&&o.studySet))return;const p=await b({title:"Rename Study Set",message:"Enter a new name for this study set:",defaultValue:o.studySet.name});if(!(p===null||p.trim()===o.studySet.name)){if(!p.trim()){await n({title:"Invalid Name",message:"Study set name cannot be empty.",variant:"error"});return}try{if(!(await fetch(`/api/study-sets/${s}`,{method:"PUT",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("auth_token")}`},body:JSON.stringify({name:p.trim()})})).ok)throw new Error("Failed to rename study set");Y(p.trim()),await n({title:"Success",message:"Study set renamed successfully!",variant:"success"}),await c(s)}catch(N){await n({title:"Error",message:N.message||"Failed to rename study set",variant:"error"})}}},U=async()=>{if(!(!s||!(o!=null&&o.studySet)||!await S({title:"Delete Study Set",message:`Are you sure you want to delete "${o.studySet.name}"?

This action cannot be undone and will delete all flashcards and quiz questions in this set.`,variant:"danger",confirmText:"Delete Study Set",cancelText:"Cancel"})))try{if(!(await fetch(`/api/study-sets/${s}`,{method:"DELETE",headers:{Authorization:`Bearer ${localStorage.getItem("auth_token")}`}})).ok)throw new Error("Failed to delete study set");await n({title:"Success",message:"Study set deleted successfully!",variant:"success"}),d("/dashboard")}catch(N){await n({title:"Error",message:N.message||"Failed to delete study set",variant:"error"})}},O=p=>{E(N=>[...N,p])},J=p=>{E(N=>N.map(h=>h.id===p.id?p:h))},K=p=>{E(N=>N.filter(h=>h.id!==p))},f=p=>{E(N=>[...N,...p])},T=p=>{j(N=>[...N,p])},V=p=>{j(N=>N.map(h=>h.id===p.id?p:h))},H=p=>{j(N=>N.filter(h=>h.id!==p))},z=p=>{j(N=>[...N,...p])};if(m)return e.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:e.jsxs("div",{className:"flex items-center justify-center py-12",children:[e.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-primary-500"}),e.jsx("span",{className:"ml-3 text-gray-400",children:"Loading study set..."})]})});if(i||!(o!=null&&o.studySet))return e.jsx("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:e.jsxs("div",{className:"text-center py-12",children:[e.jsx("div",{className:"text-red-400 mb-4",children:i||"Study set not found"}),e.jsx(I,{onClick:()=>d("/dashboard"),variant:"secondary",children:"Back to Study Sets"})]})});const{studySet:F}=o,G=A&&A.length>0,M=$&&$.length>0;return e.jsxs("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("button",{onClick:()=>d("/dashboard"),className:"text-gray-400 hover:text-white mb-4 flex items-center",children:"← Back to Study Sets"}),e.jsxs("div",{className:"flex items-center justify-between mb-2",children:[e.jsx("h1",{className:"text-3xl font-bold text-white",children:D}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(I,{onClick:W,variant:"secondary",size:"sm",children:"✏️ Rename"}),e.jsx(I,{onClick:U,variant:"danger",size:"sm",children:"🗑️ Delete"})]})]}),e.jsxs("div",{className:"flex items-center space-x-4 text-sm text-gray-400",children:[e.jsx("span",{className:"capitalize",children:F.type}),F.is_ai_generated&&e.jsx("span",{className:"bg-primary-500/20 text-primary-400 px-2 py-1 rounded",children:"AI Generated"}),e.jsxs("span",{children:["Created ",new Date(F.created_at).toLocaleDateString()]})]})]}),e.jsx("div",{className:"mb-6",children:e.jsx("div",{className:"border-b border-gray-600",children:e.jsxs("nav",{className:"-mb-px flex space-x-8",children:[e.jsx("button",{onClick:()=>l("study"),className:`
                py-2 px-1 border-b-2 font-medium text-sm transition-colors
                ${q==="study"?"border-primary-500 text-primary-400":"border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300"}
              `,children:"📚 Study Mode"}),e.jsx("button",{onClick:()=>l("manage"),className:`
                py-2 px-1 border-b-2 font-medium text-sm transition-colors
                ${q==="manage"?"border-primary-500 text-primary-400":"border-transparent text-gray-400 hover:text-gray-300 hover:border-gray-300"}
              `,children:"⚙️ Manage Content"})]})})}),q==="study"&&e.jsx(e.Fragment,{children:e.jsxs("div",{className:"bg-background-secondary rounded-lg p-6 mb-6",children:[e.jsx("h2",{className:"text-xl font-semibold text-white mb-4",children:"Choose Study Mode"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:[G&&e.jsx("div",{className:`
                    p-4 rounded-lg border-2 cursor-pointer transition-all
                    ${v==="flashcards"?"border-primary-500 bg-primary-500/10":"border-gray-600 hover:border-gray-500"}
                  `,onClick:()=>k("flashcards"),children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"text-2xl",children:"🃏"}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-white",children:"Flashcard Review"}),e.jsxs("p",{className:"text-sm text-gray-400",children:[A==null?void 0:A.length," flashcards • Interactive review"]})]})]})}),M&&e.jsx("div",{className:`
                    p-4 rounded-lg border-2 cursor-pointer transition-all
                    ${v==="quiz"?"border-primary-500 bg-primary-500/10":"border-gray-600 hover:border-gray-500"}
                  `,onClick:()=>k("quiz"),children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"text-2xl",children:"📝"}),e.jsxs("div",{children:[e.jsx("h3",{className:"font-medium text-white",children:"Quiz Practice"}),e.jsxs("p",{className:"text-sm text-gray-400",children:[$==null?void 0:$.length," questions • Test your knowledge"]})]})]})})]}),v&&w&&e.jsx("div",{className:"mb-6 p-4 bg-background-tertiary rounded-lg border border-gray-600",children:e.jsx(Ye,{enabled:w.shuffle_flashcards,onChange:async p=>{try{await C({shuffle_flashcards:p})}catch(N){console.error("Failed to update shuffle setting:",N)}},label:"Shuffle Cards",description:"Randomize the order of flashcards during study sessions"})}),e.jsx(I,{onClick:L,disabled:!v,className:"w-full",size:"lg",children:v?`Start ${v==="flashcards"?"Flashcard Review":"Quiz Practice"}`:"Select a study mode"})]})}),q==="manage"&&s&&e.jsxs("div",{className:"bg-background-secondary rounded-lg p-6 mb-6",children:[e.jsxs("div",{className:"flex space-x-1 mb-6",children:[e.jsxs("button",{onClick:()=>y("flashcards"),className:`
                py-2 px-4 rounded-lg font-medium text-sm transition-colors
                ${_==="flashcards"?"bg-primary-500 text-white":"bg-background-primary text-gray-400 hover:text-gray-300"}
              `,children:["📚 Flashcards (",A.length,")"]}),e.jsxs("button",{onClick:()=>y("quiz"),className:`
                py-2 px-4 rounded-lg font-medium text-sm transition-colors
                ${_==="quiz"?"bg-primary-500 text-white":"bg-background-primary text-gray-400 hover:text-gray-300"}
              `,children:["❓ Quiz Questions (",$.length,")"]})]}),_==="flashcards"&&e.jsx(Je,{studySetId:s,flashcards:A,onFlashcardAdded:O,onFlashcardUpdated:J,onFlashcardDeleted:K,onFlashcardsGenerated:f}),_==="quiz"&&e.jsx(Ke,{studySetId:s,questions:$,onQuestionAdded:T,onQuestionUpdated:V,onQuestionDeleted:H,onQuestionsGenerated:z})]}),e.jsxs("div",{className:"bg-background-secondary rounded-lg p-6",children:[e.jsx("h3",{className:"text-lg font-medium text-white mb-4",children:"Study Set Details"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"text-sm font-medium text-gray-300 mb-2",children:"Content"}),e.jsxs("div",{className:"space-y-1 text-sm text-gray-400",children:[G&&e.jsxs("div",{children:[A.length," flashcards"]}),M&&e.jsxs("div",{children:[$==null?void 0:$.length," quiz questions"]}),!G&&!M&&e.jsx("div",{className:"text-gray-500",children:"No content yet"})]})]}),F.source_documents&&F.source_documents.length>0&&e.jsxs("div",{children:[e.jsx("h4",{className:"text-sm font-medium text-gray-300 mb-2",children:"Source Documents"}),e.jsx("div",{className:"space-y-1 text-sm text-gray-400",children:F.source_documents.map((p,N)=>e.jsx("div",{children:p.filename},N))})]}),F.custom_prompt&&e.jsxs("div",{className:"md:col-span-2",children:[e.jsx("h4",{className:"text-sm font-medium text-gray-300 mb-2",children:"Custom Instructions"}),e.jsx("p",{className:"text-sm text-gray-400",children:F.custom_prompt})]})]})]})]})};export{st as StudySetPage};
