import{c as $,j as e,n as w,F as k,i as A,o as R,m as L,r as j,G as D,B as E,A as F,J,K as Y,b as M,L as V,q as X,M as W,k as q,t as K,D as Z,E as Q,s as G,N as ee}from"./index-9035c059.js";const O=$((s,l)=>({balance:0,transactions:[],operationCosts:[],stats:null,isLoading:!1,error:null,fetchBalance:async()=>{s({isLoading:!0,error:null});try{const a=await fetch("/api/credits/balance",{headers:{Authorization:`Bearer ${localStorage.getItem("auth_token")||sessionStorage.getItem("auth_token")}`}});if(!a.ok)throw new Error("Failed to fetch credit balance");const r=await a.json();if(r.success)s({balance:r.data.credits,isLoading:!1});else throw new Error(r.error||"Failed to fetch balance")}catch(a){s({error:a instanceof Error?a.message:"Unknown error",isLoading:!1})}},fetchTransactions:async(a=50,r=0)=>{s({isLoading:!0,error:null});try{const n=await fetch(`/api/credits/history?limit=${a}&offset=${r}`,{headers:{Authorization:`Bearer ${localStorage.getItem("auth_token")||sessionStorage.getItem("auth_token")}`}});if(!n.ok)throw new Error("Failed to fetch credit history");const x=await n.json();if(x.success)s({transactions:r===0?x.data:[...l().transactions,...x.data],isLoading:!1});else throw new Error(x.error||"Failed to fetch transactions")}catch(n){s({error:n instanceof Error?n.message:"Unknown error",isLoading:!1})}},fetchOperationCosts:async()=>{s({isLoading:!0,error:null});try{const a=await fetch("/api/credits/pricing",{headers:{Authorization:`Bearer ${localStorage.getItem("auth_token")||sessionStorage.getItem("auth_token")}`}});if(!a.ok)throw new Error("Failed to fetch operation costs");const r=await a.json();if(r.success)s({operationCosts:r.data,isLoading:!1});else throw new Error(r.error||"Failed to fetch operation costs")}catch(a){s({error:a instanceof Error?a.message:"Unknown error",isLoading:!1})}},fetchStats:async(a=30)=>{s({isLoading:!0,error:null});try{const r=await fetch(`/api/credits/stats?days=${a}`,{headers:{Authorization:`Bearer ${localStorage.getItem("auth_token")||sessionStorage.getItem("auth_token")}`}});if(!r.ok)throw new Error("Failed to fetch credit stats");const n=await r.json();if(n.success)s({stats:n.data,isLoading:!1});else throw new Error(n.error||"Failed to fetch stats")}catch(r){s({error:r instanceof Error?r.message:"Unknown error",isLoading:!1})}},purchaseCredits:async a=>{s({isLoading:!0,error:null});try{const r=await fetch("/api/credits/purchase",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${localStorage.getItem("auth_token")||sessionStorage.getItem("auth_token")}`},body:JSON.stringify(a)});if(!r.ok)throw new Error("Failed to initiate credit purchase");const n=await r.json();if(n.success)return s({isLoading:!1}),{success:!0,clientSecret:n.data.clientSecret,paymentIntentId:n.data.paymentIntentId};throw new Error(n.error||"Failed to purchase credits")}catch(r){const n=r instanceof Error?r.message:"Unknown error";return s({error:n,isLoading:!1}),{success:!1,error:n}}},clearError:()=>s({error:null}),refreshAfterPurchase:async()=>{try{await Promise.all([l().fetchBalance(),l().fetchTransactions()])}catch(a){console.error("Failed to refresh data after purchase:",a)}}})),se=({balance:s,userTier:l,isLoading:a})=>{const r=m=>{switch(m.toLowerCase()){case"pro":return"text-purple-400";case"basic":return"text-blue-400";default:return"text-gray-400"}},n=m=>{switch(m.toLowerCase()){case"pro":return e.jsx(L,{className:"w-5 h-5"});case"basic":return e.jsx(R,{className:"w-5 h-5"});default:return e.jsx(k,{className:"w-5 h-5"})}},d=(m=>m>=100?{color:"text-green-400",status:"Excellent"}:m>=50?{color:"text-yellow-400",status:"Good"}:m>=10?{color:"text-orange-400",status:"Low"}:{color:"text-red-400",status:"Critical"})(s);return e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[e.jsxs(w.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5},className:"md:col-span-2 bg-gradient-to-br from-primary-500/20 to-purple-600/20 rounded-lg p-6 border border-primary-500/30",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx("div",{className:"p-3 bg-primary-500/20 rounded-lg",children:e.jsx(k,{className:"w-6 h-6 text-primary-400"})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white",children:"Credit Balance"}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Available for AI generation"})]})]}),e.jsxs("div",{className:`flex items-center space-x-2 ${r(l)}`,children:[n(l),e.jsxs("span",{className:"font-medium",children:[l," Plan"]})]})]}),e.jsxs("div",{className:"flex items-end space-x-4",children:[e.jsx("div",{children:a?e.jsx("div",{className:"animate-pulse",children:e.jsx("div",{className:"h-12 w-32 bg-gray-600 rounded"})}):e.jsxs(w.div,{initial:{scale:.8},animate:{scale:1},transition:{duration:.3,delay:.2},children:[e.jsx("span",{className:"text-4xl font-bold text-white",children:s.toLocaleString()}),e.jsx("span",{className:"text-xl text-gray-400 ml-2",children:"credits"})]})}),e.jsxs("div",{className:`flex items-center space-x-1 ${d.color} mb-2`,children:[e.jsx("div",{className:`w-2 h-2 rounded-full ${d.color.replace("text-","bg-")}`}),e.jsx("span",{className:"text-sm font-medium",children:d.status})]})]}),s<10&&e.jsxs(w.div,{initial:{opacity:0},animate:{opacity:1},transition:{delay:.5},className:"mt-4 p-3 bg-red-500/20 border border-red-500/30 rounded-lg",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(A,{className:"w-4 h-4 text-red-400"}),e.jsx("span",{className:"text-red-400 text-sm font-medium",children:"Low Balance Warning"})]}),e.jsx("p",{className:"text-red-300 text-sm mt-1",children:"Consider purchasing more credits to continue using AI features."})]})]}),e.jsxs(w.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.5,delay:.1},className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:[e.jsx("h4",{className:"text-lg font-semibold text-white mb-4",children:"Quick Stats"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-gray-400 text-sm",children:"Plan Type"}),e.jsx("span",{className:`font-medium ${r(l)}`,children:l})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-gray-400 text-sm",children:"Status"}),e.jsx("span",{className:`font-medium ${d.color}`,children:d.status})]}),e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-gray-400 text-sm",children:"Credits Available"}),e.jsx("span",{className:"text-white font-medium",children:s})]}),l.toLowerCase()==="free"&&e.jsx("div",{className:"pt-3 border-t border-border-secondary",children:e.jsx("p",{className:"text-gray-400 text-xs",children:"Upgrade to Basic or Pro for more credits and features"})})]})]})]})},te=({transactions:s,isLoading:l,onLoadMore:a})=>{const[r,n]=j.useState("all"),[x,d]=j.useState("date"),m=t=>t>0?e.jsx(J,{className:"w-4 h-4 text-red-400"}):e.jsx(Y,{className:"w-4 h-4 text-green-400"}),y=t=>t>0?"text-red-400":"text-green-400",N=t=>{const u=new Date(t);return{date:u.toLocaleDateString(),time:u.toLocaleTimeString([],{hour:"2-digit",minute:"2-digit"})}},i=s.filter(t=>r==="used"?t.credits_used>0:r==="purchased"?t.credits_used<0:!0),o=[...i].sort((t,u)=>x==="date"?new Date(u.created_at).getTime()-new Date(t.created_at).getTime():Math.abs(u.credits_used)-Math.abs(t.credits_used)),h=()=>{const t=[["Date","Type","Credits","Operation","Description"].join(","),...o.map(f=>[new Date(f.created_at).toLocaleDateString(),f.credits_used>0?"Used":"Purchased",Math.abs(f.credits_used),f.operation_type,`"${f.description}"`].join(","))].join(`
`),u=new Blob([t],{type:"text/csv"}),p=window.URL.createObjectURL(u),v=document.createElement("a");v.href=p,v.download=`credit-history-${new Date().toISOString().split("T")[0]}.csv`,v.click(),window.URL.revokeObjectURL(p)};return e.jsxs("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 space-y-4 sm:space-y-0",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white",children:"Transaction History"}),e.jsxs("p",{className:"text-gray-400 text-sm",children:[i.length," of ",s.length," transactions"]})]}),e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsxs("div",{className:"relative",children:[e.jsxs("select",{value:r,onChange:t=>n(t.target.value),className:"appearance-none bg-background-tertiary border border-border-secondary rounded-lg px-3 py-2 pr-8 text-white text-sm focus:outline-none focus:ring-2 focus:ring-primary-500",children:[e.jsx("option",{value:"all",children:"All Transactions"}),e.jsx("option",{value:"used",children:"Credits Used"}),e.jsx("option",{value:"purchased",children:"Credits Purchased"})]}),e.jsx(D,{className:"absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none"})]}),e.jsxs("div",{className:"relative",children:[e.jsxs("select",{value:x,onChange:t=>d(t.target.value),className:"appearance-none bg-background-tertiary border border-border-secondary rounded-lg px-3 py-2 pr-8 text-white text-sm focus:outline-none focus:ring-2 focus:ring-primary-500",children:[e.jsx("option",{value:"date",children:"Sort by Date"}),e.jsx("option",{value:"amount",children:"Sort by Amount"})]}),e.jsx(D,{className:"absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none"})]}),e.jsxs(E,{onClick:h,variant:"secondary",size:"sm",disabled:s.length===0,children:[e.jsx(F,{className:"w-4 h-4 mr-2"}),"Export"]})]})]}),e.jsx("div",{className:"space-y-3",children:l&&s.length===0?e.jsx("div",{className:"space-y-3",children:[...Array(5)].map((t,u)=>e.jsx("div",{className:"animate-pulse",children:e.jsx("div",{className:"bg-background-tertiary rounded-lg p-4",children:e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("div",{className:"w-8 h-8 bg-gray-600 rounded-full"}),e.jsxs("div",{className:"flex-1",children:[e.jsx("div",{className:"h-4 bg-gray-600 rounded w-1/3 mb-2"}),e.jsx("div",{className:"h-3 bg-gray-600 rounded w-1/2"})]}),e.jsx("div",{className:"h-6 bg-gray-600 rounded w-16"})]})})},u))}):o.length===0?e.jsxs("div",{className:"text-center py-12",children:[e.jsx(A,{className:"w-12 h-12 text-gray-500 mx-auto mb-4"}),e.jsx("h4",{className:"text-lg font-medium text-gray-400 mb-2",children:"No Transactions Found"}),e.jsx("p",{className:"text-gray-500",children:r==="all"?"You haven't made any credit transactions yet.":`No ${r} transactions found.`})]}):o.map((t,u)=>{const{date:p,time:v}=N(t.created_at),f=t.credits_used<0;return e.jsx(w.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:u*.05},className:"bg-background-tertiary rounded-lg p-4 border border-border-secondary hover:border-border-primary transition-colors",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("div",{className:`p-2 rounded-full ${f?"bg-green-500/20":"bg-red-500/20"}`,children:m(t.credits_used)}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-white",children:t.operation_type.replace(/_/g," ").replace(/\b\w/g,b=>b.toUpperCase())}),e.jsx("p",{className:"text-gray-400 text-sm",children:t.description}),e.jsxs("div",{className:"flex items-center space-x-2 mt-1",children:[e.jsx("span",{className:"text-gray-500 text-xs",children:p}),e.jsx("span",{className:"text-gray-600",children:"•"}),e.jsx("span",{className:"text-gray-500 text-xs",children:v})]})]})]}),e.jsxs("div",{className:"text-right",children:[e.jsxs("span",{className:`font-semibold ${y(t.credits_used)}`,children:[f?"+":"-",Math.abs(t.credits_used)," credits"]}),t.study_set_id&&e.jsx("p",{className:"text-gray-500 text-xs mt-1",children:"Study Set"})]})]})},t.id)})}),s.length>0&&s.length%50===0&&e.jsx("div",{className:"mt-6 text-center",children:e.jsx(E,{onClick:a,variant:"secondary",isLoading:l,disabled:l,children:"Load More Transactions"})})]})},S=(s,l="")=>typeof window<"u"&&typeof import.meta<"u"&&{VITE_SUPABASE_URL:"https://jpvbtrzvbpyzgtpvltss.supabase.co",VITE_SUPABASE_ANON_KEY:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpwdmJ0cnp2YnB5emd0cHZsdHNzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA5MTM1MzAsImV4cCI6MjA2NjQ4OTUzMH0.TTEAu4XUOXRW-gBvs1qSlSx92fnW7apyMY_KTnQiUbI",BASE_URL:"/",MODE:"production",DEV:!1,PROD:!0,SSR:!1}?{VITE_SUPABASE_URL:"https://jpvbtrzvbpyzgtpvltss.supabase.co",VITE_SUPABASE_ANON_KEY:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpwdmJ0cnp2YnB5emd0cHZsdHNzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA5MTM1MzAsImV4cCI6MjA2NjQ4OTUzMH0.TTEAu4XUOXRW-gBvs1qSlSx92fnW7apyMY_KTnQiUbI",BASE_URL:"/",MODE:"production",DEV:!1,PROD:!0,SSR:!1}[s]||l:typeof process<"u"&&process.env&&process.env[s]||l,U=[{id:"Free",name:"Free",price:0,credits:25,features:["25 credits per month","Basic AI generation","Document upload (up to 5MB)","Community support","Basic analytics"],stripePriceId:void 0},{id:"Study Starter",name:"Study Starter",price:29,credits:500,features:["500 credits per month","Advanced AI generation","Document upload (up to 10MB)","Email support","Detailed analytics","Export functionality"],stripePriceId:S("STRIPE_PRICE_ID_STARTER","price_starter_placeholder")},{id:"Study Pro",name:"Study Pro",price:59,credits:1200,features:["1,200 credits per month","Premium AI generation","Document upload (up to 25MB)","Priority support","Advanced analytics","Custom study sets","Collaboration features"],isPopular:!0,stripePriceId:S("STRIPE_PRICE_ID_PRO","price_pro_placeholder")},{id:"Study Master",name:"Study Master",price:119,credits:2500,features:["2,500 credits per month","Premium AI generation","Document upload (up to 50MB)","Priority support","Advanced analytics","Custom study sets","Team collaboration","API access"],stripePriceId:S("STRIPE_PRICE_ID_MASTER","price_master_placeholder")},{id:"Study Elite",name:"Study Elite",price:239,credits:5500,features:["5,500 credits per month","Premium AI generation","Unlimited document upload","Dedicated support","Advanced analytics","Custom study sets","Team collaboration","API access","White-label options"],stripePriceId:S("STRIPE_PRICE_ID_ELITE","price_elite_placeholder")}];S("NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY",""),S("STRIPE_WEBHOOK_SECRET",""),S("FRONTEND_URL","http://localhost:3000")+"",S("FRONTEND_URL","http://localhost:3000")+"";const re=({currentBalance:s,userTier:l,onPurchaseComplete:a})=>{const[r,n]=j.useState(null),[x,d]=j.useState(!1),[m,y]=j.useState(null),[N,i]=j.useState(null),[o,h]=j.useState(""),[t,u]=j.useState(!1),[p,v]=j.useState("subscriptions"),{refreshAfterPurchase:f}=O(),{user:b}=M(),g=async c=>{const C=U.find(I=>I.id===c);if(!C){y("Selected subscription tier not found. Please try again.");return}if(!(b!=null&&b.email)){y("User email not available. Please log out and log back in.");return}d(!0),n(c),y(null),i(null);try{console.log("Creating subscription for tier:",C),i(`Subscription to ${C.name} will be implemented in the next phase. Price: $${C.price}/month`),await f(),a()}catch(I){console.error("Subscription error:",I),y("Failed to create subscription. Please try again.")}finally{d(!1),n(null)}},_=c=>{switch(c){case"Study Starter":return e.jsx(q,{className:"w-6 h-6"});case"Study Pro":return e.jsx(W,{className:"w-6 h-6"});case"Study Master":return e.jsx(X,{className:"w-6 h-6"});case"Study Elite":return e.jsx(L,{className:"w-6 h-6"});default:return e.jsx(k,{className:"w-6 h-6"})}},T=c=>l===c;return e.jsxs("div",{className:"space-y-6",children:[m&&e.jsx("div",{className:"bg-red-900/20 border border-red-500/50 rounded-lg p-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("svg",{className:"h-5 w-5 text-red-400",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})})}),e.jsxs("div",{className:"ml-3",children:[e.jsx("h3",{className:"text-sm font-medium text-red-400",children:"Error"}),e.jsx("div",{className:"mt-1 text-sm text-red-300",children:m})]}),e.jsx("div",{className:"ml-auto pl-3",children:e.jsxs("button",{onClick:()=>y(null),className:"inline-flex rounded-md bg-red-900/20 p-1.5 text-red-400 hover:bg-red-900/30 focus:outline-none focus:ring-2 focus:ring-red-500 focus:ring-offset-2 focus:ring-offset-red-900",children:[e.jsx("span",{className:"sr-only",children:"Dismiss"}),e.jsx("svg",{className:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})]})})]})}),N&&e.jsx("div",{className:"bg-green-900/20 border border-green-500/50 rounded-lg p-4",children:e.jsxs("div",{className:"flex items-center",children:[e.jsx("div",{className:"flex-shrink-0",children:e.jsx("svg",{className:"h-5 w-5 text-green-400",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})})}),e.jsxs("div",{className:"ml-3",children:[e.jsx("h3",{className:"text-sm font-medium text-green-400",children:"Success"}),e.jsx("div",{className:"mt-1 text-sm text-green-300",children:N})]}),e.jsx("div",{className:"ml-auto pl-3",children:e.jsxs("button",{onClick:()=>i(null),className:"inline-flex rounded-md bg-green-900/20 p-1.5 text-green-400 hover:bg-green-900/30 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-offset-2 focus:ring-offset-green-900",children:[e.jsx("span",{className:"sr-only",children:"Dismiss"}),e.jsx("svg",{className:"h-4 w-4",viewBox:"0 0 20 20",fill:"currentColor",children:e.jsx("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})]})})]})}),e.jsx("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsxs("h3",{className:"text-lg font-semibold text-white",children:["Current Plan: ",l]}),e.jsx("p",{className:"text-gray-400",children:"Your available credits"})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("span",{className:"text-2xl font-bold text-primary-400",children:s}),e.jsx("span",{className:"text-gray-400 ml-2",children:"credits"})]})]})}),e.jsxs("div",{className:"flex space-x-1 bg-background-tertiary rounded-lg p-1",children:[e.jsx("button",{onClick:()=>v("subscriptions"),className:`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${p==="subscriptions"?"bg-primary-500 text-white":"text-gray-400 hover:text-white"}`,children:"Monthly Plans"}),e.jsx("button",{onClick:()=>v("credits"),className:`flex-1 py-2 px-4 rounded-md text-sm font-medium transition-colors ${p==="credits"?"bg-primary-500 text-white":"text-gray-400 hover:text-white"}`,children:"One-Time Credits"})]}),p==="credits"&&e.jsxs("div",{className:"bg-background-secondary rounded-lg p-4 border border-border-primary mb-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-3",children:[e.jsx("h4",{className:"text-md font-medium text-white",children:"Have a discount code?"}),e.jsx("button",{onClick:()=>u(!t),className:"text-sm text-primary-400 hover:text-primary-300 transition-colors",children:t?"Hide":"Enter Code"})]}),t&&e.jsxs("div",{className:"flex space-x-3",children:[e.jsx("input",{type:"text",value:o,onChange:c=>h(c.target.value.toUpperCase()),placeholder:"Enter discount code",className:"flex-1 px-3 py-2 bg-background-tertiary border border-gray-600 rounded-md text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",maxLength:20}),e.jsx("button",{onClick:()=>{h(""),u(!1)},className:"px-3 py-2 text-gray-400 hover:text-white transition-colors",children:"Clear"})]})]}),p==="subscriptions"&&e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-2",children:"Choose Your Study Plan"}),e.jsx("p",{className:"text-gray-400 mb-6",children:"Monthly subscriptions with automatic credit refills. Cancel anytime."}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:U.filter(c=>c.id!=="Free").map((c,C)=>{const I=r===c.id,B=x&&I,P=T(c.id);return e.jsxs(w.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3,delay:C*.1},className:`
                    relative bg-background-secondary rounded-lg p-6 border transition-all duration-200
                    ${c.isPopular?"border-primary-500 ring-2 ring-primary-500/20":"border-border-primary hover:border-border-secondary"}
                    ${I?"ring-2 ring-primary-500/50":""}
                    ${P?"ring-2 ring-green-500/50":""}
                  `,children:[c.isPopular&&e.jsx("div",{className:"absolute -top-3 left-1/2 transform -translate-x-1/2",children:e.jsx("div",{className:"bg-primary-500 text-white px-3 py-1 rounded-full text-xs font-medium",children:"Most Popular"})}),P&&e.jsx("div",{className:"absolute -top-3 right-4",children:e.jsx("div",{className:"bg-green-500 text-white px-3 py-1 rounded-full text-xs font-medium",children:"Current Plan"})}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:`inline-flex p-3 rounded-lg mb-4 ${c.isPopular?"bg-primary-500/20 text-primary-400":"bg-background-tertiary text-gray-400"}`,children:_(c.id)}),e.jsx("h4",{className:"text-lg font-semibold text-white mb-2",children:c.name}),e.jsxs("div",{className:"mb-4",children:[e.jsx("span",{className:"text-3xl font-bold text-white",children:c.credits}),e.jsx("div",{className:"text-gray-400 text-sm",children:"credits/month"}),e.jsxs("div",{className:"text-green-400 text-xs",children:["~",c.credits*5," flashcards/quizzes"]})]}),e.jsxs("div",{className:"mb-4",children:[e.jsxs("span",{className:"text-2xl font-bold text-white",children:["$",c.price]}),e.jsx("div",{className:"text-gray-400 text-sm",children:"/month"}),e.jsxs("div",{className:"text-gray-400 text-xs",children:["$",(c.price/c.credits).toFixed(3)," per credit"]})]}),e.jsx("div",{className:"space-y-2 mb-6",children:c.features.map((z,H)=>e.jsxs("div",{className:"flex items-center text-sm text-gray-300",children:[e.jsx(V,{className:"w-4 h-4 text-green-400 mr-2 flex-shrink-0"}),e.jsx("span",{children:z})]},H))}),e.jsx(E,{onClick:()=>g(c.id),variant:c.isPopular?"primary":"secondary",className:"w-full",isLoading:B,disabled:x||!(b!=null&&b.email)||P,children:B?"Processing...":P?"Current Plan":b!=null&&b.email?`Subscribe to ${c.name}`:"Login Required"})]})]},c.id)})})]}),p==="credits"&&e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-2",children:"One-Time Credit Packages"}),e.jsx("p",{className:"text-gray-400 mb-6",children:"Purchase credits that never expire. Perfect for occasional use."}),e.jsxs("div",{className:"text-center py-12",children:[e.jsx(L,{className:"h-16 w-16 text-gray-400 mx-auto mb-4"}),e.jsx("h3",{className:"text-xl font-medium text-gray-900 mb-2",children:"One-Time Packages Coming Soon"}),e.jsx("p",{className:"text-gray-600",children:"We're working on one-time credit packages. For now, please choose a subscription plan above."})]})]}),e.jsxs("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:[e.jsx("h4",{className:"text-lg font-semibold text-white mb-4",children:p==="subscriptions"?"Subscription Benefits":"One-Time Purchase Benefits"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("h5",{className:"font-medium text-white mb-2",children:"Safe & Secure"}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Student-safe payments through Stripe. Your payment info is never stored. Perfect for using your student card or parent's card with permission."})]}),e.jsxs("div",{children:[e.jsx("h5",{className:"font-medium text-white mb-2",children:p==="subscriptions"?"Cancel Anytime":"Never Expire"}),e.jsx("p",{className:"text-gray-400 text-sm",children:p==="subscriptions"?"No long-term commitments. Cancel your subscription anytime and keep using credits until they run out.":"One-time credit purchases never expire - perfect for semester planning. Use them at your own pace!"})]})]})]})]})},ae=({stats:s,operationCosts:l})=>{const a=j.useMemo(()=>{const i=Array.from({length:7},(o,h)=>{const t=new Date;return t.setDate(t.getDate()-(6-h)),{date:t.toLocaleDateString("en-US",{weekday:"short"}),fullDate:t.toISOString().split("T")[0],credits:0}});return s!=null&&s.dailyUsage&&Array.isArray(s.dailyUsage)&&s.dailyUsage.forEach(o=>{const h=i.findIndex(t=>t.fullDate===o.date);h!==-1&&(i[h].credits=o.credits)}),i},[s==null?void 0:s.dailyUsage]),r=Math.max(...a.map(i=>i.credits),1),n=a.reduce((i,o)=>i+o.credits,0),x=n/7,d=a.slice(0,3).reduce((i,o)=>i+o.credits,0)/3,m=a.slice(4).reduce((i,o)=>i+o.credits,0)/3,y=m>d?"up":m<d?"down":"stable",N=d>0?Math.abs((m-d)/d*100):0;return e.jsxs("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:[e.jsxs("div",{className:"flex justify-between items-center mb-6",children:[e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white",children:"Usage Trends"}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Last 7 days"})]}),e.jsxs("div",{className:"flex items-center space-x-2",children:[y==="up"?e.jsx(R,{className:"w-5 h-5 text-green-400"}):y==="down"?e.jsx(K,{className:"w-5 h-5 text-red-400"}):null,y!=="stable"&&e.jsxs("span",{className:`text-sm font-medium ${y==="up"?"text-green-400":"text-red-400"}`,children:[N.toFixed(1),"%"]})]})]}),e.jsx("div",{className:"mb-6",children:e.jsx("div",{className:"flex items-end justify-between h-32 space-x-2",children:a.map((i,o)=>{const h=r>0?i.credits/r*100:0;return e.jsxs("div",{className:"flex-1 flex flex-col items-center",children:[e.jsx("div",{className:"w-full flex justify-center mb-2",children:e.jsx(w.div,{initial:{height:0},animate:{height:`${h}%`},transition:{duration:.5,delay:o*.1},className:"w-full max-w-8 bg-gradient-to-t from-primary-500 to-primary-400 rounded-t-sm relative group",style:{minHeight:h>0?"4px":"0px"},children:e.jsx("div",{className:"absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 opacity-0 group-hover:opacity-100 transition-opacity",children:e.jsxs("div",{className:"bg-background-tertiary border border-border-primary rounded px-2 py-1 text-xs text-white whitespace-nowrap",children:[i.credits," credits"]})})})}),e.jsx("span",{className:"text-xs text-gray-400",children:i.date})]},i.date)})})}),e.jsxs("div",{className:"grid grid-cols-2 gap-4",children:[e.jsx("div",{className:"bg-background-tertiary rounded-lg p-4",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-white",children:n}),e.jsx("div",{className:"text-gray-400 text-sm",children:"Total This Week"})]})}),e.jsx("div",{className:"bg-background-tertiary rounded-lg p-4",children:e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-white",children:x.toFixed(1)}),e.jsx("div",{className:"text-gray-400 text-sm",children:"Daily Average"})]})})]}),e.jsxs("div",{className:"mt-4 pt-4 border-t border-border-secondary",children:[e.jsx("h4",{className:"text-sm font-medium text-white mb-3",children:"Usage Efficiency"}),e.jsx("div",{className:"space-y-2",children:s!=null&&s.usageByOperation?Object.entries(s.usageByOperation).slice(0,3).map(([i,o])=>{const h=l==null?void 0:l.find(u=>u.operation_type===i),t=h?o*h.operations_per_credit:0;return e.jsxs("div",{className:"flex justify-between items-center text-sm",children:[e.jsx("span",{className:"text-gray-300 capitalize",children:i.replace(/_/g," ")}),e.jsxs("span",{className:"text-white",children:[t," generations"]})]},i)}):e.jsx("div",{className:"text-gray-400 text-sm text-center py-2",children:"No usage data available"})})]})]})},ie=[{id:"overview",label:"Overview",icon:G,description:"Credit balance and usage summary"},{id:"history",label:"Transaction History",icon:A,description:"Detailed credit transaction log"},{id:"purchase",label:"Buy Credits",icon:ee,description:"Purchase additional credits"}],ne=s=>{switch(s){case"quiz_generation":return"quiz questions";case"flashcard_generation":return"flashcards";case"additional_content":return"flex generations";default:return s.replace(/_/g," ").replace("generation","").trim()+"s"}},le=()=>{const[s,l]=j.useState("overview"),{user:a}=M(),{balance:r,transactions:n,operationCosts:x,stats:d,isLoading:m,error:y,fetchBalance:N,fetchTransactions:i,fetchOperationCosts:o,fetchStats:h,clearError:t}=O();j.useEffect(()=>{N(),i(),o(),h()},[N,i,o,h]);const u=async()=>{t(),await Promise.all([N(),i(),o(),h()])},p=()=>e.jsxs("div",{className:"space-y-6",children:[e.jsx(se,{balance:r,userTier:(a==null?void 0:a.subscription_tier)||"Free",isLoading:m}),d&&e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsx(ae,{stats:d,operationCosts:x}),e.jsxs("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Usage Breakdown"}),e.jsx("div",{className:"space-y-3",children:d!=null&&d.usageByOperation?Object.entries(d.usageByOperation).map(([g,_])=>e.jsxs("div",{className:"flex justify-between items-center",children:[e.jsx("span",{className:"text-gray-300 capitalize",children:g.replace(/_/g," ")}),e.jsxs("span",{className:"text-white font-medium",children:[_," credits"]})]},g)):e.jsx("div",{className:"text-gray-400 text-center py-4",children:"No usage data available"})})]})]}),e.jsxs("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Credit Costs"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:(x==null?void 0:x.length)>0?x.map(g=>e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-4 border border-border-secondary",children:[e.jsx("h4",{className:"font-medium text-white capitalize mb-2",children:g.operation_type.replace(/_/g," ")}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(k,{className:"w-4 h-4 text-primary-400"}),e.jsxs("span",{className:"text-primary-400 font-semibold",children:["1 credit = ",g.operations_per_credit," ",ne(g.operation_type)]})]})]},g.operation_type)):e.jsx("div",{className:"col-span-full text-gray-400 text-center py-8",children:"No operation cost data available"})})]})]}),v=()=>e.jsx(te,{transactions:n,isLoading:m,onLoadMore:()=>i(50,n.length)}),f=()=>e.jsx(re,{currentBalance:r,userTier:(a==null?void 0:a.subscription_tier)||"Study Starter",onPurchaseComplete:u}),b=()=>{switch(s){case"overview":return p();case"history":return v();case"purchase":return f();default:return p()}};return e.jsx("div",{className:"min-h-screen bg-background-primary text-white",children:e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[e.jsxs("div",{className:"flex justify-between items-center mb-8",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold text-white mb-2",children:"Credits"}),e.jsx("p",{className:"text-gray-400",children:"Manage your AI generation credits"})]}),e.jsx("div",{className:"flex items-center space-x-4",children:e.jsxs(E,{onClick:u,variant:"secondary",disabled:m,children:[e.jsx(Z,{className:`w-4 h-4 mr-2 ${m?"animate-spin":""}`}),"Refresh"]})})]}),y&&e.jsxs("div",{className:"mb-6 bg-red-500/20 border border-red-500/30 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(Q,{className:"w-5 h-5 text-red-400"}),e.jsx("span",{className:"text-red-400 font-medium",children:"Error"})]}),e.jsx("p",{className:"text-red-300 mt-1",children:y}),e.jsx(E,{onClick:t,variant:"secondary",size:"sm",className:"mt-2",children:"Dismiss"})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[e.jsx("div",{className:"lg:col-span-1",children:e.jsx("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:e.jsx("nav",{className:"space-y-2",children:ie.map(g=>{const _=g.icon,T=s===g.id;return e.jsxs("button",{onClick:()=>l(g.id),className:`
                        w-full flex items-center space-x-3 px-3 py-3 rounded-lg text-left
                        transition-all duration-200
                        ${T?"bg-primary-500/20 text-primary-400 border border-primary-500/30":"text-gray-300 hover:bg-background-tertiary hover:text-white"}
                      `,children:[e.jsx(_,{className:"w-5 h-5"}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("span",{className:"font-medium block",children:g.label}),e.jsx("span",{className:"text-xs text-gray-500 block truncate",children:g.description})]})]},g.id)})})})}),e.jsx("div",{className:"lg:col-span-3",children:e.jsx(w.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.3},children:b()},s)})]})]})})};export{le as CreditsPage};
