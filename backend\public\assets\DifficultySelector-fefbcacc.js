import{j as a}from"./index-e6f11275.js";var r=(e=>(e.EASY="easy",e.MEDIUM="medium",e.HARD="hard",e.COLLEGE="college",e.GRADUATE="graduate",e.PHD="phd",e))(r||{}),c=(e=>(e.SHORT="short",e.MEDIUM="medium",e.LONG="long",e))(c||{});const b=e=>({easy:"Easy",medium:"Medium",hard:"Hard",college:"College",graduate:"Graduate",phd:"PhD"})[e],h=e=>({easy:1,medium:3,hard:4,college:5,graduate:6,phd:7})[e],y=e=>{switch(e){case 1:return"easy";case 2:return"easy";case 3:return"medium";case 4:return"hard";case 5:return"college";case 6:return"graduate";case 7:return"phd";default:return"medium"}},u=[{value:r.<PERSON>,label:"Easy",description:"Basic facts and definitions"},{value:r.MEDIUM,label:"Medium",description:"Moderate understanding required"},{value:r.HARD,label:"Hard",description:"Deep analysis and critical thinking"},{value:r.COLLEGE,label:"College",description:"Undergraduate level complexity"},{value:r.GRADUATE,label:"Graduate",description:"Advanced graduate study"},{value:r.PHD,label:"PhD",description:"Research-level expertise"}],g=e=>{switch(e){case r.EASY:return"bg-background-secondary text-text-primary border-green-500/30 hover:bg-green-500/10 hover:border-green-500/50";case r.MEDIUM:return"bg-background-secondary text-text-primary border-primary-500/30 hover:bg-primary-500/10 hover:border-primary-500/50";case r.HARD:return"bg-background-secondary text-text-primary border-orange-500/30 hover:bg-orange-500/10 hover:border-orange-500/50";case r.COLLEGE:return"bg-background-secondary text-text-primary border-purple-500/30 hover:bg-purple-500/10 hover:border-purple-500/50";case r.GRADUATE:return"bg-background-secondary text-text-primary border-red-500/30 hover:bg-red-500/10 hover:border-red-500/50";case r.PHD:return"bg-background-secondary text-text-primary border-gray-500/30 hover:bg-gray-500/10 hover:border-gray-500/50";default:return"bg-background-secondary text-text-primary border-primary-500/30 hover:bg-primary-500/10 hover:border-primary-500/50"}},p=e=>{switch(e){case r.EASY:return"bg-green-500/20 text-green-300 border-green-500 shadow-lg shadow-green-500/20";case r.MEDIUM:return"bg-primary-500/20 text-primary-300 border-primary-500 shadow-lg shadow-primary-500/20";case r.HARD:return"bg-orange-500/20 text-orange-300 border-orange-500 shadow-lg shadow-orange-500/20";case r.COLLEGE:return"bg-purple-500/20 text-purple-300 border-purple-500 shadow-lg shadow-purple-500/20";case r.GRADUATE:return"bg-red-500/20 text-red-300 border-red-500 shadow-lg shadow-red-500/20";case r.PHD:return"bg-gray-500/20 text-gray-300 border-gray-500 shadow-lg shadow-gray-500/20";default:return"bg-primary-500/20 text-primary-300 border-primary-500 shadow-lg shadow-primary-500/20"}},x=({value:e,onChange:d,className:l="",disabled:o=!1,label:n="Difficulty Level"})=>a.jsxs("div",{className:`space-y-3 ${l}`,children:[a.jsx("label",{className:"block text-sm font-medium text-text-primary",children:n}),a.jsx("div",{className:"grid grid-cols-2 md:grid-cols-3 gap-3",children:u.map(t=>{const s=e===t.value,i=s?p(t.value):g(t.value);return a.jsxs("button",{type:"button",onClick:()=>!o&&d(t.value),disabled:o,className:`
                relative p-3 rounded-lg border-2 text-sm font-medium transition-all duration-200 transform-gpu
                ${i}
                ${o?"opacity-50 cursor-not-allowed":"cursor-pointer hover:scale-105"}
                ${s?"ring-2 ring-offset-2 ring-primary-500 ring-offset-background-primary":""}
                focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 focus:ring-offset-background-primary
              `,title:t.description,"aria-pressed":s,children:[a.jsxs("div",{className:"text-center",children:[a.jsx("div",{className:"font-semibold",children:t.label}),a.jsx("div",{className:`text-xs mt-1 ${s?"text-white/90":"text-text-secondary"}`,children:t.description})]}),s&&a.jsx("div",{className:"absolute top-2 right-2",children:a.jsx("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",children:a.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})})]},t.value)})}),a.jsx("p",{className:"text-xs text-text-muted",children:"Select the appropriate difficulty level for your flashcards. This affects the complexity of questions and answers generated."})]});export{c as C,r as D,x as a,h as b,b as d,y as n};
