import{r as o,j as e,E as O,T as de,B as x,D as V,n as Z,M as me,L as W,p as _,A as H,U as te,F as R,i as K,V as q,W as Y,d as B,H as U,X as ee,I as $,Y as Q,_ as J,b as ue,u as xe,Z as he,$ as pe,a0 as fe,a1 as ge,a2 as ye,y as be,x as je,a3 as Ne}from"./index-e6f11275.js";import{D as we,a as ve}from"./DifficultySelector-fefbcacc.js";const se=[{id:"free",name:"Study Starter",price:0,interval:"month",features:["500 AI study generations per month","Basic flashcards and quizzes","Up to 5 document uploads","Basic study analytics","Perfect for trying out ChewyAI"]},{id:"pro_monthly",name:"Scholar Pro",price:9.99,interval:"month",popular:!0,features:["Unlimited AI study generations","Advanced study modes & spaced repetition","Unlimited document uploads","Detailed progress analytics","Priority support during finals","Export study materials","Perfect for serious students"]},{id:"pro_yearly",name:"Academic Year Pass",price:99.99,interval:"year",features:["Unlimited AI study generations","Advanced study modes & spaced repetition","Unlimited document uploads","Detailed progress analytics","Priority support during finals","Export study materials","Save $20 vs monthly (2 months free!)","Perfect for the full academic year"]}],Se=()=>{var p,l;const[a,k]=o.useState(null),[g,d]=o.useState(!0),[b,c]=o.useState(!1),[j,n]=o.useState(null);o.useEffect(()=>{m()},[]);const m=async()=>{d(!0),n(null);try{const s=localStorage.getItem("auth_token"),t=await fetch("/api/subscription",{headers:{Authorization:`Bearer ${s}`}});if(!t.ok)throw new Error("Failed to fetch subscription data");const i=await t.json();if(i.success)k(i.data);else throw new Error(i.error)}catch(s){n(s instanceof Error?s.message:"Failed to load subscription data"),k({currentPlan:se[0],status:"active",nextBillingDate:new Date(Date.now()+30*24*60*60*1e3).toISOString()})}finally{d(!1)}},v=async s=>{c(!0),n(null);try{const t=localStorage.getItem("auth_token"),i=await fetch("/api/subscription/change",{method:"POST",headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"},body:JSON.stringify({planId:s})});if(!i.ok)throw new Error("Failed to change subscription plan");const u=await i.json();if(u.success)await m();else throw new Error(u.error)}catch(t){n(t instanceof Error?t.message:"Failed to change subscription plan")}finally{c(!1)}},h=async()=>{if(confirm("Are you sure you want to cancel your subscription? You will lose access to Pro features at the end of your billing period.")){c(!0),n(null);try{const s=localStorage.getItem("auth_token"),t=await fetch("/api/subscription/cancel",{method:"POST",headers:{Authorization:`Bearer ${s}`}});if(!t.ok)throw new Error("Failed to cancel subscription");const i=await t.json();if(i.success)await m();else throw new Error(i.error)}catch(s){n(s instanceof Error?s.message:"Failed to cancel subscription")}finally{c(!1)}}},f=async()=>{c(!0),n(null);try{const s=localStorage.getItem("auth_token"),t=await fetch("/api/subscription/reactivate",{method:"POST",headers:{Authorization:`Bearer ${s}`}});if(!t.ok)throw new Error("Failed to reactivate subscription");const i=await t.json();if(i.success)await m();else throw new Error(i.error)}catch(s){n(s instanceof Error?s.message:"Failed to reactivate subscription")}finally{c(!1)}};return g?e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"animate-pulse",children:[e.jsx("div",{className:"h-8 w-48 bg-gray-600 rounded mb-4"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[1,2,3].map(s=>e.jsx("div",{className:"h-64 bg-gray-600 rounded-lg"},s))})]})}):e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Subscription Management"}),j&&e.jsxs("div",{className:"mb-6 bg-red-500/20 border border-red-500/30 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(O,{className:"w-5 h-5 text-red-400"}),e.jsx("span",{className:"text-red-400 font-medium",children:"Error"})]}),e.jsx("p",{className:"text-red-300 mt-1",children:j})]}),a&&e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-6 border border-border-primary mb-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"text-lg font-medium text-white",children:"Current Plan"}),e.jsx("p",{className:"text-gray-400",children:((p=a.currentPlan)==null?void 0:p.name)||"No active plan"})]}),e.jsx("div",{className:"text-right",children:e.jsx("div",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${a.status==="active"?"bg-green-500/20 text-green-400":a.status==="canceled"?"bg-red-500/20 text-red-400":a.status==="past_due"?"bg-yellow-500/20 text-yellow-400":"bg-blue-500/20 text-blue-400"}`,children:a.status.charAt(0).toUpperCase()+a.status.slice(1)})})]}),a.nextBillingDate&&e.jsxs("div",{className:"flex items-center space-x-2 text-gray-400 text-sm",children:[e.jsx(de,{className:"w-4 h-4"}),e.jsx("span",{children:a.cancelAtPeriodEnd?`Access ends on ${new Date(a.nextBillingDate).toLocaleDateString()}`:`Next billing date: ${new Date(a.nextBillingDate).toLocaleDateString()}`})]}),((l=a.currentPlan)==null?void 0:l.id)!=="free"&&e.jsxs("div",{className:"mt-4 flex space-x-3",children:[a.cancelAtPeriodEnd?e.jsx(x,{onClick:f,isLoading:b,variant:"primary",size:"sm",children:"Reactivate Subscription"}):e.jsx(x,{onClick:h,isLoading:b,variant:"danger",size:"sm",children:"Cancel Subscription"}),e.jsxs(x,{onClick:m,variant:"secondary",size:"sm",children:[e.jsx(V,{className:"w-4 h-4 mr-2"}),"Refresh"]})]})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-lg font-medium text-white mb-4",children:"Available Plans"}),e.jsx("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:se.map(s=>{var i;const t=((i=a==null?void 0:a.currentPlan)==null?void 0:i.id)===s.id;return e.jsxs(Z.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},className:`relative bg-background-secondary rounded-lg p-6 border transition-all duration-200 ${s.popular?"border-primary-500 ring-2 ring-primary-500/20":t?"border-green-500 ring-2 ring-green-500/20":"border-border-primary hover:border-gray-500"}`,children:[s.popular&&e.jsx("div",{className:"absolute -top-3 left-1/2 transform -translate-x-1/2",children:e.jsxs("div",{className:"bg-primary-500 text-white px-3 py-1 rounded-full text-xs font-medium flex items-center space-x-1",children:[e.jsx(me,{className:"w-3 h-3"}),e.jsx("span",{children:"Most Popular"})]})}),t&&e.jsx("div",{className:"absolute -top-3 right-4",children:e.jsxs("div",{className:"bg-green-500 text-white px-3 py-1 rounded-full text-xs font-medium flex items-center space-x-1",children:[e.jsx(W,{className:"w-3 h-3"}),e.jsx("span",{children:"Current"})]})}),e.jsxs("div",{className:"text-center mb-6",children:[e.jsx("h5",{className:"text-xl font-semibold text-white mb-2",children:s.name}),e.jsxs("div",{className:"text-3xl font-bold text-white",children:["$",s.price,e.jsxs("span",{className:"text-lg text-gray-400",children:["/",s.interval]})]})]}),e.jsx("ul",{className:"space-y-3 mb-6",children:s.features.map((u,N)=>e.jsxs("li",{className:"flex items-center space-x-2",children:[e.jsx(W,{className:"w-4 h-4 text-green-400 flex-shrink-0"}),e.jsx("span",{className:"text-gray-300 text-sm",children:u})]},N))}),e.jsx(x,{onClick:()=>v(s.id),disabled:t||b,isLoading:b,variant:s.popular?"primary":"secondary",className:"w-full",children:t?"Current Plan":`Switch to ${s.name}`})]},s.id)})})]})]})})},ke=()=>{const[a,k]=o.useState(!1),[g,d]=o.useState(!1),[b,c]=o.useState(null),[j,n]=o.useState(null),[m,v]=o.useState({studySets:12,flashcards:245,quizzes:18,documents:8,totalSize:"2.4 MB"}),[h,f]=o.useState({studySets:!0,flashcards:!0,quizzes:!0,analytics:!0,preferences:!0}),p=async()=>{k(!0),c(null),n(null);try{const t=localStorage.getItem("auth_token"),i=await fetch("/api/data/export",{method:"POST",headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"},body:JSON.stringify({exportData:h})});if(!i.ok)throw new Error("Failed to export data");const u=await i.blob(),N=window.URL.createObjectURL(u),C=document.createElement("a");C.href=N,C.download=`chewyai-data-export-${new Date().toISOString().split("T")[0]}.json`,C.click(),window.URL.revokeObjectURL(N),n("Data exported successfully!")}catch(t){c(t instanceof Error?t.message:"Failed to export data")}finally{k(!1)}},l=async t=>{if(confirm(`Are you sure you want to clear all ${t}? This action cannot be undone.`)){d(!0),c(null),n(null);try{const i=localStorage.getItem("auth_token"),u=await fetch(`/api/data/clear/${t}`,{method:"DELETE",headers:{Authorization:`Bearer ${i}`}});if(!u.ok)throw new Error(`Failed to clear ${t}`);const N=await u.json();if(N.success)n(`${t} cleared successfully!`),await s();else throw new Error(N.error)}catch(i){c(i instanceof Error?i.message:`Failed to clear ${t}`)}finally{d(!1)}}},s=async()=>{try{const t=localStorage.getItem("auth_token"),i=await fetch("/api/data/stats",{headers:{Authorization:`Bearer ${t}`}});if(i.ok){const u=await i.json();u.success&&v(u.data)}}catch{}};return e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Data Management"}),b&&e.jsxs("div",{className:"mb-6 bg-red-500/20 border border-red-500/30 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(O,{className:"w-5 h-5 text-red-400"}),e.jsx("span",{className:"text-red-400 font-medium",children:"Error"})]}),e.jsx("p",{className:"text-red-300 mt-1",children:b})]}),j&&e.jsxs("div",{className:"mb-6 bg-green-500/20 border border-green-500/30 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(_,{className:"w-5 h-5 text-green-400"}),e.jsx("span",{className:"text-green-400 font-medium",children:"Success"})]}),e.jsx("p",{className:"text-green-300 mt-1",children:j})]}),e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-6 border border-border-primary mb-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h4",{className:"text-lg font-medium text-white",children:"Your Data Overview"}),e.jsxs(x,{onClick:s,variant:"secondary",size:"sm",children:[e.jsx(V,{className:"w-4 h-4 mr-2"}),"Refresh"]})]}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-5 gap-4",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-primary-400",children:m.studySets}),e.jsx("div",{className:"text-sm text-gray-400",children:"Study Sets"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-primary-400",children:m.flashcards}),e.jsx("div",{className:"text-sm text-gray-400",children:"Flashcards"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-primary-400",children:m.quizzes}),e.jsx("div",{className:"text-sm text-gray-400",children:"Quizzes"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-primary-400",children:m.documents}),e.jsx("div",{className:"text-sm text-gray-400",children:"Documents"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-primary-400",children:m.totalSize}),e.jsx("div",{className:"text-sm text-gray-400",children:"Total Size"})]})]})]}),e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-6 border border-border-primary mb-6",children:[e.jsxs("div",{className:"flex items-center space-x-3 mb-4",children:[e.jsx(H,{className:"w-6 h-6 text-blue-400"}),e.jsx("h4",{className:"text-lg font-medium text-white",children:"Export Your Data"})]}),e.jsx("p",{className:"text-gray-400 text-sm mb-4",children:"Download a copy of your data in JSON format. You can use this to backup your data or import it into another account."}),e.jsxs("div",{className:"space-y-3 mb-6",children:[e.jsx("h5",{className:"font-medium text-white",children:"Select data to export:"}),Object.entries(h).map(([t,i])=>e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("label",{className:"text-sm text-gray-300 capitalize",children:t.replace(/([A-Z])/g," $1").trim()}),e.jsx("button",{onClick:()=>f({...h,[t]:!i}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${i?"bg-primary-500":"bg-gray-600"}`,children:e.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${i?"translate-x-6":"translate-x-1"}`})})]},t))]}),e.jsxs(x,{onClick:p,isLoading:a,variant:"primary",children:[e.jsx(H,{className:"w-4 h-4 mr-2"}),"Export Data"]})]}),e.jsxs("div",{className:"bg-red-500/10 rounded-lg p-6 border border-red-500/30",children:[e.jsxs("div",{className:"flex items-center space-x-3 mb-4",children:[e.jsx(te,{className:"w-6 h-6 text-red-400"}),e.jsx("h4",{className:"text-lg font-medium text-white",children:"Clear Data"})]}),e.jsx("p",{className:"text-gray-400 text-sm mb-6",children:"Permanently delete specific types of data from your account. This action cannot be undone."}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[e.jsxs("div",{className:"space-y-3",children:[e.jsx(x,{onClick:()=>l("study-sets"),isLoading:g,variant:"danger",size:"sm",className:"w-full",children:"Clear All Study Sets"}),e.jsx(x,{onClick:()=>l("flashcards"),isLoading:g,variant:"danger",size:"sm",className:"w-full",children:"Clear All Flashcards"})]}),e.jsxs("div",{className:"space-y-3",children:[e.jsx(x,{onClick:()=>l("quizzes"),isLoading:g,variant:"danger",size:"sm",className:"w-full",children:"Clear All Quizzes"}),e.jsx(x,{onClick:()=>l("analytics"),isLoading:g,variant:"danger",size:"sm",className:"w-full",children:"Clear Analytics Data"})]})]})]})]})})},Ce=()=>{const[a,k]=o.useState(null),[g,d]=o.useState(!0),[b,c]=o.useState(!1),[j,n]=o.useState(null);o.useEffect(()=>{m()},[]);const m=async()=>{d(!0),n(null);try{const s=localStorage.getItem("auth_token"),t=await fetch("/api/billing",{headers:{Authorization:`Bearer ${s}`}});if(!t.ok)throw new Error("Failed to fetch billing data");const i=await t.json();if(i.success)k(i.data);else throw new Error(i.error)}catch(s){n(s instanceof Error?s.message:"Failed to load billing data"),k({paymentMethods:[{id:"1",type:"card",last4:"4242",brand:"visa",expiryMonth:12,expiryYear:2025,isDefault:!0}],invoices:[{id:"1",number:"INV-001",amount:9.99,currency:"USD",status:"paid",date:new Date(Date.now()-30*24*60*60*1e3).toISOString(),description:"Pro Plan - Monthly"},{id:"2",number:"INV-002",amount:9.99,currency:"USD",status:"pending",date:new Date().toISOString(),dueDate:new Date(Date.now()+7*24*60*60*1e3).toISOString(),description:"Pro Plan - Monthly"}],nextInvoice:{amount:9.99,currency:"USD",date:new Date(Date.now()+30*24*60*60*1e3).toISOString()}})}finally{d(!1)}},v=async s=>{try{const t=localStorage.getItem("auth_token"),i=await fetch(`/api/billing/invoices/${s}/download`,{headers:{Authorization:`Bearer ${t}`}});if(!i.ok)throw new Error("Failed to download invoice");const u=await i.blob(),N=window.URL.createObjectURL(u),C=document.createElement("a");C.href=N,C.download=`invoice-${s}.pdf`,C.click(),window.URL.revokeObjectURL(N)}catch(t){n(t instanceof Error?t.message:"Failed to download invoice")}},h=async()=>{n("Payment method management coming soon")},f=async s=>{c(!0),n(null);try{const t=localStorage.getItem("auth_token"),i=await fetch("/api/billing/payment-methods/default",{method:"POST",headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"},body:JSON.stringify({paymentMethodId:s})});if(!i.ok)throw new Error("Failed to update default payment method");const u=await i.json();if(u.success)await m();else throw new Error(u.error)}catch(t){n(t instanceof Error?t.message:"Failed to update payment method")}finally{c(!1)}},p=s=>{switch(s){case"paid":return e.jsx(_,{className:"w-5 h-5 text-green-400"});case"pending":return e.jsx(K,{className:"w-5 h-5 text-yellow-400"});case"failed":return e.jsx(q,{className:"w-5 h-5 text-red-400"});default:return e.jsx(K,{className:"w-5 h-5 text-gray-400"})}},l=s=>{switch(s){case"paid":return"text-green-400";case"pending":return"text-yellow-400";case"failed":return"text-red-400";default:return"text-gray-400"}};return g?e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{className:"animate-pulse",children:[e.jsx("div",{className:"h-8 w-48 bg-gray-600 rounded mb-4"}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"h-32 bg-gray-600 rounded-lg"}),e.jsx("div",{className:"h-64 bg-gray-600 rounded-lg"})]})]})}):e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h3",{className:"text-lg font-semibold text-white",children:"Billing & Invoices"}),e.jsxs(x,{onClick:m,variant:"secondary",size:"sm",children:[e.jsx(V,{className:"w-4 h-4 mr-2"}),"Refresh"]})]}),j&&e.jsxs("div",{className:"mb-6 bg-red-500/20 border border-red-500/30 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(O,{className:"w-5 h-5 text-red-400"}),e.jsx("span",{className:"text-red-400 font-medium",children:"Error"})]}),e.jsx("p",{className:"text-red-300 mt-1",children:j})]}),e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-6 border border-border-primary mb-6",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("h4",{className:"text-lg font-medium text-white",children:"Payment Methods"}),e.jsx(x,{onClick:h,variant:"secondary",size:"sm",children:"Add Payment Method"})]}),(a==null?void 0:a.paymentMethods.length)===0?e.jsxs("div",{className:"text-center py-8",children:[e.jsx(R,{className:"w-16 h-16 text-gray-500 mx-auto mb-4"}),e.jsx("p",{className:"text-gray-400",children:"No payment methods added"})]}):e.jsx("div",{className:"space-y-3",children:a==null?void 0:a.paymentMethods.map(s=>{var t;return e.jsxs("div",{className:"flex items-center justify-between p-4 bg-background-secondary rounded-lg border border-border-primary",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(R,{className:"w-6 h-6 text-gray-400"}),e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsxs("span",{className:"text-white font-medium",children:[(t=s.brand)==null?void 0:t.toUpperCase()," •••• ",s.last4]}),s.isDefault&&e.jsx("span",{className:"bg-primary-500/20 text-primary-400 px-2 py-1 rounded text-xs font-medium",children:"Default"})]}),s.expiryMonth&&s.expiryYear&&e.jsxs("p",{className:"text-gray-400 text-sm",children:["Expires ",s.expiryMonth.toString().padStart(2,"0"),"/",s.expiryYear]})]})]}),!s.isDefault&&e.jsx(x,{onClick:()=>f(s.id),isLoading:b,variant:"secondary",size:"sm",children:"Set as Default"})]},s.id)})})]}),(a==null?void 0:a.nextInvoice)&&e.jsxs("div",{className:"bg-blue-500/10 rounded-lg p-6 border border-blue-500/30 mb-6",children:[e.jsx("h4",{className:"text-lg font-medium text-white mb-2",children:"Upcoming Invoice"}),e.jsx("div",{className:"flex items-center justify-between",children:e.jsxs("div",{children:[e.jsxs("p",{className:"text-blue-300",children:["$",a.nextInvoice.amount," ",a.nextInvoice.currency.toUpperCase()]}),e.jsxs("p",{className:"text-blue-400 text-sm",children:["Due on ",new Date(a.nextInvoice.date).toLocaleDateString()]})]})})]}),e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-6 border border-border-primary",children:[e.jsx("h4",{className:"text-lg font-medium text-white mb-4",children:"Invoice History"}),(a==null?void 0:a.invoices.length)===0?e.jsx("div",{className:"text-center py-8",children:e.jsx("p",{className:"text-gray-400",children:"No invoices found"})}):e.jsx("div",{className:"space-y-3",children:a==null?void 0:a.invoices.map(s=>e.jsxs(Z.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"flex items-center justify-between p-4 bg-background-secondary rounded-lg border border-border-primary",children:[e.jsxs("div",{className:"flex items-center space-x-4",children:[p(s.status),e.jsxs("div",{children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("span",{className:"text-white font-medium",children:s.number}),e.jsx("span",{className:`text-sm font-medium ${l(s.status)}`,children:s.status.charAt(0).toUpperCase()+s.status.slice(1)})]}),e.jsx("p",{className:"text-gray-400 text-sm",children:s.description}),e.jsxs("p",{className:"text-gray-500 text-xs",children:[new Date(s.date).toLocaleDateString(),s.dueDate&&s.status==="pending"&&e.jsxs("span",{children:[" • Due ",new Date(s.dueDate).toLocaleDateString()]})]})]})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("span",{className:"text-white font-medium",children:["$",s.amount," ",s.currency.toUpperCase()]}),s.status==="paid"&&e.jsxs(x,{onClick:()=>v(s.id),variant:"secondary",size:"sm",children:[e.jsx(H,{className:"w-4 h-4 mr-2"}),"Download"]})]})]},s.id))})]})]})})},Ee=({enabled:a,onToggle:k})=>{const[g,d]=o.useState(!1),[b,c]=o.useState(!1),[j,n]=o.useState(null),[m,v]=o.useState(null),[h,f]=o.useState(null),[p,l]=o.useState(""),[s,t]=o.useState(!1),[i,u]=o.useState(!1),N=async()=>{c(!0),n(null),v(null);try{const{createClient:w}=await J(()=>import("./index-e6f11275.js").then(T=>T.a4),["assets/index-e6f11275.js","assets/index-e37db108.css"]),I=w("https://jpvbtrzvbpyzgtpvltss.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.TTEAu4XUOXRW-gBvs1qSlSx92fnW7apyMY_KTnQiUbI"),{data:M,error:z}=await I.auth.mfa.enroll({factorType:"totp",friendlyName:"ChewyAI Authenticator"});if(z)throw new Error(z.message);f({qrCode:M.totp.qr_code,secret:M.totp.secret,backupCodes:[],factorId:M.id}),d(!0)}catch(w){n(w instanceof Error?w.message:"Failed to setup 2FA"),f({qrCode:"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==",secret:"JBSWY3DPEHPK3PXP",factorId:"mock-factor-id",backupCodes:["12345678","87654321","11111111","22222222","33333333","44444444","55555555","66666666"]}),d(!0)}finally{c(!1)}},C=async()=>{if(!p||p.length!==6){n("Please enter a valid 6-digit code");return}c(!0),n(null);try{const{createClient:w}=await J(()=>import("./index-e6f11275.js").then(L=>L.a4),["assets/index-e6f11275.js","assets/index-e37db108.css"]),I=w("https://jpvbtrzvbpyzgtpvltss.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.TTEAu4XUOXRW-gBvs1qSlSx92fnW7apyMY_KTnQiUbI");if(!h)throw new Error("No setup data available");const{data:M,error:z}=await I.auth.mfa.challenge({factorId:h.factorId});if(z)throw new Error(z.message);const{error:T}=await I.auth.mfa.verify({factorId:h.factorId,challengeId:M.id,code:p});if(T)throw new Error(T.message);v("Two-factor authentication enabled successfully!"),d(!1),u(!0),k(!0)}catch(w){n(w instanceof Error?w.message:"Failed to verify 2FA code")}finally{c(!1)}},A=async()=>{if(confirm("Are you sure you want to disable two-factor authentication? This will make your account less secure.")){c(!0),n(null);try{const{createClient:w}=await J(()=>import("./index-e6f11275.js").then(T=>T.a4),["assets/index-e6f11275.js","assets/index-e37db108.css"]),I=w("https://jpvbtrzvbpyzgtpvltss.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.TTEAu4XUOXRW-gBvs1qSlSx92fnW7apyMY_KTnQiUbI"),{data:M,error:z}=await I.auth.mfa.listFactors();if(z)throw new Error(z.message);for(const T of M.totp){const{error:L}=await I.auth.mfa.unenroll({factorId:T.id});L&&console.error("Failed to unenroll factor:",L)}v("Two-factor authentication disabled successfully"),k(!1)}catch(w){n(w instanceof Error?w.message:"Failed to disable 2FA")}finally{c(!1)}}},E=w=>{navigator.clipboard.writeText(w),v("Copied to clipboard!"),setTimeout(()=>v(null),2e3)},D=()=>{d(!1),f(null),l(""),n(null),v(null)};return g&&h?e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-6 border border-border-primary",children:[e.jsxs("div",{className:"flex items-center space-x-3 mb-6",children:[e.jsx(Y,{className:"w-6 h-6 text-primary-400"}),e.jsx("h4",{className:"text-lg font-medium text-white",children:"Setup Two-Factor Authentication"})]}),j&&e.jsx("div",{className:"mb-4 bg-red-500/20 border border-red-500/30 rounded-lg p-3",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(O,{className:"w-4 h-4 text-red-400"}),e.jsx("span",{className:"text-red-400 text-sm",children:j})]})}),m&&e.jsx("div",{className:"mb-4 bg-green-500/20 border border-green-500/30 rounded-lg p-3",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(_,{className:"w-4 h-4 text-green-400"}),e.jsx("span",{className:"text-green-400 text-sm",children:m})]})}),i?e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsx(_,{className:"w-16 h-16 text-green-400 mx-auto mb-4"}),e.jsx("h5",{className:"text-lg font-medium text-white mb-2",children:"2FA Enabled Successfully!"}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Your account is now protected with two-factor authentication."})]}),e.jsxs("div",{className:"bg-yellow-500/10 border border-yellow-500/30 rounded-lg p-4",children:[e.jsx("h5",{className:"font-medium text-yellow-400 mb-2",children:"Important: Save Your Backup Codes"}),e.jsx("p",{className:"text-yellow-300 text-sm mb-4",children:"Store these backup codes in a safe place. You can use them to access your account if you lose your authenticator device."}),e.jsx("div",{className:"grid grid-cols-2 gap-2 mb-4",children:h.backupCodes.map((w,I)=>e.jsx("div",{className:"bg-background-secondary border border-border-primary rounded p-2 text-center",children:e.jsx("code",{className:"text-primary-400 font-mono",children:w})},I))}),e.jsxs(x,{onClick:()=>E(h.backupCodes.join(`
`)),variant:"secondary",size:"sm",children:[e.jsx(ee,{className:"w-4 h-4 mr-2"}),"Copy All Codes"]})]}),e.jsx(x,{onClick:()=>u(!1),variant:"primary",className:"w-full",children:"I've Saved My Backup Codes"})]}):e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("h5",{className:"font-medium text-white mb-2",children:"Step 1: Scan QR Code"}),e.jsx("p",{className:"text-gray-400 text-sm mb-4",children:"Scan this QR code with your authenticator app (Google Authenticator, Authy, etc.)"}),e.jsx("div",{className:"bg-white p-4 rounded-lg inline-block",children:e.jsx("img",{src:h.qrCode,alt:"2FA QR Code",className:"w-48 h-48"})})]}),e.jsxs("div",{children:[e.jsx("h5",{className:"font-medium text-white mb-2",children:"Step 2: Manual Entry (Alternative)"}),e.jsx("p",{className:"text-gray-400 text-sm mb-3",children:"If you can't scan the QR code, enter this secret key manually:"}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:"flex-1 bg-background-secondary border border-border-primary rounded-lg p-3",children:e.jsx("code",{className:"text-primary-400 font-mono",children:s?h.secret:"••••••••••••••••"})}),e.jsx(x,{onClick:()=>t(!s),variant:"secondary",size:"sm",children:s?e.jsx(B,{className:"w-4 h-4"}):e.jsx(U,{className:"w-4 h-4"})}),e.jsx(x,{onClick:()=>E(h.secret),variant:"secondary",size:"sm",children:e.jsx(ee,{className:"w-4 h-4"})})]})]}),e.jsxs("div",{children:[e.jsx("h5",{className:"font-medium text-white mb-2",children:"Step 3: Verify Setup"}),e.jsx("p",{className:"text-gray-400 text-sm mb-3",children:"Enter the 6-digit code from your authenticator app:"}),e.jsxs("div",{className:"flex space-x-3",children:[e.jsx($,{value:p,onChange:l,placeholder:"123456",className:"flex-1"}),e.jsx(x,{onClick:C,isLoading:b,disabled:p.length!==6,children:"Verify"})]})]}),e.jsx("div",{className:"flex space-x-3",children:e.jsx(x,{onClick:D,variant:"secondary",className:"flex-1",children:"Cancel"})})]})]}):e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-6 border border-border-primary",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(Y,{className:`w-6 h-6 ${a?"text-green-400":"text-gray-400"}`}),e.jsxs("div",{children:[e.jsx("h4",{className:"text-lg font-medium text-white",children:"Two-Factor Authentication"}),e.jsx("p",{className:"text-gray-400 text-sm",children:"Add an extra layer of security to your account"})]})]}),e.jsx("div",{className:`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${a?"bg-green-500/20 text-green-400":"bg-gray-500/20 text-gray-400"}`,children:a?"Enabled":"Disabled"})]}),j&&e.jsx("div",{className:"mb-4 bg-red-500/20 border border-red-500/30 rounded-lg p-3",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(O,{className:"w-4 h-4 text-red-400"}),e.jsx("span",{className:"text-red-400 text-sm",children:j})]})}),m&&e.jsx("div",{className:"mb-4 bg-green-500/20 border border-green-500/30 rounded-lg p-3",children:e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(_,{className:"w-4 h-4 text-green-400"}),e.jsx("span",{className:"text-green-400 text-sm",children:m})]})}),e.jsxs("div",{className:"space-y-4",children:[e.jsx("p",{className:"text-gray-300 text-sm",children:a?"Two-factor authentication is currently enabled for your account. You can disable it below if needed.":"Enable two-factor authentication to add an extra layer of security to your account. You'll need an authenticator app like Google Authenticator or Authy."}),e.jsx("div",{className:"flex space-x-3",children:a?e.jsx(x,{onClick:A,isLoading:b,variant:"danger",children:"Disable 2FA"}):e.jsxs(x,{onClick:N,isLoading:b,variant:"primary",children:[e.jsx(Q,{className:"w-4 h-4 mr-2"}),"Enable 2FA"]})})]})]})},Ae=({isOpen:a,onClose:k,onSuccess:g})=>{const[d,b]=o.useState({currentPassword:"",newPassword:"",confirmPassword:""}),[c,j]=o.useState({current:!1,new:!1,confirm:!1}),[n,m]=o.useState(!1),[v,h]=o.useState(null),[f,p]=o.useState(null),l=(A,E)=>{b(D=>({...D,[A]:E})),h(null)},s=A=>E=>{l(A,E)},t=A=>{j(E=>({...E,[A]:!E[A]}))},i=()=>d.currentPassword?d.newPassword?d.newPassword.length<8?(h("New password must be at least 8 characters long"),!1):d.newPassword!==d.confirmPassword?(h("New passwords do not match"),!1):d.currentPassword===d.newPassword?(h("New password must be different from current password"),!1):!0:(h("New password is required"),!1):(h("Current password is required"),!1),u=async A=>{if(A.preventDefault(),!!i()){m(!0),h(null),p(null);try{const E=localStorage.getItem("auth_token"),D=await fetch("/api/auth/change-password",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${E}`},body:JSON.stringify({currentPassword:d.currentPassword,newPassword:d.newPassword})}),w=await D.json();if(!D.ok)throw new Error(w.error||"Failed to change password");p("Password changed successfully!"),setTimeout(()=>{g(),k(),N()},1500)}catch(E){h(E instanceof Error?E.message:"Failed to change password")}finally{m(!1)}}},N=()=>{b({currentPassword:"",newPassword:"",confirmPassword:""}),j({current:!1,new:!1,confirm:!1}),h(null),p(null)},C=()=>{N(),k()};return a?e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:e.jsxs("div",{className:"bg-background-secondary rounded-lg border border-border-primary w-full max-w-md",children:[e.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-border-primary",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(Q,{className:"w-5 h-5 text-primary-400"}),e.jsx("h3",{className:"text-lg font-semibold text-white",children:"Change Password"})]}),e.jsx("button",{onClick:C,className:"text-gray-400 hover:text-white transition-colors",children:e.jsx(q,{className:"w-5 h-5"})})]}),e.jsxs("form",{onSubmit:u,className:"p-6 space-y-4",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Current Password"}),e.jsxs("div",{className:"relative",children:[e.jsx($,{type:c.current?"text":"password",value:d.currentPassword,onChange:s("currentPassword"),placeholder:"Enter your current password",className:"pr-10",disabled:n}),e.jsx("button",{type:"button",onClick:()=>t("current"),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white",children:c.current?e.jsx(B,{className:"w-4 h-4"}):e.jsx(U,{className:"w-4 h-4"})})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"New Password"}),e.jsxs("div",{className:"relative",children:[e.jsx($,{type:c.new?"text":"password",value:d.newPassword,onChange:s("newPassword"),placeholder:"Enter your new password",className:"pr-10",disabled:n}),e.jsx("button",{type:"button",onClick:()=>t("new"),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white",children:c.new?e.jsx(B,{className:"w-4 h-4"}):e.jsx(U,{className:"w-4 h-4"})})]}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Password must be at least 8 characters long"})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Confirm New Password"}),e.jsxs("div",{className:"relative",children:[e.jsx($,{type:c.confirm?"text":"password",value:d.confirmPassword,onChange:s("confirmPassword"),placeholder:"Confirm your new password",className:"pr-10",disabled:n}),e.jsx("button",{type:"button",onClick:()=>t("confirm"),className:"absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-white",children:c.confirm?e.jsx(B,{className:"w-4 h-4"}):e.jsx(U,{className:"w-4 h-4"})})]})]}),v&&e.jsx("div",{className:"bg-red-500/10 border border-red-500/30 rounded-lg p-3",children:e.jsx("p",{className:"text-red-400 text-sm",children:v})}),f&&e.jsx("div",{className:"bg-green-500/10 border border-green-500/30 rounded-lg p-3",children:e.jsx("p",{className:"text-green-400 text-sm",children:f})}),e.jsxs("div",{className:"flex space-x-3 pt-4",children:[e.jsx(x,{type:"button",variant:"secondary",onClick:C,disabled:n,className:"flex-1",children:"Cancel"}),e.jsx(x,{type:"submit",variant:"primary",isLoading:n,disabled:n,className:"flex-1",children:n?"Changing...":"Change Password"})]})]})]})}):null},Ie=({isOpen:a,onClose:k,action:g})=>{const[d,b]=o.useState(""),[c,j]=o.useState(!1),[n,m]=o.useState(null),[v,h]=o.useState("confirm"),p={deactivate:{title:"Deactivate Account",description:"Your account will be temporarily disabled. You can reactivate it by logging in again.",confirmText:"DEACTIVATE",buttonText:"Deactivate Account",warningText:"This will temporarily disable your account and log you out.",endpoint:"/api/auth/deactivate-account"},delete:{title:"Delete Account",description:"This will permanently delete your account and all associated data. This action cannot be undone.",confirmText:"DELETE FOREVER",buttonText:"Delete Account Forever",warningText:"This will permanently delete all your data including study sets, flashcards, progress, and subscription information.",endpoint:"/api/auth/delete-account"}}[g],l=d===p.confirmText,s=async()=>{if(v==="confirm"){h("final");return}if(!l){m(`Please type "${p.confirmText}" to confirm`);return}j(!0),m(null);try{const u=localStorage.getItem("auth_token"),N=await fetch(p.endpoint,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${u}`},body:JSON.stringify({confirmation:d})}),C=await N.json();if(!N.ok)throw new Error(C.error||`Failed to ${g} account`);localStorage.removeItem("auth_token"),localStorage.removeItem("user_data"),window.location.href="/login"}catch(u){m(u instanceof Error?u.message:`Failed to ${g} account`)}finally{j(!1)}},t=()=>{b(""),m(null),h("confirm"),k()},i=()=>{h("confirm"),m(null)};return a?e.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:e.jsxs("div",{className:"bg-background-secondary rounded-lg border border-red-500/30 w-full max-w-md",children:[e.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-red-500/30",children:[e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(O,{className:"w-5 h-5 text-red-400"}),e.jsx("h3",{className:"text-lg font-semibold text-white",children:p.title})]}),e.jsx("button",{onClick:t,className:"text-gray-400 hover:text-white transition-colors",children:e.jsx(q,{className:"w-5 h-5"})})]}),e.jsx("div",{className:"p-6",children:v==="confirm"?e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"bg-red-500/10 border border-red-500/30 rounded-lg p-4",children:e.jsxs("div",{className:"flex items-start space-x-3",children:[e.jsx(O,{className:"w-5 h-5 text-red-400 mt-0.5 flex-shrink-0"}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-red-400 mb-2",children:"Warning"}),e.jsx("p",{className:"text-gray-300 text-sm",children:p.warningText})]})]})}),e.jsxs("div",{children:[e.jsx("p",{className:"text-gray-300 text-sm mb-4",children:p.description}),g==="delete"&&e.jsxs("div",{className:"space-y-2 text-sm text-gray-400",children:[e.jsx("p",{children:"This will delete:"}),e.jsxs("ul",{className:"list-disc list-inside space-y-1 ml-4",children:[e.jsx("li",{children:"All study sets and flashcards"}),e.jsx("li",{children:"Quiz history and progress"}),e.jsx("li",{children:"Account settings and preferences"}),e.jsx("li",{children:"Subscription and billing information"}),e.jsx("li",{children:"All uploaded documents"})]})]})]}),e.jsxs("div",{className:"flex space-x-3 pt-4",children:[e.jsx(x,{variant:"secondary",onClick:t,className:"flex-1",children:"Cancel"}),e.jsx(x,{variant:"danger",onClick:s,className:"flex-1",children:"Continue"})]})]}):e.jsxs("div",{className:"space-y-4",children:[e.jsx("div",{className:"bg-red-500/10 border border-red-500/30 rounded-lg p-4",children:e.jsxs("div",{className:"flex items-center space-x-3",children:[e.jsx(te,{className:"w-5 h-5 text-red-400"}),e.jsx("h4",{className:"font-medium text-red-400",children:"Final Confirmation"})]})}),e.jsxs("div",{children:[e.jsxs("p",{className:"text-gray-300 text-sm mb-4",children:["To confirm this action, please type ",e.jsx("span",{className:"font-mono font-bold text-red-400",children:p.confirmText})," in the box below:"]}),e.jsx($,{type:"text",value:d,onChange:u=>{b(u),m(null)},placeholder:p.confirmText,className:"font-mono",disabled:c})]}),n&&e.jsx("div",{className:"bg-red-500/10 border border-red-500/30 rounded-lg p-3",children:e.jsx("p",{className:"text-red-400 text-sm",children:n})}),e.jsxs("div",{className:"flex space-x-3 pt-4",children:[e.jsx(x,{variant:"secondary",onClick:i,disabled:c,className:"flex-1",children:"Back"}),e.jsx(x,{variant:"danger",onClick:s,isLoading:c,disabled:c||!l,className:"flex-1",children:c?"Processing...":p.buttonText})]})]})})]})}):null},Pe=[{id:"profile",label:"Profile",icon:he,description:"Manage your account information"},{id:"preferences",label:"Preferences",icon:pe,description:"Customize your experience"},{id:"notifications",label:"Notifications",icon:fe,description:"Control notification settings"},{id:"security",label:"Security",icon:Y,description:"Password and security settings"},{id:"subscription",label:"Subscription",icon:R,description:"Manage your subscription plan"},{id:"billing",label:"Billing",icon:R,description:"Payment history and invoices"},{id:"data",label:"Data Management",icon:ge,description:"Export, import, and manage your data"}],Te=()=>{const[a,k]=o.useState("profile"),[g,d]=o.useState(!1),[b,c]=o.useState(null),[j,n]=o.useState(null),{user:m,logout:v}=ue(),h=xe(),[f,p]=o.useState({name:(m==null?void 0:m.name)||"",email:(m==null?void 0:m.email)||"",bio:"",avatar:null}),[l,s]=o.useState({theme:"dark",language:"en",studyReminders:!0,autoSave:!0,defaultStudyMode:"flashcards",sessionDuration:30,difficultyLevel:we.MEDIUM}),[t,i]=o.useState({emailNotifications:!0,studyReminders:!0,weeklyProgress:!1,marketingEmails:!1,achievementNotifications:!0,streakReminders:!0}),[u,N]=o.useState({twoFactorEnabled:!1,loginNotifications:!0,sessionTimeout:30}),[C,A]=o.useState(!1),[E,D]=o.useState(!1),[w,I]=o.useState("deactivate");o.useEffect(()=>{(async()=>{try{d(!0);const S=localStorage.getItem("auth_token");if(!S){c("Authentication required");return}const y=await fetch("/api/user/preferences",{headers:{Authorization:`Bearer ${S}`}});if(!y.ok)throw new Error("Failed to load user preferences");const P=await y.json();if(P.success&&P.data){const F=P.data;s({theme:F.theme,language:F.language,studyReminders:F.study_reminders,autoSave:F.auto_save,defaultStudyMode:F.default_study_mode,sessionDuration:F.session_duration,difficultyLevel:F.difficulty_level})}}catch(S){c("Failed to load user settings"),console.error("Load user settings error:",S)}finally{d(!1)}})()},[]);const M=async()=>{d(!0),c(null),n(null);try{const r=new FormData;r.append("name",f.name),r.append("bio",f.bio),f.avatar&&r.append("avatar",f.avatar);const S=localStorage.getItem("auth_token"),y=await fetch("/api/user/profile",{method:"PUT",headers:{Authorization:`Bearer ${S}`},body:r});if(!y.ok)throw new Error("Failed to update profile");const P=await y.json();if(P.success)n("Profile updated successfully!");else throw new Error(P.error)}catch(r){c(r instanceof Error?r.message:"Failed to update profile")}finally{d(!1)}},z=async()=>{if(!g){d(!0),c(null),n(null);try{const r=localStorage.getItem("auth_token");if(!r){c("Authentication required"),d(!1);return}const S={theme:l.theme,language:l.language,study_reminders:l.studyReminders,auto_save:l.autoSave,default_study_mode:l.defaultStudyMode,session_duration:l.sessionDuration,difficulty_level:l.difficultyLevel},y=await fetch("/api/user/preferences",{method:"PUT",headers:{Authorization:`Bearer ${r}`,"Content-Type":"application/json"},body:JSON.stringify(S)});if(!y.ok)throw new Error("Failed to update preferences");const P=await y.json();if(P.success)n("Preferences updated successfully!");else throw new Error(P.error)}catch(r){c(r instanceof Error?r.message:"Failed to update preferences")}finally{d(!1)}}},T=async()=>{d(!0),c(null),n(null);try{const r=localStorage.getItem("auth_token"),S=await fetch("/api/user/notifications",{method:"PUT",headers:{Authorization:`Bearer ${r}`,"Content-Type":"application/json"},body:JSON.stringify(t)});if(!S.ok)throw new Error("Failed to update notification settings");const y=await S.json();if(y.success)n("Notification settings updated successfully!");else throw new Error(y.error)}catch(r){c(r instanceof Error?r.message:"Failed to update notification settings")}finally{d(!1)}},L=async()=>{try{d(!0),await v(),h("/login")}catch{c("Failed to logout. Please try again."),d(!1)}},X=()=>{var r,S;return e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Profile Information"}),e.jsxs("div",{className:"mb-6",children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Profile Picture"}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsx("div",{className:"w-20 h-20 bg-gradient-to-br from-primary-500 to-primary-600 rounded-full flex items-center justify-center text-white text-2xl font-bold",children:((S=(r=m==null?void 0:m.name)==null?void 0:r.charAt(0))==null?void 0:S.toUpperCase())||"U"}),e.jsxs("div",{className:"space-y-2",children:[e.jsxs(x,{variant:"secondary",size:"sm",onClick:()=>{const y=document.createElement("input");y.type="file",y.accept="image/*",y.onchange=P=>{var G;const F=(G=P.target.files)==null?void 0:G[0];F&&p({...f,avatar:F})},y.click()},children:[e.jsx(ye,{className:"w-4 h-4 mr-2"}),"Upload Photo"]}),e.jsx("p",{className:"text-xs text-gray-500",children:"JPG, PNG up to 5MB"})]})]})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsx($,{label:"Full Name",value:f.name,onChange:y=>p({...f,name:y}),placeholder:"Enter your full name"}),e.jsx($,{label:"Email Address",type:"email",value:f.email,onChange:y=>p({...f,email:y}),placeholder:"Enter your email",disabled:!0}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Bio (Optional)"}),e.jsx("textarea",{value:f.bio,onChange:y=>p({...f,bio:y.target.value}),placeholder:"Tell us about yourself...",rows:4,className:"w-full px-3 py-2 border-2 border-gray-600 rounded-md bg-background-secondary text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent"})]})]}),e.jsx("div",{className:"mt-6",children:e.jsx(x,{onClick:M,isLoading:g,children:"Save Profile"})})]})})},ae=()=>e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"App Preferences"}),e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Theme"}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsxs("button",{onClick:()=>s({...l,theme:"dark"}),className:`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${l.theme==="dark"?"border-primary-500 bg-primary-500/20 text-primary-400":"border-gray-600 text-gray-300 hover:border-gray-500"}`,children:[e.jsx(be,{className:"w-4 h-4"}),e.jsx("span",{children:"Dark"})]}),e.jsxs("button",{onClick:()=>s({...l,theme:"light"}),className:`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${l.theme==="light"?"border-primary-500 bg-primary-500/20 text-primary-400":"border-gray-600 text-gray-300 hover:border-gray-500"}`,disabled:!0,children:[e.jsx(je,{className:"w-4 h-4"}),e.jsx("span",{children:"Light (Coming Soon)"})]})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Language"}),e.jsxs("select",{value:l.language,onChange:r=>s({...l,language:r.target.value}),className:"w-full px-3 py-2 border-2 border-gray-600 rounded-md bg-background-secondary text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",children:[e.jsx("option",{value:"en",children:"English"}),e.jsx("option",{value:"es",disabled:!0,children:"Spanish (Coming Soon)"}),e.jsx("option",{value:"fr",disabled:!0,children:"French (Coming Soon)"})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Default Study Mode"}),e.jsxs("div",{className:"flex space-x-4",children:[e.jsx("button",{onClick:()=>s({...l,defaultStudyMode:"flashcards"}),className:`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${l.defaultStudyMode==="flashcards"?"border-primary-500 bg-primary-500/20 text-primary-400":"border-gray-600 text-gray-300 hover:border-gray-500"}`,children:e.jsx("span",{children:"Flashcards"})}),e.jsx("button",{onClick:()=>s({...l,defaultStudyMode:"quiz"}),className:`flex items-center space-x-2 px-4 py-2 rounded-lg border transition-colors ${l.defaultStudyMode==="quiz"?"border-primary-500 bg-primary-500/20 text-primary-400":"border-gray-600 text-gray-300 hover:border-gray-500"}`,children:e.jsx("span",{children:"Quiz"})})]})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-3",children:"Default Session Duration (minutes)"}),e.jsxs("select",{value:l.sessionDuration,onChange:r=>s({...l,sessionDuration:parseInt(r.target.value)}),className:"w-full px-3 py-2 border-2 border-gray-600 rounded-md bg-background-secondary text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",children:[e.jsx("option",{value:15,children:"15 minutes"}),e.jsx("option",{value:30,children:"30 minutes"}),e.jsx("option",{value:45,children:"45 minutes"}),e.jsx("option",{value:60,children:"1 hour"}),e.jsx("option",{value:90,children:"1.5 hours"})]})]}),e.jsx(ve,{value:l.difficultyLevel,onChange:r=>s({...l,difficultyLevel:r}),label:"Default Difficulty Level",className:"bg-background-secondary rounded-lg p-4"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-300",children:"Study Reminders"}),e.jsx("p",{className:"text-xs text-gray-500",children:"Get reminded to study regularly"})]}),e.jsx("button",{onClick:()=>s({...l,studyReminders:!l.studyReminders}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${l.studyReminders?"bg-primary-500":"bg-gray-600"}`,children:e.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${l.studyReminders?"translate-x-6":"translate-x-1"}`})})]}),e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-300",children:"Auto-save"}),e.jsx("p",{className:"text-xs text-gray-500",children:"Automatically save your progress"})]}),e.jsx("button",{onClick:()=>s({...l,autoSave:!l.autoSave}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${l.autoSave?"bg-primary-500":"bg-gray-600"}`,children:e.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${l.autoSave?"translate-x-6":"translate-x-1"}`})})]})]})]}),e.jsx("div",{className:"mt-6",children:e.jsx(x,{onClick:z,isLoading:g,children:"Save Preferences"})})]})}),re=()=>e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Notification Settings"}),e.jsx("div",{className:"space-y-4",children:Object.entries(t).map(([r,S])=>e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-300 capitalize",children:r.replace(/([A-Z])/g," $1").trim()}),e.jsxs("p",{className:"text-xs text-gray-500",children:[r==="emailNotifications"&&"Receive important updates via email",r==="studyReminders"&&"Get reminded when it's time to study",r==="weeklyProgress"&&"Weekly summary of your study progress",r==="marketingEmails"&&"Product updates and tips",r==="achievementNotifications"&&"Get notified when you unlock achievements",r==="streakReminders"&&"Reminders to maintain your study streak"]})]}),e.jsx("button",{onClick:()=>i({...t,[r]:!S}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${S?"bg-primary-500":"bg-gray-600"}`,children:e.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${S?"translate-x-6":"translate-x-1"}`})})]},r))}),e.jsx("div",{className:"mt-6",children:e.jsx(x,{onClick:T,isLoading:g,children:"Save Notification Settings"})})]})}),ne=()=>e.jsx("div",{className:"space-y-6",children:e.jsxs("div",{children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Security Settings"}),e.jsxs("div",{className:"space-y-6",children:[e.jsx(Ee,{enabled:u.twoFactorEnabled,onToggle:r=>N({...u,twoFactorEnabled:r})}),e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-4 border border-border-primary",children:[e.jsx("h4",{className:"font-medium text-white mb-4",children:"Session Management"}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("label",{className:"text-sm font-medium text-gray-300",children:"Login Notifications"}),e.jsx("p",{className:"text-xs text-gray-500",children:"Get notified when someone logs into your account"})]}),e.jsx("button",{onClick:()=>N({...u,loginNotifications:!u.loginNotifications}),className:`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${u.loginNotifications?"bg-primary-500":"bg-gray-600"}`,children:e.jsx("span",{className:`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${u.loginNotifications?"translate-x-6":"translate-x-1"}`})})]}),e.jsxs("div",{children:[e.jsx("label",{className:"block text-sm font-medium text-gray-300 mb-2",children:"Session Timeout (minutes)"}),e.jsxs("select",{value:u.sessionTimeout,onChange:r=>N({...u,sessionTimeout:parseInt(r.target.value)}),className:"w-full px-3 py-2 border-2 border-gray-600 rounded-md bg-background-secondary text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent",children:[e.jsx("option",{value:15,children:"15 minutes"}),e.jsx("option",{value:30,children:"30 minutes"}),e.jsx("option",{value:60,children:"1 hour"}),e.jsx("option",{value:120,children:"2 hours"}),e.jsx("option",{value:480,children:"8 hours"}),e.jsx("option",{value:1440,children:"1 day"}),e.jsx("option",{value:10080,children:"1 week"}),e.jsx("option",{value:20160,children:"2 weeks"}),e.jsx("option",{value:30240,children:"3 weeks"}),e.jsx("option",{value:40320,children:"4 weeks"}),e.jsx("option",{value:50400,children:"5 weeks"}),e.jsx("option",{value:60480,children:"6 weeks"}),e.jsx("option",{value:70560,children:"7 weeks"}),e.jsx("option",{value:80640,children:"8 weeks"}),e.jsx("option",{value:0,children:"Never expire"})]})]})]})]}),e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-4 border border-border-primary",children:[e.jsxs("div",{className:"flex items-center space-x-3 mb-3",children:[e.jsx(Q,{className:"w-5 h-5 text-primary-400"}),e.jsx("h4",{className:"font-medium text-white",children:"Change Password"})]}),e.jsx("p",{className:"text-gray-400 text-sm mb-4",children:"Update your password to keep your account secure"}),e.jsx(x,{variant:"secondary",onClick:()=>A(!0),children:"Change Password"})]}),e.jsxs("div",{className:"bg-background-tertiary rounded-lg p-4 border border-border-primary",children:[e.jsxs("div",{className:"flex items-center space-x-3 mb-3",children:[e.jsx(Ne,{className:"w-5 h-5 text-orange-400"}),e.jsx("h4",{className:"font-medium text-white",children:"Sign Out"})]}),e.jsx("p",{className:"text-gray-400 text-sm mb-4",children:"Sign out of your account on this device"}),e.jsx(x,{variant:"secondary",onClick:L,disabled:g,children:g?"Signing Out...":"Sign Out"})]}),e.jsxs("div",{className:"bg-red-500/10 rounded-lg p-4 border border-red-500/30",children:[e.jsxs("div",{className:"flex items-center space-x-3 mb-3",children:[e.jsx(O,{className:"w-5 h-5 text-red-400"}),e.jsx("h4",{className:"font-medium text-white",children:"Danger Zone"})]}),e.jsxs("div",{className:"space-y-4",children:[e.jsxs("div",{children:[e.jsx("h5",{className:"font-medium text-red-400 mb-2",children:"Account Deactivation"}),e.jsx("p",{className:"text-gray-400 text-sm mb-3",children:"Temporarily deactivate your account. You can reactivate it later."}),e.jsx(x,{variant:"secondary",size:"sm",onClick:()=>{I("deactivate"),D(!0)},children:"Deactivate Account"})]}),e.jsxs("div",{children:[e.jsx("h5",{className:"font-medium text-red-400 mb-2",children:"Account Deletion"}),e.jsx("p",{className:"text-gray-400 text-sm mb-3",children:"Permanently delete your account and all associated data. This action cannot be undone."}),e.jsx(x,{variant:"danger",size:"sm",onClick:()=>{I("delete"),D(!0)},children:"Delete Account"})]})]})]})]})]})}),ie=()=>e.jsx(Se,{}),le=()=>e.jsx(Ce,{}),ce=()=>e.jsx(ke,{}),oe=()=>{switch(a){case"profile":return X();case"preferences":return ae();case"notifications":return re();case"security":return ne();case"subscription":return ie();case"billing":return le();case"data":return ce();default:return X()}};return e.jsxs("div",{className:"min-h-screen bg-background-primary text-white",children:[e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-white mb-2",children:"Settings"}),e.jsx("p",{className:"text-gray-400",children:"Manage your account and preferences"})]}),b&&e.jsxs("div",{className:"mb-6 bg-red-500/20 border border-red-500/30 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(O,{className:"w-5 h-5 text-red-400"}),e.jsx("span",{className:"text-red-400 font-medium",children:"Error"})]}),e.jsx("p",{className:"text-red-300 mt-1",children:b}),e.jsx(x,{onClick:()=>c(null),variant:"secondary",size:"sm",className:"mt-2",children:"Dismiss"})]}),j&&e.jsxs("div",{className:"mb-6 bg-green-500/20 border border-green-500/30 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(_,{className:"w-5 h-5 text-green-400"}),e.jsx("span",{className:"text-green-400 font-medium",children:"Success"})]}),e.jsx("p",{className:"text-green-300 mt-1",children:j}),e.jsx(x,{onClick:()=>n(null),variant:"secondary",size:"sm",className:"mt-2",children:"Dismiss"})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[e.jsx("div",{className:"lg:col-span-1",children:e.jsx("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:e.jsx("nav",{className:"space-y-2",children:Pe.map(r=>{const S=r.icon,y=a===r.id;return e.jsxs("button",{onClick:()=>k(r.id),className:`
                        w-full flex items-center space-x-3 px-3 py-3 rounded-lg text-left
                        transition-all duration-200
                        ${y?"bg-primary-500/20 text-primary-400 border border-primary-500/30":"text-gray-300 hover:bg-background-tertiary hover:text-white"}
                      `,children:[e.jsx(S,{className:"w-5 h-5"}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("span",{className:"font-medium block",children:r.label}),e.jsx("span",{className:"text-xs text-gray-500 block truncate",children:r.description})]})]},r.id)})})})}),e.jsx("div",{className:"lg:col-span-3",children:e.jsx(Z.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.3},className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:oe()},a)})]})]}),e.jsx(Ae,{isOpen:C,onClose:()=>A(!1),onSuccess:()=>{console.log("Password changed successfully")}}),e.jsx(Ie,{isOpen:E,onClose:()=>D(!1),action:w})]})};export{Te as SettingsPage};
