import{r as j,j as s,a as G,B as T,I as ae}from"./index-e6f11275.js";import{u as le}from"./index-6232208d.js";import{u as E}from"./documentStore-0ecab0bf.js";const ce=()=>{const[r,t]=j.useState(!1),[n,a]=j.useState([]),{uploadDocument:d,setUploadProgress:b}=E(),N=j.useCallback(async x=>{t(!0),a([]);const I=[];for(const f of x)try{if(f.size>50*1024*1024){I.push(`${f.name}: File size exceeds 50MB limit`);continue}if(!["application/pdf","application/vnd.openxmlformats-officedocument.wordprocessingml.document","text/plain","application/vnd.openxmlformats-officedocument.presentationml.presentation"].includes(f.type)){I.push(`${f.name}: Unsupported file type. Please upload PDF, DOCX, TXT, or PPTX files.`);continue}b(f.name,0),await d(f),b(f.name,100)}catch(m){I.push(`${f.name}: ${m instanceof Error?m.message:"Unknown error"}`)}a(I),t(!1)},[d,b]),{getRootProps:y,getInputProps:S,isDragActive:w}=le({onDrop:N,accept:{"application/pdf":[".pdf"],"application/vnd.openxmlformats-officedocument.wordprocessingml.document":[".docx"],"text/plain":[".txt"],"application/vnd.openxmlformats-officedocument.presentationml.presentation":[".pptx"]},multiple:!0,maxFiles:10,disabled:r});return s.jsxs("div",{className:"space-y-4",children:[s.jsxs("div",{...y(),className:`
          border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors
          ${w?"border-primary-500 bg-primary-500/10":"border-gray-600 hover:border-primary-500 hover:bg-primary-500/5"}
          ${r?"opacity-50 cursor-not-allowed":""}
        `,children:[s.jsx("input",{...S()}),s.jsxs("div",{className:"space-y-2",children:[s.jsx("svg",{className:"mx-auto h-12 w-12 text-gray-400",stroke:"currentColor",fill:"none",viewBox:"0 0 48 48",children:s.jsx("path",{d:"M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"})}),w?s.jsx("p",{className:"text-primary-400",children:"Drop the files here..."}):s.jsxs("div",{children:[s.jsxs("p",{className:"text-gray-300",children:["Drag & drop files here, or"," ",s.jsx("span",{className:"text-primary-500 font-medium",children:"browse"})]}),s.jsx("p",{className:"text-sm text-gray-500 mt-1",children:"Supports PDF, DOCX, TXT, PPTX (max 50MB each)"})]})]})]}),r&&s.jsxs("div",{className:"bg-background-secondary rounded-lg p-4",children:[s.jsx("p",{className:"text-sm text-gray-300 mb-2",children:"Uploading files..."}),s.jsx("div",{className:"space-y-2"})]}),n.length>0&&s.jsxs("div",{className:"bg-red-900/20 border border-red-700 rounded-lg p-4",children:[s.jsx("h4",{className:"text-red-400 font-medium mb-2",children:"Upload Errors:"}),s.jsx("ul",{className:"text-sm text-red-300 space-y-1",children:n.map((x,I)=>s.jsxs("li",{children:["• ",x]},I))})]})]})};function L(){return L=Object.assign?Object.assign.bind():function(r){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var a in n)({}).hasOwnProperty.call(n,a)&&(r[a]=n[a])}return r},L.apply(null,arguments)}function B(r){if(r===void 0)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return r}function F(r,t){return F=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(n,a){return n.__proto__=a,n},F(r,t)}function de(r,t){r.prototype=Object.create(t.prototype),r.prototype.constructor=r,F(r,t)}var q=Number.isNaN||function(t){return typeof t=="number"&&t!==t};function me(r,t){return!!(r===t||q(r)&&q(t))}function ue(r,t){if(r.length!==t.length)return!1;for(var n=0;n<r.length;n++)if(!me(r[n],t[n]))return!1;return!0}function M(r,t){t===void 0&&(t=ue);var n,a=[],d,b=!1;function N(){for(var y=[],S=0;S<arguments.length;S++)y[S]=arguments[S];return b&&n===this&&t(y,a)||(d=r.apply(this,y),b=!0,n=this,a=y),d}return N}var he=typeof performance=="object"&&typeof performance.now=="function",H=he?function(){return performance.now()}:function(){return Date.now()};function X(r){cancelAnimationFrame(r.id)}function pe(r,t){var n=H();function a(){H()-n>=t?r.call(null):d.id=requestAnimationFrame(a)}var d={id:requestAnimationFrame(a)};return d}var R=-1;function K(r){if(r===void 0&&(r=!1),R===-1||r){var t=document.createElement("div"),n=t.style;n.width="50px",n.height="50px",n.overflow="scroll",document.body.appendChild(t),R=t.offsetWidth-t.clientWidth,document.body.removeChild(t)}return R}var C=null;function V(r){if(r===void 0&&(r=!1),C===null||r){var t=document.createElement("div"),n=t.style;n.width="50px",n.height="50px",n.overflow="scroll",n.direction="rtl";var a=document.createElement("div"),d=a.style;return d.width="100px",d.height="100px",t.appendChild(a),document.body.appendChild(t),t.scrollLeft>0?C="positive-descending":(t.scrollLeft=1,t.scrollLeft===0?C="negative":C="positive-ascending"),document.body.removeChild(t),C}return C}var fe=150,ge=function(t,n){return t};function xe(r){var t,n=r.getItemOffset,a=r.getEstimatedTotalSize,d=r.getItemSize,b=r.getOffsetForIndexAndAlignment,N=r.getStartIndexForOffset,y=r.getStopIndexForStartIndex,S=r.initInstanceProps,w=r.shouldResetStyleCacheOnItemSizeChange,x=r.validateProps;return t=function(I){de(f,I);function f(p){var e;return e=I.call(this,p)||this,e._instanceProps=S(e.props,B(e)),e._outerRef=void 0,e._resetIsScrollingTimeoutId=null,e.state={instance:B(e),isScrolling:!1,scrollDirection:"forward",scrollOffset:typeof e.props.initialScrollOffset=="number"?e.props.initialScrollOffset:0,scrollUpdateWasRequested:!1},e._callOnItemsRendered=void 0,e._callOnItemsRendered=M(function(i,o,l,u){return e.props.onItemsRendered({overscanStartIndex:i,overscanStopIndex:o,visibleStartIndex:l,visibleStopIndex:u})}),e._callOnScroll=void 0,e._callOnScroll=M(function(i,o,l){return e.props.onScroll({scrollDirection:i,scrollOffset:o,scrollUpdateWasRequested:l})}),e._getItemStyle=void 0,e._getItemStyle=function(i){var o=e.props,l=o.direction,u=o.itemSize,g=o.layout,c=e._getItemStyleCache(w&&u,w&&g,w&&l),h;if(c.hasOwnProperty(i))h=c[i];else{var v=n(e.props,i,e._instanceProps),D=d(e.props,i,e._instanceProps),z=l==="horizontal"||g==="horizontal",P=l==="rtl",_=z?v:0;c[i]=h={position:"absolute",left:P?void 0:_,right:P?_:void 0,top:z?0:v,height:z?"100%":D,width:z?D:"100%"}}return h},e._getItemStyleCache=void 0,e._getItemStyleCache=M(function(i,o,l){return{}}),e._onScrollHorizontal=function(i){var o=i.currentTarget,l=o.clientWidth,u=o.scrollLeft,g=o.scrollWidth;e.setState(function(c){if(c.scrollOffset===u)return null;var h=e.props.direction,v=u;if(h==="rtl")switch(V()){case"negative":v=-u;break;case"positive-descending":v=g-l-u;break}return v=Math.max(0,Math.min(v,g-l)),{isScrolling:!0,scrollDirection:c.scrollOffset<v?"forward":"backward",scrollOffset:v,scrollUpdateWasRequested:!1}},e._resetIsScrollingDebounced)},e._onScrollVertical=function(i){var o=i.currentTarget,l=o.clientHeight,u=o.scrollHeight,g=o.scrollTop;e.setState(function(c){if(c.scrollOffset===g)return null;var h=Math.max(0,Math.min(g,u-l));return{isScrolling:!0,scrollDirection:c.scrollOffset<h?"forward":"backward",scrollOffset:h,scrollUpdateWasRequested:!1}},e._resetIsScrollingDebounced)},e._outerRefSetter=function(i){var o=e.props.outerRef;e._outerRef=i,typeof o=="function"?o(i):o!=null&&typeof o=="object"&&o.hasOwnProperty("current")&&(o.current=i)},e._resetIsScrollingDebounced=function(){e._resetIsScrollingTimeoutId!==null&&X(e._resetIsScrollingTimeoutId),e._resetIsScrollingTimeoutId=pe(e._resetIsScrolling,fe)},e._resetIsScrolling=function(){e._resetIsScrollingTimeoutId=null,e.setState({isScrolling:!1},function(){e._getItemStyleCache(-1,null)})},e}f.getDerivedStateFromProps=function(e,i){return ve(e,i),x(e),null};var m=f.prototype;return m.scrollTo=function(e){e=Math.max(0,e),this.setState(function(i){return i.scrollOffset===e?null:{scrollDirection:i.scrollOffset<e?"forward":"backward",scrollOffset:e,scrollUpdateWasRequested:!0}},this._resetIsScrollingDebounced)},m.scrollToItem=function(e,i){i===void 0&&(i="auto");var o=this.props,l=o.itemCount,u=o.layout,g=this.state.scrollOffset;e=Math.max(0,Math.min(e,l-1));var c=0;if(this._outerRef){var h=this._outerRef;u==="vertical"?c=h.scrollWidth>h.clientWidth?K():0:c=h.scrollHeight>h.clientHeight?K():0}this.scrollTo(b(this.props,e,i,g,this._instanceProps,c))},m.componentDidMount=function(){var e=this.props,i=e.direction,o=e.initialScrollOffset,l=e.layout;if(typeof o=="number"&&this._outerRef!=null){var u=this._outerRef;i==="horizontal"||l==="horizontal"?u.scrollLeft=o:u.scrollTop=o}this._callPropsCallbacks()},m.componentDidUpdate=function(){var e=this.props,i=e.direction,o=e.layout,l=this.state,u=l.scrollOffset,g=l.scrollUpdateWasRequested;if(g&&this._outerRef!=null){var c=this._outerRef;if(i==="horizontal"||o==="horizontal")if(i==="rtl")switch(V()){case"negative":c.scrollLeft=-u;break;case"positive-ascending":c.scrollLeft=u;break;default:var h=c.clientWidth,v=c.scrollWidth;c.scrollLeft=v-h-u;break}else c.scrollLeft=u;else c.scrollTop=u}this._callPropsCallbacks()},m.componentWillUnmount=function(){this._resetIsScrollingTimeoutId!==null&&X(this._resetIsScrollingTimeoutId)},m.render=function(){var e=this.props,i=e.children,o=e.className,l=e.direction,u=e.height,g=e.innerRef,c=e.innerElementType,h=e.innerTagName,v=e.itemCount,D=e.itemData,z=e.itemKey,P=z===void 0?ge:z,_=e.layout,Z=e.outerElementType,ee=e.outerTagName,te=e.style,re=e.useIsScrolling,se=e.width,U=this.state.isScrolling,k=l==="horizontal"||_==="horizontal",ne=k?this._onScrollHorizontal:this._onScrollVertical,W=this._getRangeToRender(),ie=W[0],oe=W[1],A=[];if(v>0)for(var O=ie;O<=oe;O++)A.push(j.createElement(i,{data:D,key:P(O,D),index:O,isScrolling:re?U:void 0,style:this._getItemStyle(O)}));var $=a(this.props,this._instanceProps);return j.createElement(Z||ee||"div",{className:o,onScroll:ne,ref:this._outerRefSetter,style:L({position:"relative",height:u,width:se,overflow:"auto",WebkitOverflowScrolling:"touch",willChange:"transform",direction:l},te)},j.createElement(c||h||"div",{children:A,ref:g,style:{height:k?"100%":$,pointerEvents:U?"none":void 0,width:k?$:"100%"}}))},m._callPropsCallbacks=function(){if(typeof this.props.onItemsRendered=="function"){var e=this.props.itemCount;if(e>0){var i=this._getRangeToRender(),o=i[0],l=i[1],u=i[2],g=i[3];this._callOnItemsRendered(o,l,u,g)}}if(typeof this.props.onScroll=="function"){var c=this.state,h=c.scrollDirection,v=c.scrollOffset,D=c.scrollUpdateWasRequested;this._callOnScroll(h,v,D)}},m._getRangeToRender=function(){var e=this.props,i=e.itemCount,o=e.overscanCount,l=this.state,u=l.isScrolling,g=l.scrollDirection,c=l.scrollOffset;if(i===0)return[0,0,0,0];var h=N(this.props,c,this._instanceProps),v=y(this.props,h,c,this._instanceProps),D=!u||g==="backward"?Math.max(1,o):1,z=!u||g==="forward"?Math.max(1,o):1;return[Math.max(0,h-D),Math.max(0,Math.min(i-1,v+z)),h,v]},f}(j.PureComponent),t.defaultProps={direction:"ltr",itemData:void 0,layout:"vertical",overscanCount:2,useIsScrolling:!1},t}var ve=function(t,n){t.children,t.direction,t.height,t.layout,t.innerTagName,t.outerTagName,t.width,n.instance},ye=xe({getItemOffset:function(t,n){var a=t.itemSize;return n*a},getItemSize:function(t,n){var a=t.itemSize;return a},getEstimatedTotalSize:function(t){var n=t.itemCount,a=t.itemSize;return a*n},getOffsetForIndexAndAlignment:function(t,n,a,d,b,N){var y=t.direction,S=t.height,w=t.itemCount,x=t.itemSize,I=t.layout,f=t.width,m=y==="horizontal"||I==="horizontal",p=m?f:S,e=Math.max(0,w*x-p),i=Math.min(e,n*x),o=Math.max(0,n*x-p+x+N);switch(a==="smart"&&(d>=o-p&&d<=i+p?a="auto":a="center"),a){case"start":return i;case"end":return o;case"center":{var l=Math.round(o+(i-o)/2);return l<Math.ceil(p/2)?0:l>e+Math.floor(p/2)?e:l}case"auto":default:return d>=o&&d<=i?d:d<o?o:i}},getStartIndexForOffset:function(t,n){var a=t.itemCount,d=t.itemSize;return Math.max(0,Math.min(a-1,Math.floor(n/d)))},getStopIndexForStartIndex:function(t,n,a){var d=t.direction,b=t.height,N=t.itemCount,y=t.itemSize,S=t.layout,w=t.width,x=d==="horizontal"||S==="horizontal",I=n*y,f=x?w:b,m=Math.ceil((f+a-I)/y);return Math.max(0,Math.min(N-1,n+m-1))},initInstanceProps:function(t){},shouldResetStyleCacheOnItemSizeChange:!0,validateProps:function(t){t.itemSize}});const Q=({document:r})=>{const{selectedDocuments:t,toggleDocumentSelection:n,deleteDocument:a}=E(),{confirm:d,alert:b}=G(),[N,y]=j.useState(!1),S=t.has(r.id),w=async()=>{if(await d({title:"Delete Document",message:`Are you sure you want to delete "${r.filename}"? This action cannot be undone.`,confirmText:"Delete",cancelText:"Cancel",variant:"danger"})){y(!0);try{await a(r.id)}catch(p){console.error("Delete error:",p),await b({title:"Delete Error",message:"Failed to delete document. Please try again.",variant:"error"})}finally{y(!1)}}},x=m=>{if(m===0)return"0 Bytes";const p=1024,e=["Bytes","KB","MB","GB"],i=Math.floor(Math.log(m)/Math.log(p));return parseFloat((m/Math.pow(p,i)).toFixed(2))+" "+e[i]},I=m=>new Date(m).toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"}),f=m=>({pdf:"📄",docx:"📝",txt:"📃",pptx:"📊"})[m]||"📄";return s.jsxs("div",{className:`
        bg-background-secondary rounded-lg p-4 border-2 transition-all cursor-pointer
        ${S?"border-primary-500 bg-primary-500/10":"border-gray-700 hover:border-gray-600"}
      `,onClick:()=>n(r.id),children:[s.jsxs("div",{className:"flex items-start justify-between mb-3",children:[s.jsxs("div",{className:"flex items-center space-x-2 flex-1 min-w-0",children:[s.jsx("span",{className:"text-2xl",children:f(r.file_type)}),s.jsxs("div",{className:"min-w-0 flex-1",children:[s.jsx("h3",{className:"text-white font-medium truncate",title:r.filename,children:r.filename}),s.jsxs("p",{className:"text-sm text-gray-400",children:[r.file_type.toUpperCase()," • ",x(r.file_size)]})]})]}),s.jsx("div",{className:`
            w-5 h-5 rounded border-2 flex items-center justify-center
            ${S?"bg-primary-500 border-primary-500":"border-gray-500"}
          `,children:S&&s.jsx("svg",{className:"w-3 h-3 text-white",fill:"currentColor",viewBox:"0 0 20 20",children:s.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})})})]}),!r.is_processed&&s.jsx("div",{className:"mb-3",children:s.jsxs("div",{className:"flex items-center space-x-2 text-yellow-400",children:[s.jsx("div",{className:"w-2 h-2 bg-yellow-400 rounded-full animate-pulse"}),s.jsx("span",{className:"text-sm",children:"Processing..."})]})}),r.processing_error&&s.jsx("div",{className:"mb-3",children:s.jsxs("div",{className:"text-red-400 text-sm",children:["⚠️ Processing failed: ",r.processing_error]})}),s.jsxs("div",{className:"text-xs text-gray-500 mb-3",children:["Uploaded ",I(r.uploaded_at)]}),s.jsx("div",{className:"flex justify-end space-x-2",onClick:m=>m.stopPropagation(),children:s.jsx(T,{onClick:w,variant:"danger",size:"sm",isLoading:N,children:"Delete"})})]})},Y=j.memo(({index:r,style:t,data:n})=>{const{documents:a}=n,d=a[r];return s.jsx("div",{style:t,children:s.jsx("div",{className:"px-2 py-1",children:s.jsx(Q,{document:d})})})});Y.displayName="DocumentItem";const J=j.memo(({documents:r,height:t=400,itemHeight:n=120})=>{const a=j.useMemo(()=>({documents:r}),[r]);return r.length===0?s.jsxs("div",{className:"text-center py-12",children:[s.jsx("div",{className:"text-gray-400 mb-4",children:"No documents uploaded yet"}),s.jsx("p",{className:"text-sm text-gray-500",children:"Upload your first document to get started with AI-powered study materials."})]}):r.length<=10?s.jsx("div",{className:"space-y-4",children:r.map(d=>s.jsx(Q,{document:d},d.id))}):s.jsx("div",{className:"border border-gray-600 rounded-lg overflow-hidden",children:s.jsx(ye,{height:t,width:"100%",itemCount:r.length,itemSize:n,itemData:a,overscanCount:5,children:Y})})});J.displayName="VirtualizedDocumentList";const Se=j.memo(()=>{const{documents:r,selectedDocuments:t,isLoading:n,fetchDocuments:a,searchDocuments:d,clearSelection:b,selectAll:N,deleteDocument:y}=E(),{confirm:S,alert:w}=G(),[x,I]=j.useState(""),[f,m]=j.useState(null),[p,e]=j.useState(!1);j.useEffect(()=>{a()},[a]);const i=async()=>{if(x.trim().length<2){m(null);return}e(!0);try{const c=await d(x.trim());m(c)}catch(c){console.error("Search error:",c)}finally{e(!1)}},o=()=>{I(""),m(null)},l=async()=>{if(!(t.size===0||!await S({title:"Delete Documents",message:`Are you sure you want to delete ${t.size} document(s)? This action cannot be undone.`,confirmText:"Delete",cancelText:"Cancel",variant:"danger"})))try{const h=Array.from(t).map(v=>y(v));await Promise.all(h),b()}catch(h){console.error("Bulk delete error:",h),await w({title:"Delete Error",message:"Some documents could not be deleted. Please try again.",variant:"error"})}},u=f||r,g=t.size>0;return n&&r.length===0?s.jsx("div",{className:"flex items-center justify-center py-12",children:s.jsx("div",{className:"text-gray-400",children:"Loading documents..."})}):s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"flex flex-col sm:flex-row gap-4",children:[s.jsx("div",{className:"flex-1",children:s.jsxs("div",{className:"flex gap-2",children:[s.jsx(ae,{placeholder:"Search documents...",value:x,onChange:I}),s.jsx(T,{onClick:i,isLoading:p,disabled:x.trim().length<2,children:"Search"}),f&&s.jsx(T,{onClick:o,variant:"secondary",children:"Clear"})]})}),g&&s.jsxs("div",{className:"flex gap-2",children:[s.jsx(T,{onClick:N,variant:"secondary",size:"sm",children:"Select All"}),s.jsxs(T,{onClick:b,variant:"secondary",size:"sm",children:["Clear (",t.size,")"]}),s.jsx(T,{onClick:l,variant:"danger",size:"sm",children:"Delete Selected"})]})]}),f&&s.jsxs("div",{className:"text-sm text-gray-400",children:["Found ",f.length,' document(s) matching "',x,'"']}),s.jsx(J,{documents:u,height:600})]})}),we=()=>s.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:s.jsxs("div",{className:"space-y-8",children:[s.jsxs("div",{children:[s.jsx("h1",{className:"text-3xl font-bold text-white",children:"Documents"}),s.jsx("p",{className:"mt-2 text-gray-400",children:"Upload and manage your study documents. Supported formats: PDF, DOCX, TXT, PPTX"})]}),s.jsxs("div",{className:"bg-background-secondary rounded-lg p-6",children:[s.jsx("h2",{className:"text-xl font-semibold text-white mb-4",children:"Upload Documents"}),s.jsx(ce,{})]}),s.jsxs("div",{children:[s.jsx("h2",{className:"text-xl font-semibold text-white mb-4",children:"Your Documents"}),s.jsx(Se,{})]})]})});export{we as DocumentsPage};
