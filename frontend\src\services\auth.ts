import { createClient } from "@supabase/supabase-js";
import { <PERSON>r<PERSON>ro<PERSON>le, AuthResult } from "../../../shared/types";

const supabaseUrl = import.meta.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env.VITE_SUPABASE_ANON_KEY;

export const supabase = createClient(supabaseUrl, supabaseAnonKey);

class AuthService {
  // Helper methods for token storage
  private setToken(token: string, rememberMe: boolean = true): void {
    // Clear any existing tokens from both storages
    localStorage.removeItem("auth_token");
    sessionStorage.removeItem("auth_token");

    // Store in appropriate storage based on user preference
    if (rememberMe) {
      localStorage.setItem("auth_token", token);
    } else {
      sessionStorage.setItem("auth_token", token);
    }
  }

  private getToken(): string | null {
    // Check localStorage first, then sessionStorage
    return (
      localStorage.getItem("auth_token") || sessionStorage.getItem("auth_token")
    );
  }

  private clearToken(): void {
    localStorage.removeItem("auth_token");
    sessionStorage.removeItem("auth_token");
  }

  async signUp(
    email: string,
    password: string,
    name?: string
  ): Promise<AuthResult> {
    try {
      const response = await fetch("/api/auth/signup", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email, password, name }),
      });
      const result = await response.json();
      if (result.success && result.token) {
        // Default to persistent storage for signup
        this.setToken(result.token, true);
      }
      return result;
    } catch {
      return { success: false, error: "Network error during signup" };
    }
  }

  async signIn(
    email: string,
    password: string,
    rememberMe: boolean = true
  ): Promise<AuthResult> {
    try {
      const response = await fetch("/api/auth/login", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email, password }),
      });
      const result = await response.json();
      if (result.success && result.token) {
        this.setToken(result.token, rememberMe);
      }
      return result;
    } catch {
      return { success: false, error: "Network error during login" };
    }
  }

  async signOut(): Promise<void> {
    try {
      const token = this.getToken();
      if (token) {
        await fetch("/api/auth/logout", {
          method: "POST",
          headers: { Authorization: `Bearer ${token}` },
        });
      }
    } finally {
      this.clearToken();
    }
  }

  async getCurrentUser(): Promise<UserProfile | null> {
    try {
      const token = this.getToken();
      if (!token) {
        return null;
      }

      const response = await fetch("/api/auth/user", {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          console.log("Token expired or invalid, clearing...");
          this.clearToken();
        }
        return null;
      }

      const result = await response.json();

      if (result.success && result.data) {
        return result.data;
      }

      return null;
    } catch (error) {
      console.error("getCurrentUser error:", error);
      return null;
    }
  }

  isAuthenticated(): boolean {
    return !!this.getToken();
  }

  async signInWithGoogle(): Promise<{ error?: string }> {
    try {
      const { error } = await supabase.auth.signInWithOAuth({
        provider: "google",
        options: {
          redirectTo: `${window.location.origin}/auth/callback`,
        },
      });

      if (error) {
        return { error: error.message };
      }

      return {};
    } catch (error) {
      return { error: "Failed to sign in with Google" };
    }
  }

  async handleOAuthCallback(): Promise<AuthResult> {
    try {
      const { data, error } = await supabase.auth.getSession();

      if (error) {
        return { success: false, error: error.message };
      }

      if (data.session) {
        // Store the access token
        this.setToken(data.session.access_token, true);

        // Get or create user profile
        const user = data.session.user;
        if (user) {
          // Call backend to create/get user profile
          const response = await fetch("/api/auth/oauth-user", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${data.session.access_token}`,
            },
            body: JSON.stringify({
              id: user.id,
              email: user.email,
              name: user.user_metadata?.full_name || user.user_metadata?.name,
              provider: "google",
            }),
          });

          const result = await response.json();

          if (result.success) {
            return { success: true, user: result.user };
          } else {
            return {
              success: false,
              error: result.error || "Failed to create user profile",
            };
          }
        }
      }

      return { success: false, error: "No session found" };
    } catch (error) {
      return { success: false, error: "Failed to handle OAuth callback" };
    }
  }
}

export const authService = new AuthService();
