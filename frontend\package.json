{"name": "chewy-ai-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "vitest"}, "dependencies": {"@tanstack/react-query": "^4.28.0", "@types/react-window": "^1.8.8", "framer-motion": "^10.12.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.3.8", "react-icons": "^4.8.0", "react-router-dom": "^6.8.1", "react-window": "^1.8.11", "zustand": "^4.3.6"}, "devDependencies": {"@types/react": "^18.0.28", "@types/react-dom": "^18.0.11", "@typescript-eslint/eslint-plugin": "^5.57.1", "@typescript-eslint/parser": "^5.57.1", "@vitejs/plugin-react": "^4.0.0", "autoprefixer": "^10.4.14", "eslint": "^8.38.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.3.4", "postcss": "^8.4.21", "tailwindcss": "^3.3.0", "typescript": "^5.0.2", "vite": "^4.3.2", "vitest": "^0.30.1"}}