import{r as D,j as e,f as ce,h as le,i as L,k as oe,l as de,m as me,n as H,o as z,p as xe,q as ue,s as J,t as he,v as ye,w as ge,x as pe,y as ve,z as je,B as U,A as be,D as fe,E as Ne}from"./index-9035c059.js";import{useStudyStore as Se}from"./studyStore-69666106.js";import{u as we}from"./documentStore-9d47d43b.js";const O=n=>{const c=Math.floor(n/3600),i=Math.floor(n%3600/60),r=n%60;return c>0?`${c}h ${i}m`:i>0?`${i}m ${r}s`:`${r}s`},C=D.memo(({title:n,value:c,subtitle:i,icon:r})=>e.jsx("div",{className:"bg-background-secondary border border-gray-600 rounded-lg p-6",children:e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-sm text-gray-400",children:n}),e.jsx("p",{className:"text-2xl font-semibold text-white mt-1",children:c}),i&&e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:i})]}),r&&e.jsx("div",{className:"text-2xl",children:r})]})}));C.displayName="StatCard";const K=D.memo(({sessions:n})=>{const c=D.useMemo(()=>{const r=Array.from({length:7},(a,s)=>{const m=new Date;return m.setDate(m.getDate()-(6-s)),{date:m.toLocaleDateString("en-US",{weekday:"short"}),sessions:0,timeSpent:0}});return n.forEach(a=>{const s=new Date(a.startTime),m=Math.floor((Date.now()-s.getTime())/(1e3*60*60*24));if(m>=0&&m<7){const d=6-m;r[d].sessions+=1,r[d].timeSpent+=a.timeSpent}}),r},[n]),i=Math.max(...c.map(r=>r.timeSpent));return e.jsxs("div",{className:"bg-background-secondary border border-gray-600 rounded-lg p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Study Activity (Last 7 Days)"}),e.jsx("div",{className:"flex items-end justify-between h-32 space-x-2",children:c.map((r,a)=>e.jsxs("div",{className:"flex-1 flex flex-col items-center",children:[e.jsx("div",{className:"w-full bg-gray-700 rounded-t relative",style:{height:"100px"},children:e.jsx("div",{className:"bg-primary-500 rounded-t transition-all duration-300",style:{height:i>0?`${r.timeSpent/i*100}%`:"0%",position:"absolute",bottom:0,left:0,right:0},title:`${r.sessions} sessions, ${O(r.timeSpent)}`})}),e.jsx("div",{className:"text-xs text-gray-400 mt-2",children:r.date})]},a))})]})});K.displayName="ProgressChart";const X=D.memo(({studySets:n,sessions:c})=>{const i=D.useMemo(()=>{var j,o;const r=c.reduce((t,l)=>t+l.timeSpent,0),a=c.length,s=a>0?r/a:0,m=c.reduce((t,l)=>t+l.reviewedItems,0),d=c.filter(t=>t.type==="quiz"&&t.correctAnswers!==void 0),y=d.reduce((t,l)=>t+l.totalItems,0),h=d.reduce((t,l)=>t+(l.correctAnswers||0),0),f=y>0?h/y*100:0,v=new Date;let S=0;for(let t=0;t<365;t++){const l=new Date(v);if(l.setDate(v.getDate()-t),c.some(p=>new Date(p.startTime).toDateString()===l.toDateString()))S++;else if(t>0)break}const w=c.reduce((t,l)=>(t[l.studySetId]=(t[l.studySetId]||0)+1,t),{}),T=(j=Object.entries(w).sort(([,t],[,l])=>l-t)[0])==null?void 0:j[0],N=((o=n.find(t=>t.id===T))==null?void 0:o.name)||"None",A=c.sort((t,l)=>new Date(l.startTime).getTime()-new Date(t.startTime).getTime()).slice(0,5);return{totalStudyTime:r,totalSessions:a,averageSessionTime:s,totalItemsReviewed:m,averageAccuracy:f,studyStreak:S,mostStudiedSet:N,recentActivity:A}},[c,n]);return e.jsxs("div",{className:"max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[e.jsxs("div",{className:"mb-8",children:[e.jsx("h1",{className:"text-3xl font-bold text-white mb-2",children:"Study Analytics"}),e.jsx("p",{className:"text-gray-400",children:"Track your learning progress and performance"})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[e.jsx(C,{title:"Total Study Time",value:O(i.totalStudyTime),icon:"⏱️"}),e.jsx(C,{title:"Study Sessions",value:i.totalSessions,subtitle:"All time",icon:"📚"}),e.jsx(C,{title:"Average Session",value:O(Math.round(i.averageSessionTime)),icon:"⏰"}),e.jsx(C,{title:"Study Streak",value:`${i.studyStreak} days`,icon:"🔥"})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8",children:[e.jsx(C,{title:"Items Reviewed",value:i.totalItemsReviewed,subtitle:"Flashcards & questions",icon:"✅"}),e.jsx(C,{title:"Quiz Accuracy",value:`${Math.round(i.averageAccuracy)}%`,subtitle:"Average across all quizzes",icon:"🎯"})]}),e.jsx("div",{className:"mb-8",children:e.jsx(K,{sessions:c})}),e.jsxs("div",{className:"bg-background-secondary border border-gray-600 rounded-lg p-6",children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Recent Activity"}),i.recentActivity.length===0?e.jsx("p",{className:"text-gray-400",children:"No recent study sessions"}):e.jsx("div",{className:"space-y-3",children:i.recentActivity.map((r,a)=>{const s=n.find(m=>m.id===r.studySetId);return e.jsxs("div",{className:"flex items-center justify-between py-2 border-b border-gray-700 last:border-b-0",children:[e.jsxs("div",{children:[e.jsx("p",{className:"text-white font-medium",children:(s==null?void 0:s.name)||"Unknown Set"}),e.jsxs("p",{className:"text-sm text-gray-400",children:[r.type==="quiz"?"Quiz":"Flashcards"," • ",r.reviewedItems,"/",r.totalItems," items"]})]}),e.jsxs("div",{className:"text-right",children:[e.jsx("p",{className:"text-sm text-gray-300",children:O(r.timeSpent)}),e.jsx("p",{className:"text-xs text-gray-500",children:new Date(r.startTime).toLocaleDateString()})]})]},a)})})]})]})});X.displayName="StudyAnalytics";const G=n=>{const c=Math.floor(n/3600),i=Math.floor(n%3600/60);return c>0?`${c}h ${i}m`:i>0?`${i}m`:`${n}s`},P=({title:n,value:c,subtitle:i,icon:r,trend:a,isLoading:s})=>e.jsxs(H.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("div",{className:"p-3 bg-primary-500/20 rounded-lg",children:e.jsx(r,{className:"w-6 h-6 text-primary-400"})}),a&&e.jsxs("div",{className:`flex items-center space-x-1 ${a.isPositive?"text-green-400":"text-red-400"}`,children:[e.jsx(z,{className:`w-4 h-4 ${a.isPositive?"":"rotate-180"}`}),e.jsxs("span",{className:"text-sm font-medium",children:[Math.abs(a.value),"%"]})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-gray-400 mb-1",children:n}),s?e.jsx("div",{className:"animate-pulse",children:e.jsx("div",{className:"h-8 w-20 bg-gray-600 rounded"})}):e.jsx("p",{className:"text-2xl font-bold text-white",children:c}),i&&e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:i})]})]}),De=({studySets:n,sessions:c,documents:i,timeRange:r,isLoading:a})=>{const s=D.useMemo(()=>{const d=new Date,y={"7d":7*24*60*60*1e3,"30d":30*24*60*60*1e3,"90d":90*24*60*60*1e3,all:1/0}[r],h=c.filter(t=>{const l=new Date(t.startTime).getTime();return d.getTime()-l<=y}),f=h.reduce((t,l)=>t+l.timeSpent,0),v=h.length,S=v>0?f/v:0,w=h.reduce((t,l)=>t+l.totalItems,0),T=h.reduce((t,l)=>t+l.reviewedItems,0),N=w>0?T/w*100:0,A=n.filter(t=>t.is_ai_generated).length,j=24*60*60*1e3,o=c.filter(t=>{const l=new Date(t.startTime).getTime();return d.getTime()-l<=j}).length;return{totalStudySets:n.length,totalDocuments:i.length,totalSessions:v,totalStudyTime:f,averageSessionTime:S,completionRate:N,aiGeneratedSets:A,recentActivity:o}},[n,c,i,r]),m=D.useMemo(()=>{const d=new Date,y={"7d":7*24*60*60*1e3,"30d":30*24*60*60*1e3,"90d":90*24*60*60*1e3,all:1/0}[r];if(y===1/0)return{};const h=d.getTime()-y,f=h-y,v=c.filter(j=>new Date(j.startTime).getTime()>=h),S=c.filter(j=>{const o=new Date(j.startTime).getTime();return o>=f&&o<h}),w=v.reduce((j,o)=>j+o.timeSpent,0),T=S.reduce((j,o)=>j+o.timeSpent,0),N=T>0?(w-T)/T*100:0,A=S.length>0?(v.length-S.length)/S.length*100:0;return{studyTime:{value:Math.round(N),isPositive:N>=0},sessions:{value:Math.round(A),isPositive:A>=0}}},[c,r]);return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[e.jsx(P,{title:"Study Sets",value:s.totalStudySets,subtitle:"Total created",icon:ce,isLoading:a}),e.jsx(P,{title:"Documents",value:s.totalDocuments,subtitle:"Uploaded files",icon:le,isLoading:a}),e.jsx(P,{title:"Study Time",value:G(s.totalStudyTime),subtitle:`${r==="all"?"All time":`Last ${r.replace("d"," days")}`}`,icon:L,trend:m.studyTime,isLoading:a}),e.jsx(P,{title:"Sessions",value:s.totalSessions,subtitle:`${r==="all"?"All time":`Last ${r.replace("d"," days")}`}`,icon:oe,trend:m.sessions,isLoading:a})]}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[e.jsx(P,{title:"Avg Session",value:G(Math.round(s.averageSessionTime)),subtitle:"Time per session",icon:L,isLoading:a}),e.jsx(P,{title:"Completion Rate",value:`${Math.round(s.completionRate)}%`,subtitle:"Items reviewed",icon:de,isLoading:a}),e.jsx(P,{title:"AI Generated",value:s.aiGeneratedSets,subtitle:"Study sets created by AI",icon:me,isLoading:a})]}),e.jsxs("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Quick Stats"}),e.jsxs("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4",children:[e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-primary-400",children:s.recentActivity}),e.jsx("div",{className:"text-sm text-gray-400",children:"Sessions today"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-green-400",children:n.filter(d=>d.type==="flashcards").length}),e.jsx("div",{className:"text-sm text-gray-400",children:"Flashcard sets"})]}),e.jsxs("div",{className:"text-center",children:[e.jsx("div",{className:"text-2xl font-bold text-blue-400",children:n.filter(d=>d.type==="quiz").length}),e.jsx("div",{className:"text-sm text-gray-400",children:"Quiz sets"})]}),e.jsxs("div",{className:"text-center",children:[e.jsxs("div",{className:"text-2xl font-bold text-purple-400",children:[Math.round(s.aiGeneratedSets/Math.max(s.totalStudySets,1)*100),"%"]}),e.jsx("div",{className:"text-sm text-gray-400",children:"AI generated"})]})]})]})]})},Y=n=>n<1?`${Math.round(n*60)}s/item`:`${n.toFixed(1)} items/min`,R=({title:n,value:c,subtitle:i,icon:r,trend:a,isLoading:s,color:m="primary"})=>{const d={primary:"bg-primary-500/20 text-primary-400",green:"bg-green-500/20 text-green-400",red:"bg-red-500/20 text-red-400",yellow:"bg-yellow-500/20 text-yellow-400"};return e.jsxs(H.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:[e.jsxs("div",{className:"flex items-center justify-between mb-4",children:[e.jsx("div",{className:`p-3 rounded-lg ${d[m]}`,children:e.jsx(r,{className:"w-6 h-6"})}),a!==void 0&&e.jsxs("div",{className:`flex items-center space-x-1 ${a>=0?"text-green-400":"text-red-400"}`,children:[a>=0?e.jsx(z,{className:"w-4 h-4"}):e.jsx(he,{className:"w-4 h-4"}),e.jsxs("span",{className:"text-sm font-medium",children:[Math.abs(a).toFixed(1),"%"]})]})]}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-gray-400 mb-1",children:n}),s?e.jsx("div",{className:"animate-pulse",children:e.jsx("div",{className:"h-8 w-20 bg-gray-600 rounded"})}):e.jsx("p",{className:"text-2xl font-bold text-white",children:c}),i&&e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:i})]})]})},V=({subjects:n,title:c,color:i})=>e.jsxs("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:c}),e.jsx("div",{className:"space-y-3",children:n.length===0?e.jsx("p",{className:"text-gray-400 text-center py-4",children:"No data available"}):n.map((r,a)=>e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-300 truncate flex-1 mr-4",children:r.name}),e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx("div",{className:"w-20 bg-gray-700 rounded-full h-2",children:e.jsx("div",{className:`h-2 rounded-full ${i}`,style:{width:`${r.accuracy}%`}})}),e.jsxs("span",{className:"text-white font-medium w-12 text-right",children:[Math.round(r.accuracy),"%"]})]})]},a))})]}),Te=({studySets:n,sessions:c,timeRange:i,isLoading:r})=>{const a=D.useMemo(()=>{const s=new Date,m={"7d":7*24*60*60*1e3,"30d":30*24*60*60*1e3,"90d":90*24*60*60*1e3,all:1/0}[i],d=c.filter(x=>{const u=new Date(x.startTime).getTime();return s.getTime()-u<=m}),y=d.filter(x=>x.type==="quiz"&&x.correctAnswers!==void 0),h=y.reduce((x,u)=>x+u.totalItems,0),f=y.reduce((x,u)=>x+(u.correctAnswers||0),0),v=h>0?f/h*100:0,S=d.reduce((x,u)=>x+u.reviewedItems,0),w=d.reduce((x,u)=>x+u.timeSpent,0)/60,T=w>0?S/w:0,N=new Map;y.forEach(x=>{const u=n.find(k=>k.id===x.studySetId);if(u){const k=N.get(u.name)||{correct:0,total:0};N.set(u.name,{correct:k.correct+(x.correctAnswers||0),total:k.total+x.totalItems})}});const A=Array.from(N.entries()).map(([x,u])=>({name:x,accuracy:u.total>0?u.correct/u.total*100:0})).sort((x,u)=>u.accuracy-x.accuracy),j=A.slice(0,5),o=A.slice(-5).reverse(),t=Array.from({length:7},(x,u)=>{const k=new Date;k.setDate(k.getDate()-(6-u));const se=k.toISOString().split("T")[0],q=d.filter($=>new Date($.startTime).toISOString().split("T")[0]===se),Q=q.filter($=>$.type==="quiz"&&$.correctAnswers!==void 0),B=Q.reduce(($,I)=>$+I.totalItems,0),re=Q.reduce(($,I)=>$+(I.correctAnswers||0),0),ae=B>0?re/B*100:0,ie=q.reduce(($,I)=>$+I.reviewedItems,0),_=q.reduce(($,I)=>$+I.timeSpent,0)/60,ne=_>0?ie/_:0;return{date:k.toLocaleDateString("en-US",{weekday:"short"}),accuracy:ae,speed:ne}}),l=t.slice(-3).reduce((x,u)=>x+u.accuracy,0)/3,g=t.slice(0,3).reduce((x,u)=>x+u.accuracy,0)/3,p=g>0?(l-g)/g*100:0,b=t.slice(-3).reduce((x,u)=>x+u.speed,0)/3,M=t.slice(0,3).reduce((x,u)=>x+u.speed,0)/3,E=M>0?(b-M)/M*100:0,ee=t.reduce((x,u)=>{const k=u.accuracy-v;return x+k*k},0)/t.length,te=Math.max(0,100-Math.sqrt(ee));return{overallAccuracy:v,accuracyTrend:p,averageSpeed:T,speedTrend:E,consistencyScore:te,improvementRate:p,strongestSubjects:j,weakestSubjects:o,dailyPerformance:t}},[c,n,i]);return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[e.jsx(R,{title:"Overall Accuracy",value:`${Math.round(a.overallAccuracy)}%`,subtitle:"Quiz performance",icon:xe,trend:a.accuracyTrend,isLoading:r,color:"green"}),e.jsx(R,{title:"Average Speed",value:Y(a.averageSpeed),subtitle:"Items per minute",icon:ue,trend:a.speedTrend,isLoading:r,color:"yellow"}),e.jsx(R,{title:"Consistency",value:`${Math.round(a.consistencyScore)}%`,subtitle:"Performance stability",icon:J,isLoading:r,color:"primary"}),e.jsx(R,{title:"Improvement",value:`${a.improvementRate>=0?"+":""}${a.improvementRate.toFixed(1)}%`,subtitle:"Recent trend",icon:z,isLoading:r,color:a.improvementRate>=0?"green":"red"})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsx(V,{subjects:a.strongestSubjects,title:"Strongest Subjects",color:"bg-green-500"}),e.jsx(V,{subjects:a.weakestSubjects,title:"Areas for Improvement",color:"bg-red-500"})]}),e.jsxs("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Daily Performance (Last 7 Days)"}),e.jsx("div",{className:"flex items-end justify-between h-32 space-x-2",children:a.dailyPerformance.map((s,m)=>{const d=Math.max(...a.dailyPerformance.map(h=>h.accuracy),1),y=s.accuracy/d*100;return e.jsxs("div",{className:"flex-1 flex flex-col items-center",children:[e.jsx("div",{className:"w-full bg-gray-700 rounded-t relative",style:{height:"100px"},children:e.jsx(H.div,{initial:{height:0},animate:{height:`${y}%`},transition:{duration:.5,delay:m*.1},className:"bg-gradient-to-t from-green-500 to-green-400 rounded-t transition-all duration-300 absolute bottom-0 left-0 right-0",title:`${Math.round(s.accuracy)}% accuracy, ${Y(s.speed)}`})}),e.jsx("div",{className:"text-xs text-gray-400 mt-2",children:s.date})]},m)})})]})]})},Z=n=>{const c=Math.floor(n/3600),i=Math.floor(n%3600/60);return c>0?`${c}h ${i}m`:i>0?`${i}m`:`${n}s`},F=({title:n,value:c,subtitle:i,icon:r,isLoading:a,color:s="text-primary-400"})=>e.jsxs(H.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},transition:{duration:.3},className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:[e.jsxs("div",{className:"flex items-center space-x-3 mb-4",children:[e.jsx("div",{className:"p-3 bg-primary-500/20 rounded-lg",children:e.jsx(r,{className:`w-6 h-6 ${s}`})}),e.jsxs("div",{children:[e.jsx("h3",{className:"text-sm font-medium text-gray-400",children:n}),a?e.jsx("div",{className:"animate-pulse",children:e.jsx("div",{className:"h-6 w-16 bg-gray-600 rounded"})}):e.jsx("p",{className:"text-xl font-bold text-white",children:c})]})]}),i&&e.jsx("p",{className:"text-xs text-gray-500",children:i})]}),W=({title:n,data:c,color:i})=>{const r=Math.max(...c.map(a=>a.value),1);return e.jsxs("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:n}),e.jsx("div",{className:"space-y-3",children:c.map((a,s)=>e.jsxs("div",{className:"flex items-center justify-between",children:[e.jsx("span",{className:"text-gray-300 text-sm w-20",children:a.label}),e.jsx("div",{className:"flex-1 mx-4",children:e.jsx("div",{className:"w-full bg-gray-700 rounded-full h-2",children:e.jsx(H.div,{initial:{width:0},animate:{width:`${a.value/r*100}%`},transition:{duration:.5,delay:s*.1},className:`h-2 rounded-full ${i}`})})}),e.jsxs("div",{className:"text-right w-20",children:[e.jsx("span",{className:"text-white font-medium text-sm",children:a.value}),a.time&&e.jsx("div",{className:"text-xs text-gray-400",children:Z(a.time)})]})]},s))})]})},Ae=({sessions:n,timeRange:c,isLoading:i})=>{const r=D.useMemo(()=>{const s=new Date;let m=0,d=0,y=0;for(let o=0;o<365;o++){const t=new Date(s);if(t.setDate(s.getDate()-o),n.some(g=>new Date(g.startTime).toDateString()===t.toDateString()))(o===0||m===o)&&m++,y++,d=Math.max(d,y);else{if(o===0){const g=new Date(s);g.setDate(s.getDate()-1),n.some(b=>new Date(b.startTime).toDateString()===g.toDateString())||(m=0)}y=0}}const h={"7d":7*24*60*60*1e3,"30d":30*24*60*60*1e3,"90d":90*24*60*60*1e3,all:1/0}[c],f=n.filter(o=>{const t=new Date(o.startTime).getTime();return s.getTime()-t<=h}),v=h===1/0?Math.max(1,Math.ceil((s.getTime()-Math.min(...n.map(o=>new Date(o.startTime).getTime())))/(24*60*60*1e3))):Math.ceil(h/(24*60*60*1e3)),S=f.length/v,w=Array.from({length:24},(o,t)=>{const l=f.filter(g=>new Date(g.startTime).getHours()===t);return{hour:t,sessions:l.length,time:l.reduce((g,p)=>g+p.timeSpent,0)}}),T=w.reduce((o,t)=>t.sessions>o.sessions?t:o).hour,N=["Sun","Mon","Tue","Wed","Thu","Fri","Sat"].map((o,t)=>{const l=f.filter(g=>new Date(g.startTime).getDay()===t);return{day:o,sessions:l.length,time:l.reduce((g,p)=>g+p.timeSpent,0)}}),A=N.reduce((o,t)=>t.sessions>o.sessions?t:o).day,j=Array.from({length:6},(o,t)=>{const l=new Date(s);l.setMonth(s.getMonth()-(5-t));const g=l.toLocaleDateString("en-US",{month:"short"}),p=n.filter(b=>{const M=new Date(b.startTime);return M.getMonth()===l.getMonth()&&M.getFullYear()===l.getFullYear()});return{month:g,sessions:p.length,time:p.reduce((b,M)=>b+M.timeSpent,0)}});return{studyStreak:m,longestStreak:d,averageSessionsPerDay:S,mostProductiveHour:T,mostProductiveDay:A,weeklyPattern:N,hourlyPattern:w.filter(o=>o.sessions>0),monthlyTrend:j}},[n,c]),a=s=>s===0?"12 AM":s<12?`${s} AM`:s===12?"12 PM":`${s-12} PM`;return e.jsxs("div",{className:"space-y-6",children:[e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[e.jsx(F,{title:"Current Streak",value:`${r.studyStreak} days`,subtitle:"Consecutive study days",icon:ye,isLoading:i,color:"text-orange-400"}),e.jsx(F,{title:"Longest Streak",value:`${r.longestStreak} days`,subtitle:"Personal best",icon:z,isLoading:i,color:"text-green-400"}),e.jsx(F,{title:"Daily Average",value:r.averageSessionsPerDay.toFixed(1),subtitle:"Sessions per day",icon:ge,isLoading:i,color:"text-blue-400"}),e.jsx(F,{title:"Peak Hour",value:a(r.mostProductiveHour),subtitle:"Most active time",icon:r.mostProductiveHour<12?pe:ve,isLoading:i,color:"text-yellow-400"})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[e.jsx(W,{title:"Weekly Study Pattern",data:r.weeklyPattern.map(s=>({label:s.day,value:s.sessions,time:s.time})),type:"bar",color:"bg-primary-500"}),e.jsx(W,{title:"Active Study Hours",data:r.hourlyPattern.slice(0,8).map(s=>({label:a(s.hour),value:s.sessions,time:s.time})),type:"bar",color:"bg-yellow-500"})]}),e.jsxs("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"6-Month Study Trend"}),e.jsx("div",{className:"flex items-end justify-between h-40 space-x-2",children:r.monthlyTrend.map((s,m)=>{const d=Math.max(...r.monthlyTrend.map(h=>h.sessions),1),y=s.sessions/d*100;return e.jsxs("div",{className:"flex-1 flex flex-col items-center",children:[e.jsx("div",{className:"w-full bg-gray-700 rounded-t relative",style:{height:"120px"},children:e.jsx(H.div,{initial:{height:0},animate:{height:`${y}%`},transition:{duration:.5,delay:m*.1},className:"bg-gradient-to-t from-purple-500 to-purple-400 rounded-t transition-all duration-300 absolute bottom-0 left-0 right-0",title:`${s.sessions} sessions, ${Z(s.time)}`})}),e.jsx("div",{className:"text-xs text-gray-400 mt-2",children:s.month}),e.jsx("div",{className:"text-xs text-white font-medium",children:s.sessions})]},m)})})]}),e.jsxs("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:[e.jsx("h3",{className:"text-lg font-semibold text-white mb-4",children:"Study Insights"}),e.jsxs("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-white mb-2",children:"Best Study Day"}),e.jsx("p",{className:"text-gray-300",children:r.mostProductiveDay}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Most sessions completed on this day of the week"})]}),e.jsxs("div",{children:[e.jsx("h4",{className:"font-medium text-white mb-2",children:"Optimal Study Time"}),e.jsx("p",{className:"text-gray-300",children:a(r.mostProductiveHour)}),e.jsx("p",{className:"text-xs text-gray-500 mt-1",children:"Peak productivity hour based on session frequency"})]})]})]})]})},Me=[{id:"overview",label:"Overview",icon:J,description:"General study analytics and progress"},{id:"performance",label:"Performance",icon:z,description:"Detailed performance metrics and accuracy"},{id:"trends",label:"Trends",icon:L,description:"Study patterns and time analysis"}],Pe=()=>{const[n,c]=D.useState("overview"),[i,r]=D.useState("30d"),[a,s]=D.useState(!0),[m,d]=D.useState(null),{studySets:y,sessions:h,fetchStudySets:f,fetchStudySessions:v}=Se(),{documents:S}=we();D.useEffect(()=>{(async()=>{s(!0),d(null);try{await Promise.all([f(),v(i)])}catch(l){d(l instanceof Error?l.message:"Failed to load analytics data")}finally{s(!1)}})()},[i,f,v]);const w=async()=>{d(null),s(!0);try{await Promise.all([f(),v(i)])}catch(t){d(t instanceof Error?t.message:"Failed to refresh data")}finally{s(!1)}},T=()=>{const t=[["Date","Study Set","Type","Items Reviewed","Time Spent (minutes)","Accuracy"].join(","),...h.map(b=>{const M=y.find(E=>E.id===b.studySetId);return[new Date(b.startTime).toLocaleDateString(),(M==null?void 0:M.name)||"Unknown",b.type,b.reviewedItems,Math.round(b.timeSpent/60),b.correctAnswers?`${Math.round(b.correctAnswers/b.totalItems*100)}%`:"N/A"].join(",")})].join(`
`),l=new Blob([t],{type:"text/csv"}),g=window.URL.createObjectURL(l),p=document.createElement("a");p.href=g,p.download=`study-analytics-${new Date().toISOString().split("T")[0]}.csv`,p.click(),window.URL.revokeObjectURL(g)},N=()=>e.jsxs("div",{className:"space-y-6",children:[e.jsx(De,{studySets:y,sessions:h,documents:S,timeRange:i,isLoading:a}),e.jsx(X,{studySets:y,sessions:h})]}),A=()=>e.jsx(Te,{studySets:y,sessions:h,timeRange:i,isLoading:a}),j=()=>e.jsx(Ae,{sessions:h,timeRange:i,isLoading:a}),o=()=>{switch(n){case"overview":return N();case"performance":return A();case"trends":return j();default:return N()}};return e.jsx("div",{className:"min-h-screen bg-background-primary text-white",children:e.jsxs("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[e.jsxs("div",{className:"flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8 space-y-4 sm:space-y-0",children:[e.jsxs("div",{children:[e.jsx("h1",{className:"text-3xl font-bold text-white mb-2",children:"Analytics"}),e.jsx("p",{className:"text-gray-400",children:"Track your learning progress and performance insights"})]}),e.jsxs("div",{className:"flex items-center space-x-4",children:[e.jsxs("div",{className:"relative",children:[e.jsxs("select",{value:i,onChange:t=>r(t.target.value),className:"appearance-none bg-background-secondary border border-border-primary rounded-lg px-3 py-2 pr-8 text-white text-sm focus:outline-none focus:ring-2 focus:ring-primary-500",children:[e.jsx("option",{value:"7d",children:"Last 7 days"}),e.jsx("option",{value:"30d",children:"Last 30 days"}),e.jsx("option",{value:"90d",children:"Last 90 days"}),e.jsx("option",{value:"all",children:"All time"})]}),e.jsx(je,{className:"absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none"})]}),e.jsxs(U,{onClick:T,variant:"secondary",size:"sm",disabled:h.length===0,children:[e.jsx(be,{className:"w-4 h-4 mr-2"}),"Export"]}),e.jsxs(U,{onClick:w,variant:"secondary",size:"sm",disabled:a,children:[e.jsx(fe,{className:`w-4 h-4 mr-2 ${a?"animate-spin":""}`}),"Refresh"]})]})]}),m&&e.jsxs("div",{className:"mb-6 bg-red-500/20 border border-red-500/30 rounded-lg p-4",children:[e.jsxs("div",{className:"flex items-center space-x-2",children:[e.jsx(Ne,{className:"w-5 h-5 text-red-400"}),e.jsx("span",{className:"text-red-400 font-medium",children:"Error"})]}),e.jsx("p",{className:"text-red-300 mt-1",children:m}),e.jsx(U,{onClick:()=>d(null),variant:"secondary",size:"sm",className:"mt-2",children:"Dismiss"})]}),e.jsxs("div",{className:"grid grid-cols-1 lg:grid-cols-4 gap-8",children:[e.jsx("div",{className:"lg:col-span-1",children:e.jsx("div",{className:"bg-background-secondary rounded-lg p-6 border border-border-primary",children:e.jsx("nav",{className:"space-y-2",children:Me.map(t=>{const l=t.icon,g=n===t.id;return e.jsxs("button",{onClick:()=>c(t.id),className:`
                        w-full flex items-center space-x-3 px-3 py-3 rounded-lg text-left
                        transition-all duration-200
                        ${g?"bg-primary-500/20 text-primary-400 border border-primary-500/30":"text-gray-300 hover:bg-background-tertiary hover:text-white"}
                      `,children:[e.jsx(l,{className:"w-5 h-5"}),e.jsxs("div",{className:"flex-1 min-w-0",children:[e.jsx("span",{className:"font-medium block",children:t.label}),e.jsx("span",{className:"text-xs text-gray-500 block truncate",children:t.description})]})]},t.id)})})})}),e.jsx("div",{className:"lg:col-span-3",children:e.jsx(H.div,{initial:{opacity:0,x:20},animate:{opacity:1,x:0},transition:{duration:.3},children:o()},n)})]})]})})};export{Pe as AnalyticsPage};
