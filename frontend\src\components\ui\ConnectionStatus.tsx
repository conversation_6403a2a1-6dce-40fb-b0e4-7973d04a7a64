import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { HiWifi, HiExclamationTriangle, HiRefresh } from 'react-icons/hi';
import { useConnectionStore } from '../../services/connectionService';

interface ConnectionStatusProps {
  showWhenOnline?: boolean;
  position?: 'top' | 'bottom';
  className?: string;
}

export const ConnectionStatus: React.FC<ConnectionStatusProps> = ({
  showWhenOnline = false,
  position = 'top',
  className = '',
}) => {
  const { 
    isOnline, 
    isBackendAvailable, 
    lastChecked, 
    retryCount,
    isChecking,
    checkConnection 
  } = useConnectionStore();
  
  const [showStatus, setShowStatus] = useState(false);
  const [autoHideTimer, setAutoHideTimer] = useState<NodeJS.Timeout | null>(null);

  // Determine connection status
  const isFullyConnected = isOnline && isBackendAvailable;
  const hasConnectionIssues = !isOnline || !isBackendAvailable;

  // Show/hide logic
  useEffect(() => {
    if (hasConnectionIssues) {
      setShowStatus(true);
      // Clear any existing auto-hide timer
      if (autoHideTimer) {
        clearTimeout(autoHideTimer);
        setAutoHideTimer(null);
      }
    } else if (isFullyConnected) {
      if (showWhenOnline) {
        setShowStatus(true);
        // Auto-hide after 3 seconds when connection is restored
        const timer = setTimeout(() => {
          setShowStatus(false);
        }, 3000);
        setAutoHideTimer(timer);
      } else {
        setShowStatus(false);
      }
    }

    return () => {
      if (autoHideTimer) {
        clearTimeout(autoHideTimer);
      }
    };
  }, [isOnline, isBackendAvailable, showWhenOnline]);

  const handleRetry = async () => {
    await checkConnection();
  };

  const getStatusConfig = () => {
    if (!isOnline) {
      return {
        icon: HiExclamationTriangle,
        message: 'No internet connection',
        bgColor: 'bg-red-500',
        textColor: 'text-white',
        showRetry: false,
      };
    }
    
    if (!isBackendAvailable) {
      return {
        icon: HiExclamationTriangle,
        message: retryCount > 3 
          ? 'Server temporarily unavailable' 
          : 'Connecting to server...',
        bgColor: 'bg-yellow-500',
        textColor: 'text-white',
        showRetry: retryCount > 2,
      };
    }
    
    return {
      icon: HiWifi,
      message: 'Connected',
      bgColor: 'bg-green-500',
      textColor: 'text-white',
      showRetry: false,
    };
  };

  const statusConfig = getStatusConfig();
  const Icon = statusConfig.icon;

  if (!showStatus) {
    return null;
  }

  const positionClasses = position === 'top' 
    ? 'top-4 left-1/2 transform -translate-x-1/2' 
    : 'bottom-4 left-1/2 transform -translate-x-1/2';

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: position === 'top' ? -50 : 50 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: position === 'top' ? -50 : 50 }}
        className={`
          fixed ${positionClasses} z-50 
          ${statusConfig.bgColor} ${statusConfig.textColor}
          px-4 py-2 rounded-lg shadow-lg border border-gray-600
          flex items-center space-x-2 text-sm font-medium
          ${className}
        `}
      >
        <Icon className={`w-4 h-4 ${isChecking ? 'animate-spin' : ''}`} />
        <span>{statusConfig.message}</span>
        
        {statusConfig.showRetry && (
          <button
            onClick={handleRetry}
            disabled={isChecking}
            className="ml-2 p-1 rounded hover:bg-black/20 transition-colors disabled:opacity-50"
            aria-label="Retry connection"
          >
            <HiRefresh className={`w-3 h-3 ${isChecking ? 'animate-spin' : ''}`} />
          </button>
        )}
        
        {lastChecked && hasConnectionIssues && (
          <span className="text-xs opacity-75 ml-2">
            Last checked: {lastChecked.toLocaleTimeString()}
          </span>
        )}
      </motion.div>
    </AnimatePresence>
  );
};

// Compact version for sidebar or header
export const ConnectionIndicator: React.FC<{ className?: string }> = ({ 
  className = '' 
}) => {
  const { isOnline, isBackendAvailable, isChecking } = useConnectionStore();
  
  const isFullyConnected = isOnline && isBackendAvailable;
  
  const getIndicatorColor = () => {
    if (!isOnline) return 'bg-red-500';
    if (!isBackendAvailable) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  const getTooltipText = () => {
    if (!isOnline) return 'No internet connection';
    if (!isBackendAvailable) return 'Server unavailable';
    return 'Connected';
  };

  return (
    <div 
      className={`flex items-center space-x-2 ${className}`}
      title={getTooltipText()}
    >
      <div 
        className={`
          w-2 h-2 rounded-full ${getIndicatorColor()}
          ${isChecking ? 'animate-pulse' : ''}
        `}
      />
      <span className="text-xs text-gray-400">
        {isFullyConnected ? 'Online' : 'Offline'}
      </span>
    </div>
  );
};
