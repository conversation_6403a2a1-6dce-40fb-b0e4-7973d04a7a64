function Tv(e,t){for(var n=0;n<t.length;n++){const r=t[n];if(typeof r!="string"&&!Array.isArray(r)){for(const i in r)if(i!=="default"&&!(i in e)){const s=Object.getOwnPropertyDescriptor(r,i);s&&Object.defineProperty(e,i,s.get?s:{enumerable:!0,get:()=>r[i]})}}}return Object.freeze(Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}))}(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const i of document.querySelectorAll('link[rel="modulepreload"]'))r(i);new MutationObserver(i=>{for(const s of i)if(s.type==="childList")for(const o of s.addedNodes)o.tagName==="LINK"&&o.rel==="modulepreload"&&r(o)}).observe(document,{childList:!0,subtree:!0});function n(i){const s={};return i.integrity&&(s.integrity=i.integrity),i.referrerPolicy&&(s.referrerPolicy=i.referrerPolicy),i.crossOrigin==="use-credentials"?s.credentials="include":i.crossOrigin==="anonymous"?s.credentials="omit":s.credentials="same-origin",s}function r(i){if(i.ep)return;i.ep=!0;const s=n(i);fetch(i.href,s)}})();var nt=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function Hf(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function jv(e){if(e.__esModule)return e;var t=e.default;if(typeof t=="function"){var n=function r(){return this instanceof r?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};n.prototype=t.prototype}else n={};return Object.defineProperty(n,"__esModule",{value:!0}),Object.keys(e).forEach(function(r){var i=Object.getOwnPropertyDescriptor(e,r);Object.defineProperty(n,r,i.get?i:{enumerable:!0,get:function(){return e[r]}})}),n}var Wf={exports:{}},Vo={},Kf={exports:{}},$={};/**
 * @license React
 * react.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ns=Symbol.for("react.element"),Av=Symbol.for("react.portal"),Rv=Symbol.for("react.fragment"),Lv=Symbol.for("react.strict_mode"),Ov=Symbol.for("react.profiler"),Iv=Symbol.for("react.provider"),Nv=Symbol.for("react.context"),Mv=Symbol.for("react.forward_ref"),Dv=Symbol.for("react.suspense"),Vv=Symbol.for("react.memo"),$v=Symbol.for("react.lazy"),sd=Symbol.iterator;function zv(e){return e===null||typeof e!="object"?null:(e=sd&&e[sd]||e["@@iterator"],typeof e=="function"?e:null)}var Gf={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},qf=Object.assign,Jf={};function Wr(e,t,n){this.props=e,this.context=t,this.refs=Jf,this.updater=n||Gf}Wr.prototype.isReactComponent={};Wr.prototype.setState=function(e,t){if(typeof e!="object"&&typeof e!="function"&&e!=null)throw Error("setState(...): takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,e,t,"setState")};Wr.prototype.forceUpdate=function(e){this.updater.enqueueForceUpdate(this,e,"forceUpdate")};function Qf(){}Qf.prototype=Wr.prototype;function Ou(e,t,n){this.props=e,this.context=t,this.refs=Jf,this.updater=n||Gf}var Iu=Ou.prototype=new Qf;Iu.constructor=Ou;qf(Iu,Wr.prototype);Iu.isPureReactComponent=!0;var od=Array.isArray,Yf=Object.prototype.hasOwnProperty,Nu={current:null},Xf={key:!0,ref:!0,__self:!0,__source:!0};function Zf(e,t,n){var r,i={},s=null,o=null;if(t!=null)for(r in t.ref!==void 0&&(o=t.ref),t.key!==void 0&&(s=""+t.key),t)Yf.call(t,r)&&!Xf.hasOwnProperty(r)&&(i[r]=t[r]);var a=arguments.length-2;if(a===1)i.children=n;else if(1<a){for(var l=Array(a),u=0;u<a;u++)l[u]=arguments[u+2];i.children=l}if(e&&e.defaultProps)for(r in a=e.defaultProps,a)i[r]===void 0&&(i[r]=a[r]);return{$$typeof:ns,type:e,key:s,ref:o,props:i,_owner:Nu.current}}function Bv(e,t){return{$$typeof:ns,type:e.type,key:t,ref:e.ref,props:e.props,_owner:e._owner}}function Mu(e){return typeof e=="object"&&e!==null&&e.$$typeof===ns}function Uv(e){var t={"=":"=0",":":"=2"};return"$"+e.replace(/[=:]/g,function(n){return t[n]})}var ad=/\/+/g;function _a(e,t){return typeof e=="object"&&e!==null&&e.key!=null?Uv(""+e.key):t.toString(36)}function Ds(e,t,n,r,i){var s=typeof e;(s==="undefined"||s==="boolean")&&(e=null);var o=!1;if(e===null)o=!0;else switch(s){case"string":case"number":o=!0;break;case"object":switch(e.$$typeof){case ns:case Av:o=!0}}if(o)return o=e,i=i(o),e=r===""?"."+_a(o,0):r,od(i)?(n="",e!=null&&(n=e.replace(ad,"$&/")+"/"),Ds(i,t,n,"",function(u){return u})):i!=null&&(Mu(i)&&(i=Bv(i,n+(!i.key||o&&o.key===i.key?"":(""+i.key).replace(ad,"$&/")+"/")+e)),t.push(i)),1;if(o=0,r=r===""?".":r+":",od(e))for(var a=0;a<e.length;a++){s=e[a];var l=r+_a(s,a);o+=Ds(s,t,n,l,i)}else if(l=zv(e),typeof l=="function")for(e=l.call(e),a=0;!(s=e.next()).done;)s=s.value,l=r+_a(s,a++),o+=Ds(s,t,n,l,i);else if(s==="object")throw t=String(e),Error("Objects are not valid as a React child (found: "+(t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)+"). If you meant to render a collection of children, use an array instead.");return o}function ps(e,t,n){if(e==null)return e;var r=[],i=0;return Ds(e,r,"","",function(s){return t.call(n,s,i++)}),r}function Fv(e){if(e._status===-1){var t=e._result;t=t(),t.then(function(n){(e._status===0||e._status===-1)&&(e._status=1,e._result=n)},function(n){(e._status===0||e._status===-1)&&(e._status=2,e._result=n)}),e._status===-1&&(e._status=0,e._result=t)}if(e._status===1)return e._result.default;throw e._result}var Te={current:null},Vs={transition:null},Hv={ReactCurrentDispatcher:Te,ReactCurrentBatchConfig:Vs,ReactCurrentOwner:Nu};function ep(){throw Error("act(...) is not supported in production builds of React.")}$.Children={map:ps,forEach:function(e,t,n){ps(e,function(){t.apply(this,arguments)},n)},count:function(e){var t=0;return ps(e,function(){t++}),t},toArray:function(e){return ps(e,function(t){return t})||[]},only:function(e){if(!Mu(e))throw Error("React.Children.only expected to receive a single React element child.");return e}};$.Component=Wr;$.Fragment=Rv;$.Profiler=Ov;$.PureComponent=Ou;$.StrictMode=Lv;$.Suspense=Dv;$.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=Hv;$.act=ep;$.cloneElement=function(e,t,n){if(e==null)throw Error("React.cloneElement(...): The argument must be a React element, but you passed "+e+".");var r=qf({},e.props),i=e.key,s=e.ref,o=e._owner;if(t!=null){if(t.ref!==void 0&&(s=t.ref,o=Nu.current),t.key!==void 0&&(i=""+t.key),e.type&&e.type.defaultProps)var a=e.type.defaultProps;for(l in t)Yf.call(t,l)&&!Xf.hasOwnProperty(l)&&(r[l]=t[l]===void 0&&a!==void 0?a[l]:t[l])}var l=arguments.length-2;if(l===1)r.children=n;else if(1<l){a=Array(l);for(var u=0;u<l;u++)a[u]=arguments[u+2];r.children=a}return{$$typeof:ns,type:e.type,key:i,ref:s,props:r,_owner:o}};$.createContext=function(e){return e={$$typeof:Nv,_currentValue:e,_currentValue2:e,_threadCount:0,Provider:null,Consumer:null,_defaultValue:null,_globalName:null},e.Provider={$$typeof:Iv,_context:e},e.Consumer=e};$.createElement=Zf;$.createFactory=function(e){var t=Zf.bind(null,e);return t.type=e,t};$.createRef=function(){return{current:null}};$.forwardRef=function(e){return{$$typeof:Mv,render:e}};$.isValidElement=Mu;$.lazy=function(e){return{$$typeof:$v,_payload:{_status:-1,_result:e},_init:Fv}};$.memo=function(e,t){return{$$typeof:Vv,type:e,compare:t===void 0?null:t}};$.startTransition=function(e){var t=Vs.transition;Vs.transition={};try{e()}finally{Vs.transition=t}};$.unstable_act=ep;$.useCallback=function(e,t){return Te.current.useCallback(e,t)};$.useContext=function(e){return Te.current.useContext(e)};$.useDebugValue=function(){};$.useDeferredValue=function(e){return Te.current.useDeferredValue(e)};$.useEffect=function(e,t){return Te.current.useEffect(e,t)};$.useId=function(){return Te.current.useId()};$.useImperativeHandle=function(e,t,n){return Te.current.useImperativeHandle(e,t,n)};$.useInsertionEffect=function(e,t){return Te.current.useInsertionEffect(e,t)};$.useLayoutEffect=function(e,t){return Te.current.useLayoutEffect(e,t)};$.useMemo=function(e,t){return Te.current.useMemo(e,t)};$.useReducer=function(e,t,n){return Te.current.useReducer(e,t,n)};$.useRef=function(e){return Te.current.useRef(e)};$.useState=function(e){return Te.current.useState(e)};$.useSyncExternalStore=function(e,t,n){return Te.current.useSyncExternalStore(e,t,n)};$.useTransition=function(){return Te.current.useTransition()};$.version="18.3.1";Kf.exports=$;var _=Kf.exports;const Fe=Hf(_),Wv=Tv({__proto__:null,default:Fe},[_]);/**
 * @license React
 * react-jsx-runtime.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Kv=_,Gv=Symbol.for("react.element"),qv=Symbol.for("react.fragment"),Jv=Object.prototype.hasOwnProperty,Qv=Kv.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,Yv={key:!0,ref:!0,__self:!0,__source:!0};function tp(e,t,n){var r,i={},s=null,o=null;n!==void 0&&(s=""+n),t.key!==void 0&&(s=""+t.key),t.ref!==void 0&&(o=t.ref);for(r in t)Jv.call(t,r)&&!Yv.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps,t)i[r]===void 0&&(i[r]=t[r]);return{$$typeof:Gv,type:e,key:s,ref:o,props:i,_owner:Qv.current}}Vo.Fragment=qv;Vo.jsx=tp;Vo.jsxs=tp;Wf.exports=Vo;var v=Wf.exports,hl={},np={exports:{}},Ge={},rp={exports:{}},ip={};/**
 * @license React
 * scheduler.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */(function(e){function t(T,O){var D=T.length;T.push(O);e:for(;0<D;){var I=D-1>>>1,G=T[I];if(0<i(G,O))T[I]=O,T[D]=G,D=I;else break e}}function n(T){return T.length===0?null:T[0]}function r(T){if(T.length===0)return null;var O=T[0],D=T.pop();if(D!==O){T[0]=D;e:for(var I=0,G=T.length,Pn=G>>>1;I<Pn;){var pt=2*(I+1)-1,nr=T[pt],ze=pt+1,Tn=T[ze];if(0>i(nr,D))ze<G&&0>i(Tn,nr)?(T[I]=Tn,T[ze]=D,I=ze):(T[I]=nr,T[pt]=D,I=pt);else if(ze<G&&0>i(Tn,D))T[I]=Tn,T[ze]=D,I=ze;else break e}}return O}function i(T,O){var D=T.sortIndex-O.sortIndex;return D!==0?D:T.id-O.id}if(typeof performance=="object"&&typeof performance.now=="function"){var s=performance;e.unstable_now=function(){return s.now()}}else{var o=Date,a=o.now();e.unstable_now=function(){return o.now()-a}}var l=[],u=[],c=1,d=null,h=3,f=!1,g=!1,w=!1,x=typeof setTimeout=="function"?setTimeout:null,y=typeof clearTimeout=="function"?clearTimeout:null,p=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function m(T){for(var O=n(u);O!==null;){if(O.callback===null)r(u);else if(O.startTime<=T)r(u),O.sortIndex=O.expirationTime,t(l,O);else break;O=n(u)}}function S(T){if(w=!1,m(T),!g)if(n(l)!==null)g=!0,ne(k);else{var O=n(u);O!==null&&Je(S,O.startTime-T)}}function k(T,O){g=!1,w&&(w=!1,y(b),b=-1),f=!0;var D=h;try{for(m(O),d=n(l);d!==null&&(!(d.expirationTime>O)||T&&!ue());){var I=d.callback;if(typeof I=="function"){d.callback=null,h=d.priorityLevel;var G=I(d.expirationTime<=O);O=e.unstable_now(),typeof G=="function"?d.callback=G:d===n(l)&&r(l),m(O)}else r(l);d=n(l)}if(d!==null)var Pn=!0;else{var pt=n(u);pt!==null&&Je(S,pt.startTime-O),Pn=!1}return Pn}finally{d=null,h=D,f=!1}}var C=!1,E=null,b=-1,A=5,L=-1;function ue(){return!(e.unstable_now()-L<A)}function pe(){if(E!==null){var T=e.unstable_now();L=T;var O=!0;try{O=E(!0,T)}finally{O?Ce():(C=!1,E=null)}}else C=!1}var Ce;if(typeof p=="function")Ce=function(){p(pe)};else if(typeof MessageChannel<"u"){var ce=new MessageChannel,Ht=ce.port2;ce.port1.onmessage=pe,Ce=function(){Ht.postMessage(null)}}else Ce=function(){x(pe,0)};function ne(T){E=T,C||(C=!0,Ce())}function Je(T,O){b=x(function(){T(e.unstable_now())},O)}e.unstable_IdlePriority=5,e.unstable_ImmediatePriority=1,e.unstable_LowPriority=4,e.unstable_NormalPriority=3,e.unstable_Profiling=null,e.unstable_UserBlockingPriority=2,e.unstable_cancelCallback=function(T){T.callback=null},e.unstable_continueExecution=function(){g||f||(g=!0,ne(k))},e.unstable_forceFrameRate=function(T){0>T||125<T?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):A=0<T?Math.floor(1e3/T):5},e.unstable_getCurrentPriorityLevel=function(){return h},e.unstable_getFirstCallbackNode=function(){return n(l)},e.unstable_next=function(T){switch(h){case 1:case 2:case 3:var O=3;break;default:O=h}var D=h;h=O;try{return T()}finally{h=D}},e.unstable_pauseExecution=function(){},e.unstable_requestPaint=function(){},e.unstable_runWithPriority=function(T,O){switch(T){case 1:case 2:case 3:case 4:case 5:break;default:T=3}var D=h;h=T;try{return O()}finally{h=D}},e.unstable_scheduleCallback=function(T,O,D){var I=e.unstable_now();switch(typeof D=="object"&&D!==null?(D=D.delay,D=typeof D=="number"&&0<D?I+D:I):D=I,T){case 1:var G=-1;break;case 2:G=250;break;case 5:G=**********;break;case 4:G=1e4;break;default:G=5e3}return G=D+G,T={id:c++,callback:O,priorityLevel:T,startTime:D,expirationTime:G,sortIndex:-1},D>I?(T.sortIndex=D,t(u,T),n(l)===null&&T===n(u)&&(w?(y(b),b=-1):w=!0,Je(S,D-I))):(T.sortIndex=G,t(l,T),g||f||(g=!0,ne(k))),T},e.unstable_shouldYield=ue,e.unstable_wrapCallback=function(T){var O=h;return function(){var D=h;h=O;try{return T.apply(this,arguments)}finally{h=D}}}})(ip);rp.exports=ip;var Xv=rp.exports;/**
 * @license React
 * react-dom.production.min.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Zv=_,We=Xv;function P(e){for(var t="https://reactjs.org/docs/error-decoder.html?invariant="+e,n=1;n<arguments.length;n++)t+="&args[]="+encodeURIComponent(arguments[n]);return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}var sp=new Set,Ii={};function Yn(e,t){Or(e,t),Or(e+"Capture",t)}function Or(e,t){for(Ii[e]=t,e=0;e<t.length;e++)sp.add(t[e])}var Nt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),fl=Object.prototype.hasOwnProperty,ey=/^[:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD][:A-Z_a-z\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02FF\u0370-\u037D\u037F-\u1FFF\u200C-\u200D\u2070-\u218F\u2C00-\u2FEF\u3001-\uD7FF\uF900-\uFDCF\uFDF0-\uFFFD\-.0-9\u00B7\u0300-\u036F\u203F-\u2040]*$/,ld={},ud={};function ty(e){return fl.call(ud,e)?!0:fl.call(ld,e)?!1:ey.test(e)?ud[e]=!0:(ld[e]=!0,!1)}function ny(e,t,n,r){if(n!==null&&n.type===0)return!1;switch(typeof t){case"function":case"symbol":return!0;case"boolean":return r?!1:n!==null?!n.acceptsBooleans:(e=e.toLowerCase().slice(0,5),e!=="data-"&&e!=="aria-");default:return!1}}function ry(e,t,n,r){if(t===null||typeof t>"u"||ny(e,t,n,r))return!0;if(r)return!1;if(n!==null)switch(n.type){case 3:return!t;case 4:return t===!1;case 5:return isNaN(t);case 6:return isNaN(t)||1>t}return!1}function je(e,t,n,r,i,s,o){this.acceptsBooleans=t===2||t===3||t===4,this.attributeName=r,this.attributeNamespace=i,this.mustUseProperty=n,this.propertyName=e,this.type=t,this.sanitizeURL=s,this.removeEmptyString=o}var ye={};"children dangerouslySetInnerHTML defaultValue defaultChecked innerHTML suppressContentEditableWarning suppressHydrationWarning style".split(" ").forEach(function(e){ye[e]=new je(e,0,!1,e,null,!1,!1)});[["acceptCharset","accept-charset"],["className","class"],["htmlFor","for"],["httpEquiv","http-equiv"]].forEach(function(e){var t=e[0];ye[t]=new je(t,1,!1,e[1],null,!1,!1)});["contentEditable","draggable","spellCheck","value"].forEach(function(e){ye[e]=new je(e,2,!1,e.toLowerCase(),null,!1,!1)});["autoReverse","externalResourcesRequired","focusable","preserveAlpha"].forEach(function(e){ye[e]=new je(e,2,!1,e,null,!1,!1)});"allowFullScreen async autoFocus autoPlay controls default defer disabled disablePictureInPicture disableRemotePlayback formNoValidate hidden loop noModule noValidate open playsInline readOnly required reversed scoped seamless itemScope".split(" ").forEach(function(e){ye[e]=new je(e,3,!1,e.toLowerCase(),null,!1,!1)});["checked","multiple","muted","selected"].forEach(function(e){ye[e]=new je(e,3,!0,e,null,!1,!1)});["capture","download"].forEach(function(e){ye[e]=new je(e,4,!1,e,null,!1,!1)});["cols","rows","size","span"].forEach(function(e){ye[e]=new je(e,6,!1,e,null,!1,!1)});["rowSpan","start"].forEach(function(e){ye[e]=new je(e,5,!1,e.toLowerCase(),null,!1,!1)});var Du=/[\-:]([a-z])/g;function Vu(e){return e[1].toUpperCase()}"accent-height alignment-baseline arabic-form baseline-shift cap-height clip-path clip-rule color-interpolation color-interpolation-filters color-profile color-rendering dominant-baseline enable-background fill-opacity fill-rule flood-color flood-opacity font-family font-size font-size-adjust font-stretch font-style font-variant font-weight glyph-name glyph-orientation-horizontal glyph-orientation-vertical horiz-adv-x horiz-origin-x image-rendering letter-spacing lighting-color marker-end marker-mid marker-start overline-position overline-thickness paint-order panose-1 pointer-events rendering-intent shape-rendering stop-color stop-opacity strikethrough-position strikethrough-thickness stroke-dasharray stroke-dashoffset stroke-linecap stroke-linejoin stroke-miterlimit stroke-opacity stroke-width text-anchor text-decoration text-rendering underline-position underline-thickness unicode-bidi unicode-range units-per-em v-alphabetic v-hanging v-ideographic v-mathematical vector-effect vert-adv-y vert-origin-x vert-origin-y word-spacing writing-mode xmlns:xlink x-height".split(" ").forEach(function(e){var t=e.replace(Du,Vu);ye[t]=new je(t,1,!1,e,null,!1,!1)});"xlink:actuate xlink:arcrole xlink:role xlink:show xlink:title xlink:type".split(" ").forEach(function(e){var t=e.replace(Du,Vu);ye[t]=new je(t,1,!1,e,"http://www.w3.org/1999/xlink",!1,!1)});["xml:base","xml:lang","xml:space"].forEach(function(e){var t=e.replace(Du,Vu);ye[t]=new je(t,1,!1,e,"http://www.w3.org/XML/1998/namespace",!1,!1)});["tabIndex","crossOrigin"].forEach(function(e){ye[e]=new je(e,1,!1,e.toLowerCase(),null,!1,!1)});ye.xlinkHref=new je("xlinkHref",1,!1,"xlink:href","http://www.w3.org/1999/xlink",!0,!1);["src","href","action","formAction"].forEach(function(e){ye[e]=new je(e,1,!1,e.toLowerCase(),null,!0,!0)});function $u(e,t,n,r){var i=ye.hasOwnProperty(t)?ye[t]:null;(i!==null?i.type!==0:r||!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(ry(t,n,i,r)&&(n=null),r||i===null?ty(t)&&(n===null?e.removeAttribute(t):e.setAttribute(t,""+n)):i.mustUseProperty?e[i.propertyName]=n===null?i.type===3?!1:"":n:(t=i.attributeName,r=i.attributeNamespace,n===null?e.removeAttribute(t):(i=i.type,n=i===3||i===4&&n===!0?"":""+n,r?e.setAttributeNS(r,t,n):e.setAttribute(t,n))))}var Bt=Zv.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED,ms=Symbol.for("react.element"),lr=Symbol.for("react.portal"),ur=Symbol.for("react.fragment"),zu=Symbol.for("react.strict_mode"),pl=Symbol.for("react.profiler"),op=Symbol.for("react.provider"),ap=Symbol.for("react.context"),Bu=Symbol.for("react.forward_ref"),ml=Symbol.for("react.suspense"),gl=Symbol.for("react.suspense_list"),Uu=Symbol.for("react.memo"),Gt=Symbol.for("react.lazy"),lp=Symbol.for("react.offscreen"),cd=Symbol.iterator;function Zr(e){return e===null||typeof e!="object"?null:(e=cd&&e[cd]||e["@@iterator"],typeof e=="function"?e:null)}var Z=Object.assign,Sa;function ci(e){if(Sa===void 0)try{throw Error()}catch(n){var t=n.stack.trim().match(/\n( *(at )?)/);Sa=t&&t[1]||""}return`
`+Sa+e}var ka=!1;function Ca(e,t){if(!e||ka)return"";ka=!0;var n=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{if(t)if(t=function(){throw Error()},Object.defineProperty(t.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(t,[])}catch(u){var r=u}Reflect.construct(e,[],t)}else{try{t.call()}catch(u){r=u}e.call(t.prototype)}else{try{throw Error()}catch(u){r=u}e()}}catch(u){if(u&&r&&typeof u.stack=="string"){for(var i=u.stack.split(`
`),s=r.stack.split(`
`),o=i.length-1,a=s.length-1;1<=o&&0<=a&&i[o]!==s[a];)a--;for(;1<=o&&0<=a;o--,a--)if(i[o]!==s[a]){if(o!==1||a!==1)do if(o--,a--,0>a||i[o]!==s[a]){var l=`
`+i[o].replace(" at new "," at ");return e.displayName&&l.includes("<anonymous>")&&(l=l.replace("<anonymous>",e.displayName)),l}while(1<=o&&0<=a);break}}}finally{ka=!1,Error.prepareStackTrace=n}return(e=e?e.displayName||e.name:"")?ci(e):""}function iy(e){switch(e.tag){case 5:return ci(e.type);case 16:return ci("Lazy");case 13:return ci("Suspense");case 19:return ci("SuspenseList");case 0:case 2:case 15:return e=Ca(e.type,!1),e;case 11:return e=Ca(e.type.render,!1),e;case 1:return e=Ca(e.type,!0),e;default:return""}}function vl(e){if(e==null)return null;if(typeof e=="function")return e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case ur:return"Fragment";case lr:return"Portal";case pl:return"Profiler";case zu:return"StrictMode";case ml:return"Suspense";case gl:return"SuspenseList"}if(typeof e=="object")switch(e.$$typeof){case ap:return(e.displayName||"Context")+".Consumer";case op:return(e._context.displayName||"Context")+".Provider";case Bu:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Uu:return t=e.displayName||null,t!==null?t:vl(e.type)||"Memo";case Gt:t=e._payload,e=e._init;try{return vl(e(t))}catch{}}return null}function sy(e){var t=e.type;switch(e.tag){case 24:return"Cache";case 9:return(t.displayName||"Context")+".Consumer";case 10:return(t._context.displayName||"Context")+".Provider";case 18:return"DehydratedFragment";case 11:return e=t.render,e=e.displayName||e.name||"",t.displayName||(e!==""?"ForwardRef("+e+")":"ForwardRef");case 7:return"Fragment";case 5:return t;case 4:return"Portal";case 3:return"Root";case 6:return"Text";case 16:return vl(t);case 8:return t===zu?"StrictMode":"Mode";case 22:return"Offscreen";case 12:return"Profiler";case 21:return"Scope";case 13:return"Suspense";case 19:return"SuspenseList";case 25:return"TracingMarker";case 1:case 0:case 17:case 2:case 14:case 15:if(typeof t=="function")return t.displayName||t.name||null;if(typeof t=="string")return t}return null}function gn(e){switch(typeof e){case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function up(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function oy(e){var t=up(e)?"checked":"value",n=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),r=""+e[t];if(!e.hasOwnProperty(t)&&typeof n<"u"&&typeof n.get=="function"&&typeof n.set=="function"){var i=n.get,s=n.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return i.call(this)},set:function(o){r=""+o,s.call(this,o)}}),Object.defineProperty(e,t,{enumerable:n.enumerable}),{getValue:function(){return r},setValue:function(o){r=""+o},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function gs(e){e._valueTracker||(e._valueTracker=oy(e))}function cp(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var n=t.getValue(),r="";return e&&(r=up(e)?e.checked?"true":"false":e.value),e=r,e!==n?(t.setValue(e),!0):!1}function Zs(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function yl(e,t){var n=t.checked;return Z({},t,{defaultChecked:void 0,defaultValue:void 0,value:void 0,checked:n??e._wrapperState.initialChecked})}function dd(e,t){var n=t.defaultValue==null?"":t.defaultValue,r=t.checked!=null?t.checked:t.defaultChecked;n=gn(t.value!=null?t.value:n),e._wrapperState={initialChecked:r,initialValue:n,controlled:t.type==="checkbox"||t.type==="radio"?t.checked!=null:t.value!=null}}function dp(e,t){t=t.checked,t!=null&&$u(e,"checked",t,!1)}function wl(e,t){dp(e,t);var n=gn(t.value),r=t.type;if(n!=null)r==="number"?(n===0&&e.value===""||e.value!=n)&&(e.value=""+n):e.value!==""+n&&(e.value=""+n);else if(r==="submit"||r==="reset"){e.removeAttribute("value");return}t.hasOwnProperty("value")?xl(e,t.type,n):t.hasOwnProperty("defaultValue")&&xl(e,t.type,gn(t.defaultValue)),t.checked==null&&t.defaultChecked!=null&&(e.defaultChecked=!!t.defaultChecked)}function hd(e,t,n){if(t.hasOwnProperty("value")||t.hasOwnProperty("defaultValue")){var r=t.type;if(!(r!=="submit"&&r!=="reset"||t.value!==void 0&&t.value!==null))return;t=""+e._wrapperState.initialValue,n||t===e.value||(e.value=t),e.defaultValue=t}n=e.name,n!==""&&(e.name=""),e.defaultChecked=!!e._wrapperState.initialChecked,n!==""&&(e.name=n)}function xl(e,t,n){(t!=="number"||Zs(e.ownerDocument)!==e)&&(n==null?e.defaultValue=""+e._wrapperState.initialValue:e.defaultValue!==""+n&&(e.defaultValue=""+n))}var di=Array.isArray;function Er(e,t,n,r){if(e=e.options,t){t={};for(var i=0;i<n.length;i++)t["$"+n[i]]=!0;for(n=0;n<e.length;n++)i=t.hasOwnProperty("$"+e[n].value),e[n].selected!==i&&(e[n].selected=i),i&&r&&(e[n].defaultSelected=!0)}else{for(n=""+gn(n),t=null,i=0;i<e.length;i++){if(e[i].value===n){e[i].selected=!0,r&&(e[i].defaultSelected=!0);return}t!==null||e[i].disabled||(t=e[i])}t!==null&&(t.selected=!0)}}function _l(e,t){if(t.dangerouslySetInnerHTML!=null)throw Error(P(91));return Z({},t,{value:void 0,defaultValue:void 0,children:""+e._wrapperState.initialValue})}function fd(e,t){var n=t.value;if(n==null){if(n=t.children,t=t.defaultValue,n!=null){if(t!=null)throw Error(P(92));if(di(n)){if(1<n.length)throw Error(P(93));n=n[0]}t=n}t==null&&(t=""),n=t}e._wrapperState={initialValue:gn(n)}}function hp(e,t){var n=gn(t.value),r=gn(t.defaultValue);n!=null&&(n=""+n,n!==e.value&&(e.value=n),t.defaultValue==null&&e.defaultValue!==n&&(e.defaultValue=n)),r!=null&&(e.defaultValue=""+r)}function pd(e){var t=e.textContent;t===e._wrapperState.initialValue&&t!==""&&t!==null&&(e.value=t)}function fp(e){switch(e){case"svg":return"http://www.w3.org/2000/svg";case"math":return"http://www.w3.org/1998/Math/MathML";default:return"http://www.w3.org/1999/xhtml"}}function Sl(e,t){return e==null||e==="http://www.w3.org/1999/xhtml"?fp(t):e==="http://www.w3.org/2000/svg"&&t==="foreignObject"?"http://www.w3.org/1999/xhtml":e}var vs,pp=function(e){return typeof MSApp<"u"&&MSApp.execUnsafeLocalFunction?function(t,n,r,i){MSApp.execUnsafeLocalFunction(function(){return e(t,n,r,i)})}:e}(function(e,t){if(e.namespaceURI!=="http://www.w3.org/2000/svg"||"innerHTML"in e)e.innerHTML=t;else{for(vs=vs||document.createElement("div"),vs.innerHTML="<svg>"+t.valueOf().toString()+"</svg>",t=vs.firstChild;e.firstChild;)e.removeChild(e.firstChild);for(;t.firstChild;)e.appendChild(t.firstChild)}});function Ni(e,t){if(t){var n=e.firstChild;if(n&&n===e.lastChild&&n.nodeType===3){n.nodeValue=t;return}}e.textContent=t}var wi={animationIterationCount:!0,aspectRatio:!0,borderImageOutset:!0,borderImageSlice:!0,borderImageWidth:!0,boxFlex:!0,boxFlexGroup:!0,boxOrdinalGroup:!0,columnCount:!0,columns:!0,flex:!0,flexGrow:!0,flexPositive:!0,flexShrink:!0,flexNegative:!0,flexOrder:!0,gridArea:!0,gridRow:!0,gridRowEnd:!0,gridRowSpan:!0,gridRowStart:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnSpan:!0,gridColumnStart:!0,fontWeight:!0,lineClamp:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,tabSize:!0,widows:!0,zIndex:!0,zoom:!0,fillOpacity:!0,floodOpacity:!0,stopOpacity:!0,strokeDasharray:!0,strokeDashoffset:!0,strokeMiterlimit:!0,strokeOpacity:!0,strokeWidth:!0},ay=["Webkit","ms","Moz","O"];Object.keys(wi).forEach(function(e){ay.forEach(function(t){t=t+e.charAt(0).toUpperCase()+e.substring(1),wi[t]=wi[e]})});function mp(e,t,n){return t==null||typeof t=="boolean"||t===""?"":n||typeof t!="number"||t===0||wi.hasOwnProperty(e)&&wi[e]?(""+t).trim():t+"px"}function gp(e,t){e=e.style;for(var n in t)if(t.hasOwnProperty(n)){var r=n.indexOf("--")===0,i=mp(n,t[n],r);n==="float"&&(n="cssFloat"),r?e.setProperty(n,i):e[n]=i}}var ly=Z({menuitem:!0},{area:!0,base:!0,br:!0,col:!0,embed:!0,hr:!0,img:!0,input:!0,keygen:!0,link:!0,meta:!0,param:!0,source:!0,track:!0,wbr:!0});function kl(e,t){if(t){if(ly[e]&&(t.children!=null||t.dangerouslySetInnerHTML!=null))throw Error(P(137,e));if(t.dangerouslySetInnerHTML!=null){if(t.children!=null)throw Error(P(60));if(typeof t.dangerouslySetInnerHTML!="object"||!("__html"in t.dangerouslySetInnerHTML))throw Error(P(61))}if(t.style!=null&&typeof t.style!="object")throw Error(P(62))}}function Cl(e,t){if(e.indexOf("-")===-1)return typeof t.is=="string";switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var El=null;function Fu(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var bl=null,br=null,Pr=null;function md(e){if(e=ss(e)){if(typeof bl!="function")throw Error(P(280));var t=e.stateNode;t&&(t=Fo(t),bl(e.stateNode,e.type,t))}}function vp(e){br?Pr?Pr.push(e):Pr=[e]:br=e}function yp(){if(br){var e=br,t=Pr;if(Pr=br=null,md(e),t)for(e=0;e<t.length;e++)md(t[e])}}function wp(e,t){return e(t)}function xp(){}var Ea=!1;function _p(e,t,n){if(Ea)return e(t,n);Ea=!0;try{return wp(e,t,n)}finally{Ea=!1,(br!==null||Pr!==null)&&(xp(),yp())}}function Mi(e,t){var n=e.stateNode;if(n===null)return null;var r=Fo(n);if(r===null)return null;n=r[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(r=!r.disabled)||(e=e.type,r=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!r;break e;default:e=!1}if(e)return null;if(n&&typeof n!="function")throw Error(P(231,t,typeof n));return n}var Pl=!1;if(Nt)try{var ei={};Object.defineProperty(ei,"passive",{get:function(){Pl=!0}}),window.addEventListener("test",ei,ei),window.removeEventListener("test",ei,ei)}catch{Pl=!1}function uy(e,t,n,r,i,s,o,a,l){var u=Array.prototype.slice.call(arguments,3);try{t.apply(n,u)}catch(c){this.onError(c)}}var xi=!1,eo=null,to=!1,Tl=null,cy={onError:function(e){xi=!0,eo=e}};function dy(e,t,n,r,i,s,o,a,l){xi=!1,eo=null,uy.apply(cy,arguments)}function hy(e,t,n,r,i,s,o,a,l){if(dy.apply(this,arguments),xi){if(xi){var u=eo;xi=!1,eo=null}else throw Error(P(198));to||(to=!0,Tl=u)}}function Xn(e){var t=e,n=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,t.flags&4098&&(n=t.return),e=t.return;while(e)}return t.tag===3?n:null}function Sp(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function gd(e){if(Xn(e)!==e)throw Error(P(188))}function fy(e){var t=e.alternate;if(!t){if(t=Xn(e),t===null)throw Error(P(188));return t!==e?null:e}for(var n=e,r=t;;){var i=n.return;if(i===null)break;var s=i.alternate;if(s===null){if(r=i.return,r!==null){n=r;continue}break}if(i.child===s.child){for(s=i.child;s;){if(s===n)return gd(i),e;if(s===r)return gd(i),t;s=s.sibling}throw Error(P(188))}if(n.return!==r.return)n=i,r=s;else{for(var o=!1,a=i.child;a;){if(a===n){o=!0,n=i,r=s;break}if(a===r){o=!0,r=i,n=s;break}a=a.sibling}if(!o){for(a=s.child;a;){if(a===n){o=!0,n=s,r=i;break}if(a===r){o=!0,r=s,n=i;break}a=a.sibling}if(!o)throw Error(P(189))}}if(n.alternate!==r)throw Error(P(190))}if(n.tag!==3)throw Error(P(188));return n.stateNode.current===n?e:t}function kp(e){return e=fy(e),e!==null?Cp(e):null}function Cp(e){if(e.tag===5||e.tag===6)return e;for(e=e.child;e!==null;){var t=Cp(e);if(t!==null)return t;e=e.sibling}return null}var Ep=We.unstable_scheduleCallback,vd=We.unstable_cancelCallback,py=We.unstable_shouldYield,my=We.unstable_requestPaint,re=We.unstable_now,gy=We.unstable_getCurrentPriorityLevel,Hu=We.unstable_ImmediatePriority,bp=We.unstable_UserBlockingPriority,no=We.unstable_NormalPriority,vy=We.unstable_LowPriority,Pp=We.unstable_IdlePriority,$o=null,wt=null;function yy(e){if(wt&&typeof wt.onCommitFiberRoot=="function")try{wt.onCommitFiberRoot($o,e,void 0,(e.current.flags&128)===128)}catch{}}var dt=Math.clz32?Math.clz32:_y,wy=Math.log,xy=Math.LN2;function _y(e){return e>>>=0,e===0?32:31-(wy(e)/xy|0)|0}var ys=64,ws=4194304;function hi(e){switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194240;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return e&130023424;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 1073741824;default:return e}}function ro(e,t){var n=e.pendingLanes;if(n===0)return 0;var r=0,i=e.suspendedLanes,s=e.pingedLanes,o=n&268435455;if(o!==0){var a=o&~i;a!==0?r=hi(a):(s&=o,s!==0&&(r=hi(s)))}else o=n&~i,o!==0?r=hi(o):s!==0&&(r=hi(s));if(r===0)return 0;if(t!==0&&t!==r&&!(t&i)&&(i=r&-r,s=t&-t,i>=s||i===16&&(s&4194240)!==0))return t;if(r&4&&(r|=n&16),t=e.entangledLanes,t!==0)for(e=e.entanglements,t&=r;0<t;)n=31-dt(t),i=1<<n,r|=e[n],t&=~i;return r}function Sy(e,t){switch(e){case 1:case 2:case 4:return t+250;case 8:case 16:case 32:case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:return-1;case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function ky(e,t){for(var n=e.suspendedLanes,r=e.pingedLanes,i=e.expirationTimes,s=e.pendingLanes;0<s;){var o=31-dt(s),a=1<<o,l=i[o];l===-1?(!(a&n)||a&r)&&(i[o]=Sy(a,t)):l<=t&&(e.expiredLanes|=a),s&=~a}}function jl(e){return e=e.pendingLanes&-1073741825,e!==0?e:e&1073741824?1073741824:0}function Tp(){var e=ys;return ys<<=1,!(ys&4194240)&&(ys=64),e}function ba(e){for(var t=[],n=0;31>n;n++)t.push(e);return t}function rs(e,t,n){e.pendingLanes|=t,t!==536870912&&(e.suspendedLanes=0,e.pingedLanes=0),e=e.eventTimes,t=31-dt(t),e[t]=n}function Cy(e,t){var n=e.pendingLanes&~t;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.expiredLanes&=t,e.mutableReadLanes&=t,e.entangledLanes&=t,t=e.entanglements;var r=e.eventTimes;for(e=e.expirationTimes;0<n;){var i=31-dt(n),s=1<<i;t[i]=0,r[i]=-1,e[i]=-1,n&=~s}}function Wu(e,t){var n=e.entangledLanes|=t;for(e=e.entanglements;n;){var r=31-dt(n),i=1<<r;i&t|e[r]&t&&(e[r]|=t),n&=~i}}var B=0;function jp(e){return e&=-e,1<e?4<e?e&268435455?16:536870912:4:1}var Ap,Ku,Rp,Lp,Op,Al=!1,xs=[],rn=null,sn=null,on=null,Di=new Map,Vi=new Map,Yt=[],Ey="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset submit".split(" ");function yd(e,t){switch(e){case"focusin":case"focusout":rn=null;break;case"dragenter":case"dragleave":sn=null;break;case"mouseover":case"mouseout":on=null;break;case"pointerover":case"pointerout":Di.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":Vi.delete(t.pointerId)}}function ti(e,t,n,r,i,s){return e===null||e.nativeEvent!==s?(e={blockedOn:t,domEventName:n,eventSystemFlags:r,nativeEvent:s,targetContainers:[i]},t!==null&&(t=ss(t),t!==null&&Ku(t)),e):(e.eventSystemFlags|=r,t=e.targetContainers,i!==null&&t.indexOf(i)===-1&&t.push(i),e)}function by(e,t,n,r,i){switch(t){case"focusin":return rn=ti(rn,e,t,n,r,i),!0;case"dragenter":return sn=ti(sn,e,t,n,r,i),!0;case"mouseover":return on=ti(on,e,t,n,r,i),!0;case"pointerover":var s=i.pointerId;return Di.set(s,ti(Di.get(s)||null,e,t,n,r,i)),!0;case"gotpointercapture":return s=i.pointerId,Vi.set(s,ti(Vi.get(s)||null,e,t,n,r,i)),!0}return!1}function Ip(e){var t=zn(e.target);if(t!==null){var n=Xn(t);if(n!==null){if(t=n.tag,t===13){if(t=Sp(n),t!==null){e.blockedOn=t,Op(e.priority,function(){Rp(n)});return}}else if(t===3&&n.stateNode.current.memoizedState.isDehydrated){e.blockedOn=n.tag===3?n.stateNode.containerInfo:null;return}}}e.blockedOn=null}function $s(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var n=Rl(e.domEventName,e.eventSystemFlags,t[0],e.nativeEvent);if(n===null){n=e.nativeEvent;var r=new n.constructor(n.type,n);El=r,n.target.dispatchEvent(r),El=null}else return t=ss(n),t!==null&&Ku(t),e.blockedOn=n,!1;t.shift()}return!0}function wd(e,t,n){$s(e)&&n.delete(t)}function Py(){Al=!1,rn!==null&&$s(rn)&&(rn=null),sn!==null&&$s(sn)&&(sn=null),on!==null&&$s(on)&&(on=null),Di.forEach(wd),Vi.forEach(wd)}function ni(e,t){e.blockedOn===t&&(e.blockedOn=null,Al||(Al=!0,We.unstable_scheduleCallback(We.unstable_NormalPriority,Py)))}function $i(e){function t(i){return ni(i,e)}if(0<xs.length){ni(xs[0],e);for(var n=1;n<xs.length;n++){var r=xs[n];r.blockedOn===e&&(r.blockedOn=null)}}for(rn!==null&&ni(rn,e),sn!==null&&ni(sn,e),on!==null&&ni(on,e),Di.forEach(t),Vi.forEach(t),n=0;n<Yt.length;n++)r=Yt[n],r.blockedOn===e&&(r.blockedOn=null);for(;0<Yt.length&&(n=Yt[0],n.blockedOn===null);)Ip(n),n.blockedOn===null&&Yt.shift()}var Tr=Bt.ReactCurrentBatchConfig,io=!0;function Ty(e,t,n,r){var i=B,s=Tr.transition;Tr.transition=null;try{B=1,Gu(e,t,n,r)}finally{B=i,Tr.transition=s}}function jy(e,t,n,r){var i=B,s=Tr.transition;Tr.transition=null;try{B=4,Gu(e,t,n,r)}finally{B=i,Tr.transition=s}}function Gu(e,t,n,r){if(io){var i=Rl(e,t,n,r);if(i===null)Ma(e,t,r,so,n),yd(e,r);else if(by(i,e,t,n,r))r.stopPropagation();else if(yd(e,r),t&4&&-1<Ey.indexOf(e)){for(;i!==null;){var s=ss(i);if(s!==null&&Ap(s),s=Rl(e,t,n,r),s===null&&Ma(e,t,r,so,n),s===i)break;i=s}i!==null&&r.stopPropagation()}else Ma(e,t,r,null,n)}}var so=null;function Rl(e,t,n,r){if(so=null,e=Fu(r),e=zn(e),e!==null)if(t=Xn(e),t===null)e=null;else if(n=t.tag,n===13){if(e=Sp(t),e!==null)return e;e=null}else if(n===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null);return so=e,null}function Np(e){switch(e){case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 1;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"toggle":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 4;case"message":switch(gy()){case Hu:return 1;case bp:return 4;case no:case vy:return 16;case Pp:return 536870912;default:return 16}default:return 16}}var en=null,qu=null,zs=null;function Mp(){if(zs)return zs;var e,t=qu,n=t.length,r,i="value"in en?en.value:en.textContent,s=i.length;for(e=0;e<n&&t[e]===i[e];e++);var o=n-e;for(r=1;r<=o&&t[n-r]===i[s-r];r++);return zs=i.slice(e,1<r?1-r:void 0)}function Bs(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function _s(){return!0}function xd(){return!1}function qe(e){function t(n,r,i,s,o){this._reactName=n,this._targetInst=i,this.type=r,this.nativeEvent=s,this.target=o,this.currentTarget=null;for(var a in e)e.hasOwnProperty(a)&&(n=e[a],this[a]=n?n(s):s[a]);return this.isDefaultPrevented=(s.defaultPrevented!=null?s.defaultPrevented:s.returnValue===!1)?_s:xd,this.isPropagationStopped=xd,this}return Z(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var n=this.nativeEvent;n&&(n.preventDefault?n.preventDefault():typeof n.returnValue!="unknown"&&(n.returnValue=!1),this.isDefaultPrevented=_s)},stopPropagation:function(){var n=this.nativeEvent;n&&(n.stopPropagation?n.stopPropagation():typeof n.cancelBubble!="unknown"&&(n.cancelBubble=!0),this.isPropagationStopped=_s)},persist:function(){},isPersistent:_s}),t}var Kr={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Ju=qe(Kr),is=Z({},Kr,{view:0,detail:0}),Ay=qe(is),Pa,Ta,ri,zo=Z({},is,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Qu,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==ri&&(ri&&e.type==="mousemove"?(Pa=e.screenX-ri.screenX,Ta=e.screenY-ri.screenY):Ta=Pa=0,ri=e),Pa)},movementY:function(e){return"movementY"in e?e.movementY:Ta}}),_d=qe(zo),Ry=Z({},zo,{dataTransfer:0}),Ly=qe(Ry),Oy=Z({},is,{relatedTarget:0}),ja=qe(Oy),Iy=Z({},Kr,{animationName:0,elapsedTime:0,pseudoElement:0}),Ny=qe(Iy),My=Z({},Kr,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),Dy=qe(My),Vy=Z({},Kr,{data:0}),Sd=qe(Vy),$y={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},zy={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},By={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Uy(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=By[e])?!!t[e]:!1}function Qu(){return Uy}var Fy=Z({},is,{key:function(e){if(e.key){var t=$y[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=Bs(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?zy[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Qu,charCode:function(e){return e.type==="keypress"?Bs(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?Bs(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Hy=qe(Fy),Wy=Z({},zo,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),kd=qe(Wy),Ky=Z({},is,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Qu}),Gy=qe(Ky),qy=Z({},Kr,{propertyName:0,elapsedTime:0,pseudoElement:0}),Jy=qe(qy),Qy=Z({},zo,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),Yy=qe(Qy),Xy=[9,13,27,32],Yu=Nt&&"CompositionEvent"in window,_i=null;Nt&&"documentMode"in document&&(_i=document.documentMode);var Zy=Nt&&"TextEvent"in window&&!_i,Dp=Nt&&(!Yu||_i&&8<_i&&11>=_i),Cd=String.fromCharCode(32),Ed=!1;function Vp(e,t){switch(e){case"keyup":return Xy.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function $p(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var cr=!1;function e1(e,t){switch(e){case"compositionend":return $p(t);case"keypress":return t.which!==32?null:(Ed=!0,Cd);case"textInput":return e=t.data,e===Cd&&Ed?null:e;default:return null}}function t1(e,t){if(cr)return e==="compositionend"||!Yu&&Vp(e,t)?(e=Mp(),zs=qu=en=null,cr=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Dp&&t.locale!=="ko"?null:t.data;default:return null}}var n1={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function bd(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!n1[e.type]:t==="textarea"}function zp(e,t,n,r){vp(r),t=oo(t,"onChange"),0<t.length&&(n=new Ju("onChange","change",null,n,r),e.push({event:n,listeners:t}))}var Si=null,zi=null;function r1(e){Yp(e,0)}function Bo(e){var t=fr(e);if(cp(t))return e}function i1(e,t){if(e==="change")return t}var Bp=!1;if(Nt){var Aa;if(Nt){var Ra="oninput"in document;if(!Ra){var Pd=document.createElement("div");Pd.setAttribute("oninput","return;"),Ra=typeof Pd.oninput=="function"}Aa=Ra}else Aa=!1;Bp=Aa&&(!document.documentMode||9<document.documentMode)}function Td(){Si&&(Si.detachEvent("onpropertychange",Up),zi=Si=null)}function Up(e){if(e.propertyName==="value"&&Bo(zi)){var t=[];zp(t,zi,e,Fu(e)),_p(r1,t)}}function s1(e,t,n){e==="focusin"?(Td(),Si=t,zi=n,Si.attachEvent("onpropertychange",Up)):e==="focusout"&&Td()}function o1(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Bo(zi)}function a1(e,t){if(e==="click")return Bo(t)}function l1(e,t){if(e==="input"||e==="change")return Bo(t)}function u1(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var ft=typeof Object.is=="function"?Object.is:u1;function Bi(e,t){if(ft(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var n=Object.keys(e),r=Object.keys(t);if(n.length!==r.length)return!1;for(r=0;r<n.length;r++){var i=n[r];if(!fl.call(t,i)||!ft(e[i],t[i]))return!1}return!0}function jd(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Ad(e,t){var n=jd(e);e=0;for(var r;n;){if(n.nodeType===3){if(r=e+n.textContent.length,e<=t&&r>=t)return{node:n,offset:t-e};e=r}e:{for(;n;){if(n.nextSibling){n=n.nextSibling;break e}n=n.parentNode}n=void 0}n=jd(n)}}function Fp(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Fp(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Hp(){for(var e=window,t=Zs();t instanceof e.HTMLIFrameElement;){try{var n=typeof t.contentWindow.location.href=="string"}catch{n=!1}if(n)e=t.contentWindow;else break;t=Zs(e.document)}return t}function Xu(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}function c1(e){var t=Hp(),n=e.focusedElem,r=e.selectionRange;if(t!==n&&n&&n.ownerDocument&&Fp(n.ownerDocument.documentElement,n)){if(r!==null&&Xu(n)){if(t=r.start,e=r.end,e===void 0&&(e=t),"selectionStart"in n)n.selectionStart=t,n.selectionEnd=Math.min(e,n.value.length);else if(e=(t=n.ownerDocument||document)&&t.defaultView||window,e.getSelection){e=e.getSelection();var i=n.textContent.length,s=Math.min(r.start,i);r=r.end===void 0?s:Math.min(r.end,i),!e.extend&&s>r&&(i=r,r=s,s=i),i=Ad(n,s);var o=Ad(n,r);i&&o&&(e.rangeCount!==1||e.anchorNode!==i.node||e.anchorOffset!==i.offset||e.focusNode!==o.node||e.focusOffset!==o.offset)&&(t=t.createRange(),t.setStart(i.node,i.offset),e.removeAllRanges(),s>r?(e.addRange(t),e.extend(o.node,o.offset)):(t.setEnd(o.node,o.offset),e.addRange(t)))}}for(t=[],e=n;e=e.parentNode;)e.nodeType===1&&t.push({element:e,left:e.scrollLeft,top:e.scrollTop});for(typeof n.focus=="function"&&n.focus(),n=0;n<t.length;n++)e=t[n],e.element.scrollLeft=e.left,e.element.scrollTop=e.top}}var d1=Nt&&"documentMode"in document&&11>=document.documentMode,dr=null,Ll=null,ki=null,Ol=!1;function Rd(e,t,n){var r=n.window===n?n.document:n.nodeType===9?n:n.ownerDocument;Ol||dr==null||dr!==Zs(r)||(r=dr,"selectionStart"in r&&Xu(r)?r={start:r.selectionStart,end:r.selectionEnd}:(r=(r.ownerDocument&&r.ownerDocument.defaultView||window).getSelection(),r={anchorNode:r.anchorNode,anchorOffset:r.anchorOffset,focusNode:r.focusNode,focusOffset:r.focusOffset}),ki&&Bi(ki,r)||(ki=r,r=oo(Ll,"onSelect"),0<r.length&&(t=new Ju("onSelect","select",null,t,n),e.push({event:t,listeners:r}),t.target=dr)))}function Ss(e,t){var n={};return n[e.toLowerCase()]=t.toLowerCase(),n["Webkit"+e]="webkit"+t,n["Moz"+e]="moz"+t,n}var hr={animationend:Ss("Animation","AnimationEnd"),animationiteration:Ss("Animation","AnimationIteration"),animationstart:Ss("Animation","AnimationStart"),transitionend:Ss("Transition","TransitionEnd")},La={},Wp={};Nt&&(Wp=document.createElement("div").style,"AnimationEvent"in window||(delete hr.animationend.animation,delete hr.animationiteration.animation,delete hr.animationstart.animation),"TransitionEvent"in window||delete hr.transitionend.transition);function Uo(e){if(La[e])return La[e];if(!hr[e])return e;var t=hr[e],n;for(n in t)if(t.hasOwnProperty(n)&&n in Wp)return La[e]=t[n];return e}var Kp=Uo("animationend"),Gp=Uo("animationiteration"),qp=Uo("animationstart"),Jp=Uo("transitionend"),Qp=new Map,Ld="abort auxClick cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");function xn(e,t){Qp.set(e,t),Yn(t,[e])}for(var Oa=0;Oa<Ld.length;Oa++){var Ia=Ld[Oa],h1=Ia.toLowerCase(),f1=Ia[0].toUpperCase()+Ia.slice(1);xn(h1,"on"+f1)}xn(Kp,"onAnimationEnd");xn(Gp,"onAnimationIteration");xn(qp,"onAnimationStart");xn("dblclick","onDoubleClick");xn("focusin","onFocus");xn("focusout","onBlur");xn(Jp,"onTransitionEnd");Or("onMouseEnter",["mouseout","mouseover"]);Or("onMouseLeave",["mouseout","mouseover"]);Or("onPointerEnter",["pointerout","pointerover"]);Or("onPointerLeave",["pointerout","pointerover"]);Yn("onChange","change click focusin focusout input keydown keyup selectionchange".split(" "));Yn("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" "));Yn("onBeforeInput",["compositionend","keypress","textInput","paste"]);Yn("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" "));Yn("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" "));Yn("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var fi="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),p1=new Set("cancel close invalid load scroll toggle".split(" ").concat(fi));function Od(e,t,n){var r=e.type||"unknown-event";e.currentTarget=n,hy(r,t,void 0,e),e.currentTarget=null}function Yp(e,t){t=(t&4)!==0;for(var n=0;n<e.length;n++){var r=e[n],i=r.event;r=r.listeners;e:{var s=void 0;if(t)for(var o=r.length-1;0<=o;o--){var a=r[o],l=a.instance,u=a.currentTarget;if(a=a.listener,l!==s&&i.isPropagationStopped())break e;Od(i,a,u),s=l}else for(o=0;o<r.length;o++){if(a=r[o],l=a.instance,u=a.currentTarget,a=a.listener,l!==s&&i.isPropagationStopped())break e;Od(i,a,u),s=l}}}if(to)throw e=Tl,to=!1,Tl=null,e}function W(e,t){var n=t[Vl];n===void 0&&(n=t[Vl]=new Set);var r=e+"__bubble";n.has(r)||(Xp(t,e,2,!1),n.add(r))}function Na(e,t,n){var r=0;t&&(r|=4),Xp(n,e,r,t)}var ks="_reactListening"+Math.random().toString(36).slice(2);function Ui(e){if(!e[ks]){e[ks]=!0,sp.forEach(function(n){n!=="selectionchange"&&(p1.has(n)||Na(n,!1,e),Na(n,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[ks]||(t[ks]=!0,Na("selectionchange",!1,t))}}function Xp(e,t,n,r){switch(Np(t)){case 1:var i=Ty;break;case 4:i=jy;break;default:i=Gu}n=i.bind(null,t,n,e),i=void 0,!Pl||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(i=!0),r?i!==void 0?e.addEventListener(t,n,{capture:!0,passive:i}):e.addEventListener(t,n,!0):i!==void 0?e.addEventListener(t,n,{passive:i}):e.addEventListener(t,n,!1)}function Ma(e,t,n,r,i){var s=r;if(!(t&1)&&!(t&2)&&r!==null)e:for(;;){if(r===null)return;var o=r.tag;if(o===3||o===4){var a=r.stateNode.containerInfo;if(a===i||a.nodeType===8&&a.parentNode===i)break;if(o===4)for(o=r.return;o!==null;){var l=o.tag;if((l===3||l===4)&&(l=o.stateNode.containerInfo,l===i||l.nodeType===8&&l.parentNode===i))return;o=o.return}for(;a!==null;){if(o=zn(a),o===null)return;if(l=o.tag,l===5||l===6){r=s=o;continue e}a=a.parentNode}}r=r.return}_p(function(){var u=s,c=Fu(n),d=[];e:{var h=Qp.get(e);if(h!==void 0){var f=Ju,g=e;switch(e){case"keypress":if(Bs(n)===0)break e;case"keydown":case"keyup":f=Hy;break;case"focusin":g="focus",f=ja;break;case"focusout":g="blur",f=ja;break;case"beforeblur":case"afterblur":f=ja;break;case"click":if(n.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":f=_d;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":f=Ly;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":f=Gy;break;case Kp:case Gp:case qp:f=Ny;break;case Jp:f=Jy;break;case"scroll":f=Ay;break;case"wheel":f=Yy;break;case"copy":case"cut":case"paste":f=Dy;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":f=kd}var w=(t&4)!==0,x=!w&&e==="scroll",y=w?h!==null?h+"Capture":null:h;w=[];for(var p=u,m;p!==null;){m=p;var S=m.stateNode;if(m.tag===5&&S!==null&&(m=S,y!==null&&(S=Mi(p,y),S!=null&&w.push(Fi(p,S,m)))),x)break;p=p.return}0<w.length&&(h=new f(h,g,null,n,c),d.push({event:h,listeners:w}))}}if(!(t&7)){e:{if(h=e==="mouseover"||e==="pointerover",f=e==="mouseout"||e==="pointerout",h&&n!==El&&(g=n.relatedTarget||n.fromElement)&&(zn(g)||g[Mt]))break e;if((f||h)&&(h=c.window===c?c:(h=c.ownerDocument)?h.defaultView||h.parentWindow:window,f?(g=n.relatedTarget||n.toElement,f=u,g=g?zn(g):null,g!==null&&(x=Xn(g),g!==x||g.tag!==5&&g.tag!==6)&&(g=null)):(f=null,g=u),f!==g)){if(w=_d,S="onMouseLeave",y="onMouseEnter",p="mouse",(e==="pointerout"||e==="pointerover")&&(w=kd,S="onPointerLeave",y="onPointerEnter",p="pointer"),x=f==null?h:fr(f),m=g==null?h:fr(g),h=new w(S,p+"leave",f,n,c),h.target=x,h.relatedTarget=m,S=null,zn(c)===u&&(w=new w(y,p+"enter",g,n,c),w.target=m,w.relatedTarget=x,S=w),x=S,f&&g)t:{for(w=f,y=g,p=0,m=w;m;m=rr(m))p++;for(m=0,S=y;S;S=rr(S))m++;for(;0<p-m;)w=rr(w),p--;for(;0<m-p;)y=rr(y),m--;for(;p--;){if(w===y||y!==null&&w===y.alternate)break t;w=rr(w),y=rr(y)}w=null}else w=null;f!==null&&Id(d,h,f,w,!1),g!==null&&x!==null&&Id(d,x,g,w,!0)}}e:{if(h=u?fr(u):window,f=h.nodeName&&h.nodeName.toLowerCase(),f==="select"||f==="input"&&h.type==="file")var k=i1;else if(bd(h))if(Bp)k=l1;else{k=o1;var C=s1}else(f=h.nodeName)&&f.toLowerCase()==="input"&&(h.type==="checkbox"||h.type==="radio")&&(k=a1);if(k&&(k=k(e,u))){zp(d,k,n,c);break e}C&&C(e,h,u),e==="focusout"&&(C=h._wrapperState)&&C.controlled&&h.type==="number"&&xl(h,"number",h.value)}switch(C=u?fr(u):window,e){case"focusin":(bd(C)||C.contentEditable==="true")&&(dr=C,Ll=u,ki=null);break;case"focusout":ki=Ll=dr=null;break;case"mousedown":Ol=!0;break;case"contextmenu":case"mouseup":case"dragend":Ol=!1,Rd(d,n,c);break;case"selectionchange":if(d1)break;case"keydown":case"keyup":Rd(d,n,c)}var E;if(Yu)e:{switch(e){case"compositionstart":var b="onCompositionStart";break e;case"compositionend":b="onCompositionEnd";break e;case"compositionupdate":b="onCompositionUpdate";break e}b=void 0}else cr?Vp(e,n)&&(b="onCompositionEnd"):e==="keydown"&&n.keyCode===229&&(b="onCompositionStart");b&&(Dp&&n.locale!=="ko"&&(cr||b!=="onCompositionStart"?b==="onCompositionEnd"&&cr&&(E=Mp()):(en=c,qu="value"in en?en.value:en.textContent,cr=!0)),C=oo(u,b),0<C.length&&(b=new Sd(b,e,null,n,c),d.push({event:b,listeners:C}),E?b.data=E:(E=$p(n),E!==null&&(b.data=E)))),(E=Zy?e1(e,n):t1(e,n))&&(u=oo(u,"onBeforeInput"),0<u.length&&(c=new Sd("onBeforeInput","beforeinput",null,n,c),d.push({event:c,listeners:u}),c.data=E))}Yp(d,t)})}function Fi(e,t,n){return{instance:e,listener:t,currentTarget:n}}function oo(e,t){for(var n=t+"Capture",r=[];e!==null;){var i=e,s=i.stateNode;i.tag===5&&s!==null&&(i=s,s=Mi(e,n),s!=null&&r.unshift(Fi(e,s,i)),s=Mi(e,t),s!=null&&r.push(Fi(e,s,i))),e=e.return}return r}function rr(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5);return e||null}function Id(e,t,n,r,i){for(var s=t._reactName,o=[];n!==null&&n!==r;){var a=n,l=a.alternate,u=a.stateNode;if(l!==null&&l===r)break;a.tag===5&&u!==null&&(a=u,i?(l=Mi(n,s),l!=null&&o.unshift(Fi(n,l,a))):i||(l=Mi(n,s),l!=null&&o.push(Fi(n,l,a)))),n=n.return}o.length!==0&&e.push({event:t,listeners:o})}var m1=/\r\n?/g,g1=/\u0000|\uFFFD/g;function Nd(e){return(typeof e=="string"?e:""+e).replace(m1,`
`).replace(g1,"")}function Cs(e,t,n){if(t=Nd(t),Nd(e)!==t&&n)throw Error(P(425))}function ao(){}var Il=null,Nl=null;function Ml(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Dl=typeof setTimeout=="function"?setTimeout:void 0,v1=typeof clearTimeout=="function"?clearTimeout:void 0,Md=typeof Promise=="function"?Promise:void 0,y1=typeof queueMicrotask=="function"?queueMicrotask:typeof Md<"u"?function(e){return Md.resolve(null).then(e).catch(w1)}:Dl;function w1(e){setTimeout(function(){throw e})}function Da(e,t){var n=t,r=0;do{var i=n.nextSibling;if(e.removeChild(n),i&&i.nodeType===8)if(n=i.data,n==="/$"){if(r===0){e.removeChild(i),$i(t);return}r--}else n!=="$"&&n!=="$?"&&n!=="$!"||r++;n=i}while(n);$i(t)}function an(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?")break;if(t==="/$")return null}}return e}function Dd(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="$"||n==="$!"||n==="$?"){if(t===0)return e;t--}else n==="/$"&&t++}e=e.previousSibling}return null}var Gr=Math.random().toString(36).slice(2),yt="__reactFiber$"+Gr,Hi="__reactProps$"+Gr,Mt="__reactContainer$"+Gr,Vl="__reactEvents$"+Gr,x1="__reactListeners$"+Gr,_1="__reactHandles$"+Gr;function zn(e){var t=e[yt];if(t)return t;for(var n=e.parentNode;n;){if(t=n[Mt]||n[yt]){if(n=t.alternate,t.child!==null||n!==null&&n.child!==null)for(e=Dd(e);e!==null;){if(n=e[yt])return n;e=Dd(e)}return t}e=n,n=e.parentNode}return null}function ss(e){return e=e[yt]||e[Mt],!e||e.tag!==5&&e.tag!==6&&e.tag!==13&&e.tag!==3?null:e}function fr(e){if(e.tag===5||e.tag===6)return e.stateNode;throw Error(P(33))}function Fo(e){return e[Hi]||null}var $l=[],pr=-1;function _n(e){return{current:e}}function K(e){0>pr||(e.current=$l[pr],$l[pr]=null,pr--)}function F(e,t){pr++,$l[pr]=e.current,e.current=t}var vn={},ke=_n(vn),Ne=_n(!1),Kn=vn;function Ir(e,t){var n=e.type.contextTypes;if(!n)return vn;var r=e.stateNode;if(r&&r.__reactInternalMemoizedUnmaskedChildContext===t)return r.__reactInternalMemoizedMaskedChildContext;var i={},s;for(s in n)i[s]=t[s];return r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=t,e.__reactInternalMemoizedMaskedChildContext=i),i}function Me(e){return e=e.childContextTypes,e!=null}function lo(){K(Ne),K(ke)}function Vd(e,t,n){if(ke.current!==vn)throw Error(P(168));F(ke,t),F(Ne,n)}function Zp(e,t,n){var r=e.stateNode;if(t=t.childContextTypes,typeof r.getChildContext!="function")return n;r=r.getChildContext();for(var i in r)if(!(i in t))throw Error(P(108,sy(e)||"Unknown",i));return Z({},n,r)}function uo(e){return e=(e=e.stateNode)&&e.__reactInternalMemoizedMergedChildContext||vn,Kn=ke.current,F(ke,e),F(Ne,Ne.current),!0}function $d(e,t,n){var r=e.stateNode;if(!r)throw Error(P(169));n?(e=Zp(e,t,Kn),r.__reactInternalMemoizedMergedChildContext=e,K(Ne),K(ke),F(ke,e)):K(Ne),F(Ne,n)}var Tt=null,Ho=!1,Va=!1;function e0(e){Tt===null?Tt=[e]:Tt.push(e)}function S1(e){Ho=!0,e0(e)}function Sn(){if(!Va&&Tt!==null){Va=!0;var e=0,t=B;try{var n=Tt;for(B=1;e<n.length;e++){var r=n[e];do r=r(!0);while(r!==null)}Tt=null,Ho=!1}catch(i){throw Tt!==null&&(Tt=Tt.slice(e+1)),Ep(Hu,Sn),i}finally{B=t,Va=!1}}return null}var mr=[],gr=0,co=null,ho=0,Xe=[],Ze=0,Gn=null,jt=1,At="";function Ln(e,t){mr[gr++]=ho,mr[gr++]=co,co=e,ho=t}function t0(e,t,n){Xe[Ze++]=jt,Xe[Ze++]=At,Xe[Ze++]=Gn,Gn=e;var r=jt;e=At;var i=32-dt(r)-1;r&=~(1<<i),n+=1;var s=32-dt(t)+i;if(30<s){var o=i-i%5;s=(r&(1<<o)-1).toString(32),r>>=o,i-=o,jt=1<<32-dt(t)+i|n<<i|r,At=s+e}else jt=1<<s|n<<i|r,At=e}function Zu(e){e.return!==null&&(Ln(e,1),t0(e,1,0))}function ec(e){for(;e===co;)co=mr[--gr],mr[gr]=null,ho=mr[--gr],mr[gr]=null;for(;e===Gn;)Gn=Xe[--Ze],Xe[Ze]=null,At=Xe[--Ze],Xe[Ze]=null,jt=Xe[--Ze],Xe[Ze]=null}var He=null,Ue=null,q=!1,ct=null;function n0(e,t){var n=et(5,null,null,0);n.elementType="DELETED",n.stateNode=t,n.return=e,t=e.deletions,t===null?(e.deletions=[n],e.flags|=16):t.push(n)}function zd(e,t){switch(e.tag){case 5:var n=e.type;return t=t.nodeType!==1||n.toLowerCase()!==t.nodeName.toLowerCase()?null:t,t!==null?(e.stateNode=t,He=e,Ue=an(t.firstChild),!0):!1;case 6:return t=e.pendingProps===""||t.nodeType!==3?null:t,t!==null?(e.stateNode=t,He=e,Ue=null,!0):!1;case 13:return t=t.nodeType!==8?null:t,t!==null?(n=Gn!==null?{id:jt,overflow:At}:null,e.memoizedState={dehydrated:t,treeContext:n,retryLane:1073741824},n=et(18,null,null,0),n.stateNode=t,n.return=e,e.child=n,He=e,Ue=null,!0):!1;default:return!1}}function zl(e){return(e.mode&1)!==0&&(e.flags&128)===0}function Bl(e){if(q){var t=Ue;if(t){var n=t;if(!zd(e,t)){if(zl(e))throw Error(P(418));t=an(n.nextSibling);var r=He;t&&zd(e,t)?n0(r,n):(e.flags=e.flags&-4097|2,q=!1,He=e)}}else{if(zl(e))throw Error(P(418));e.flags=e.flags&-4097|2,q=!1,He=e}}}function Bd(e){for(e=e.return;e!==null&&e.tag!==5&&e.tag!==3&&e.tag!==13;)e=e.return;He=e}function Es(e){if(e!==He)return!1;if(!q)return Bd(e),q=!0,!1;var t;if((t=e.tag!==3)&&!(t=e.tag!==5)&&(t=e.type,t=t!=="head"&&t!=="body"&&!Ml(e.type,e.memoizedProps)),t&&(t=Ue)){if(zl(e))throw r0(),Error(P(418));for(;t;)n0(e,t),t=an(t.nextSibling)}if(Bd(e),e.tag===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(P(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8){var n=e.data;if(n==="/$"){if(t===0){Ue=an(e.nextSibling);break e}t--}else n!=="$"&&n!=="$!"&&n!=="$?"||t++}e=e.nextSibling}Ue=null}}else Ue=He?an(e.stateNode.nextSibling):null;return!0}function r0(){for(var e=Ue;e;)e=an(e.nextSibling)}function Nr(){Ue=He=null,q=!1}function tc(e){ct===null?ct=[e]:ct.push(e)}var k1=Bt.ReactCurrentBatchConfig;function ii(e,t,n){if(e=n.ref,e!==null&&typeof e!="function"&&typeof e!="object"){if(n._owner){if(n=n._owner,n){if(n.tag!==1)throw Error(P(309));var r=n.stateNode}if(!r)throw Error(P(147,e));var i=r,s=""+e;return t!==null&&t.ref!==null&&typeof t.ref=="function"&&t.ref._stringRef===s?t.ref:(t=function(o){var a=i.refs;o===null?delete a[s]:a[s]=o},t._stringRef=s,t)}if(typeof e!="string")throw Error(P(284));if(!n._owner)throw Error(P(290,e))}return e}function bs(e,t){throw e=Object.prototype.toString.call(t),Error(P(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e))}function Ud(e){var t=e._init;return t(e._payload)}function i0(e){function t(y,p){if(e){var m=y.deletions;m===null?(y.deletions=[p],y.flags|=16):m.push(p)}}function n(y,p){if(!e)return null;for(;p!==null;)t(y,p),p=p.sibling;return null}function r(y,p){for(y=new Map;p!==null;)p.key!==null?y.set(p.key,p):y.set(p.index,p),p=p.sibling;return y}function i(y,p){return y=dn(y,p),y.index=0,y.sibling=null,y}function s(y,p,m){return y.index=m,e?(m=y.alternate,m!==null?(m=m.index,m<p?(y.flags|=2,p):m):(y.flags|=2,p)):(y.flags|=1048576,p)}function o(y){return e&&y.alternate===null&&(y.flags|=2),y}function a(y,p,m,S){return p===null||p.tag!==6?(p=Wa(m,y.mode,S),p.return=y,p):(p=i(p,m),p.return=y,p)}function l(y,p,m,S){var k=m.type;return k===ur?c(y,p,m.props.children,S,m.key):p!==null&&(p.elementType===k||typeof k=="object"&&k!==null&&k.$$typeof===Gt&&Ud(k)===p.type)?(S=i(p,m.props),S.ref=ii(y,p,m),S.return=y,S):(S=qs(m.type,m.key,m.props,null,y.mode,S),S.ref=ii(y,p,m),S.return=y,S)}function u(y,p,m,S){return p===null||p.tag!==4||p.stateNode.containerInfo!==m.containerInfo||p.stateNode.implementation!==m.implementation?(p=Ka(m,y.mode,S),p.return=y,p):(p=i(p,m.children||[]),p.return=y,p)}function c(y,p,m,S,k){return p===null||p.tag!==7?(p=Wn(m,y.mode,S,k),p.return=y,p):(p=i(p,m),p.return=y,p)}function d(y,p,m){if(typeof p=="string"&&p!==""||typeof p=="number")return p=Wa(""+p,y.mode,m),p.return=y,p;if(typeof p=="object"&&p!==null){switch(p.$$typeof){case ms:return m=qs(p.type,p.key,p.props,null,y.mode,m),m.ref=ii(y,null,p),m.return=y,m;case lr:return p=Ka(p,y.mode,m),p.return=y,p;case Gt:var S=p._init;return d(y,S(p._payload),m)}if(di(p)||Zr(p))return p=Wn(p,y.mode,m,null),p.return=y,p;bs(y,p)}return null}function h(y,p,m,S){var k=p!==null?p.key:null;if(typeof m=="string"&&m!==""||typeof m=="number")return k!==null?null:a(y,p,""+m,S);if(typeof m=="object"&&m!==null){switch(m.$$typeof){case ms:return m.key===k?l(y,p,m,S):null;case lr:return m.key===k?u(y,p,m,S):null;case Gt:return k=m._init,h(y,p,k(m._payload),S)}if(di(m)||Zr(m))return k!==null?null:c(y,p,m,S,null);bs(y,m)}return null}function f(y,p,m,S,k){if(typeof S=="string"&&S!==""||typeof S=="number")return y=y.get(m)||null,a(p,y,""+S,k);if(typeof S=="object"&&S!==null){switch(S.$$typeof){case ms:return y=y.get(S.key===null?m:S.key)||null,l(p,y,S,k);case lr:return y=y.get(S.key===null?m:S.key)||null,u(p,y,S,k);case Gt:var C=S._init;return f(y,p,m,C(S._payload),k)}if(di(S)||Zr(S))return y=y.get(m)||null,c(p,y,S,k,null);bs(p,S)}return null}function g(y,p,m,S){for(var k=null,C=null,E=p,b=p=0,A=null;E!==null&&b<m.length;b++){E.index>b?(A=E,E=null):A=E.sibling;var L=h(y,E,m[b],S);if(L===null){E===null&&(E=A);break}e&&E&&L.alternate===null&&t(y,E),p=s(L,p,b),C===null?k=L:C.sibling=L,C=L,E=A}if(b===m.length)return n(y,E),q&&Ln(y,b),k;if(E===null){for(;b<m.length;b++)E=d(y,m[b],S),E!==null&&(p=s(E,p,b),C===null?k=E:C.sibling=E,C=E);return q&&Ln(y,b),k}for(E=r(y,E);b<m.length;b++)A=f(E,y,b,m[b],S),A!==null&&(e&&A.alternate!==null&&E.delete(A.key===null?b:A.key),p=s(A,p,b),C===null?k=A:C.sibling=A,C=A);return e&&E.forEach(function(ue){return t(y,ue)}),q&&Ln(y,b),k}function w(y,p,m,S){var k=Zr(m);if(typeof k!="function")throw Error(P(150));if(m=k.call(m),m==null)throw Error(P(151));for(var C=k=null,E=p,b=p=0,A=null,L=m.next();E!==null&&!L.done;b++,L=m.next()){E.index>b?(A=E,E=null):A=E.sibling;var ue=h(y,E,L.value,S);if(ue===null){E===null&&(E=A);break}e&&E&&ue.alternate===null&&t(y,E),p=s(ue,p,b),C===null?k=ue:C.sibling=ue,C=ue,E=A}if(L.done)return n(y,E),q&&Ln(y,b),k;if(E===null){for(;!L.done;b++,L=m.next())L=d(y,L.value,S),L!==null&&(p=s(L,p,b),C===null?k=L:C.sibling=L,C=L);return q&&Ln(y,b),k}for(E=r(y,E);!L.done;b++,L=m.next())L=f(E,y,b,L.value,S),L!==null&&(e&&L.alternate!==null&&E.delete(L.key===null?b:L.key),p=s(L,p,b),C===null?k=L:C.sibling=L,C=L);return e&&E.forEach(function(pe){return t(y,pe)}),q&&Ln(y,b),k}function x(y,p,m,S){if(typeof m=="object"&&m!==null&&m.type===ur&&m.key===null&&(m=m.props.children),typeof m=="object"&&m!==null){switch(m.$$typeof){case ms:e:{for(var k=m.key,C=p;C!==null;){if(C.key===k){if(k=m.type,k===ur){if(C.tag===7){n(y,C.sibling),p=i(C,m.props.children),p.return=y,y=p;break e}}else if(C.elementType===k||typeof k=="object"&&k!==null&&k.$$typeof===Gt&&Ud(k)===C.type){n(y,C.sibling),p=i(C,m.props),p.ref=ii(y,C,m),p.return=y,y=p;break e}n(y,C);break}else t(y,C);C=C.sibling}m.type===ur?(p=Wn(m.props.children,y.mode,S,m.key),p.return=y,y=p):(S=qs(m.type,m.key,m.props,null,y.mode,S),S.ref=ii(y,p,m),S.return=y,y=S)}return o(y);case lr:e:{for(C=m.key;p!==null;){if(p.key===C)if(p.tag===4&&p.stateNode.containerInfo===m.containerInfo&&p.stateNode.implementation===m.implementation){n(y,p.sibling),p=i(p,m.children||[]),p.return=y,y=p;break e}else{n(y,p);break}else t(y,p);p=p.sibling}p=Ka(m,y.mode,S),p.return=y,y=p}return o(y);case Gt:return C=m._init,x(y,p,C(m._payload),S)}if(di(m))return g(y,p,m,S);if(Zr(m))return w(y,p,m,S);bs(y,m)}return typeof m=="string"&&m!==""||typeof m=="number"?(m=""+m,p!==null&&p.tag===6?(n(y,p.sibling),p=i(p,m),p.return=y,y=p):(n(y,p),p=Wa(m,y.mode,S),p.return=y,y=p),o(y)):n(y,p)}return x}var Mr=i0(!0),s0=i0(!1),fo=_n(null),po=null,vr=null,nc=null;function rc(){nc=vr=po=null}function ic(e){var t=fo.current;K(fo),e._currentValue=t}function Ul(e,t,n){for(;e!==null;){var r=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,r!==null&&(r.childLanes|=t)):r!==null&&(r.childLanes&t)!==t&&(r.childLanes|=t),e===n)break;e=e.return}}function jr(e,t){po=e,nc=vr=null,e=e.dependencies,e!==null&&e.firstContext!==null&&(e.lanes&t&&(Ie=!0),e.firstContext=null)}function rt(e){var t=e._currentValue;if(nc!==e)if(e={context:e,memoizedValue:t,next:null},vr===null){if(po===null)throw Error(P(308));vr=e,po.dependencies={lanes:0,firstContext:e}}else vr=vr.next=e;return t}var Bn=null;function sc(e){Bn===null?Bn=[e]:Bn.push(e)}function o0(e,t,n,r){var i=t.interleaved;return i===null?(n.next=n,sc(t)):(n.next=i.next,i.next=n),t.interleaved=n,Dt(e,r)}function Dt(e,t){e.lanes|=t;var n=e.alternate;for(n!==null&&(n.lanes|=t),n=e,e=e.return;e!==null;)e.childLanes|=t,n=e.alternate,n!==null&&(n.childLanes|=t),n=e,e=e.return;return n.tag===3?n.stateNode:null}var qt=!1;function oc(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,interleaved:null,lanes:0},effects:null}}function a0(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,effects:e.effects})}function Lt(e,t){return{eventTime:e,lane:t,tag:0,payload:null,callback:null,next:null}}function ln(e,t,n){var r=e.updateQueue;if(r===null)return null;if(r=r.shared,z&2){var i=r.pending;return i===null?t.next=t:(t.next=i.next,i.next=t),r.pending=t,Dt(e,n)}return i=r.interleaved,i===null?(t.next=t,sc(r)):(t.next=i.next,i.next=t),r.interleaved=t,Dt(e,n)}function Us(e,t,n){if(t=t.updateQueue,t!==null&&(t=t.shared,(n&4194240)!==0)){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Wu(e,n)}}function Fd(e,t){var n=e.updateQueue,r=e.alternate;if(r!==null&&(r=r.updateQueue,n===r)){var i=null,s=null;if(n=n.firstBaseUpdate,n!==null){do{var o={eventTime:n.eventTime,lane:n.lane,tag:n.tag,payload:n.payload,callback:n.callback,next:null};s===null?i=s=o:s=s.next=o,n=n.next}while(n!==null);s===null?i=s=t:s=s.next=t}else i=s=t;n={baseState:r.baseState,firstBaseUpdate:i,lastBaseUpdate:s,shared:r.shared,effects:r.effects},e.updateQueue=n;return}e=n.lastBaseUpdate,e===null?n.firstBaseUpdate=t:e.next=t,n.lastBaseUpdate=t}function mo(e,t,n,r){var i=e.updateQueue;qt=!1;var s=i.firstBaseUpdate,o=i.lastBaseUpdate,a=i.shared.pending;if(a!==null){i.shared.pending=null;var l=a,u=l.next;l.next=null,o===null?s=u:o.next=u,o=l;var c=e.alternate;c!==null&&(c=c.updateQueue,a=c.lastBaseUpdate,a!==o&&(a===null?c.firstBaseUpdate=u:a.next=u,c.lastBaseUpdate=l))}if(s!==null){var d=i.baseState;o=0,c=u=l=null,a=s;do{var h=a.lane,f=a.eventTime;if((r&h)===h){c!==null&&(c=c.next={eventTime:f,lane:0,tag:a.tag,payload:a.payload,callback:a.callback,next:null});e:{var g=e,w=a;switch(h=t,f=n,w.tag){case 1:if(g=w.payload,typeof g=="function"){d=g.call(f,d,h);break e}d=g;break e;case 3:g.flags=g.flags&-65537|128;case 0:if(g=w.payload,h=typeof g=="function"?g.call(f,d,h):g,h==null)break e;d=Z({},d,h);break e;case 2:qt=!0}}a.callback!==null&&a.lane!==0&&(e.flags|=64,h=i.effects,h===null?i.effects=[a]:h.push(a))}else f={eventTime:f,lane:h,tag:a.tag,payload:a.payload,callback:a.callback,next:null},c===null?(u=c=f,l=d):c=c.next=f,o|=h;if(a=a.next,a===null){if(a=i.shared.pending,a===null)break;h=a,a=h.next,h.next=null,i.lastBaseUpdate=h,i.shared.pending=null}}while(1);if(c===null&&(l=d),i.baseState=l,i.firstBaseUpdate=u,i.lastBaseUpdate=c,t=i.shared.interleaved,t!==null){i=t;do o|=i.lane,i=i.next;while(i!==t)}else s===null&&(i.shared.lanes=0);Jn|=o,e.lanes=o,e.memoizedState=d}}function Hd(e,t,n){if(e=t.effects,t.effects=null,e!==null)for(t=0;t<e.length;t++){var r=e[t],i=r.callback;if(i!==null){if(r.callback=null,r=n,typeof i!="function")throw Error(P(191,i));i.call(r)}}}var os={},xt=_n(os),Wi=_n(os),Ki=_n(os);function Un(e){if(e===os)throw Error(P(174));return e}function ac(e,t){switch(F(Ki,t),F(Wi,e),F(xt,os),e=t.nodeType,e){case 9:case 11:t=(t=t.documentElement)?t.namespaceURI:Sl(null,"");break;default:e=e===8?t.parentNode:t,t=e.namespaceURI||null,e=e.tagName,t=Sl(t,e)}K(xt),F(xt,t)}function Dr(){K(xt),K(Wi),K(Ki)}function l0(e){Un(Ki.current);var t=Un(xt.current),n=Sl(t,e.type);t!==n&&(F(Wi,e),F(xt,n))}function lc(e){Wi.current===e&&(K(xt),K(Wi))}var J=_n(0);function go(e){for(var t=e;t!==null;){if(t.tag===13){var n=t.memoizedState;if(n!==null&&(n=n.dehydrated,n===null||n.data==="$?"||n.data==="$!"))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if(t.flags&128)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}var $a=[];function uc(){for(var e=0;e<$a.length;e++)$a[e]._workInProgressVersionPrimary=null;$a.length=0}var Fs=Bt.ReactCurrentDispatcher,za=Bt.ReactCurrentBatchConfig,qn=0,X=null,ae=null,he=null,vo=!1,Ci=!1,Gi=0,C1=0;function we(){throw Error(P(321))}function cc(e,t){if(t===null)return!1;for(var n=0;n<t.length&&n<e.length;n++)if(!ft(e[n],t[n]))return!1;return!0}function dc(e,t,n,r,i,s){if(qn=s,X=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,Fs.current=e===null||e.memoizedState===null?T1:j1,e=n(r,i),Ci){s=0;do{if(Ci=!1,Gi=0,25<=s)throw Error(P(301));s+=1,he=ae=null,t.updateQueue=null,Fs.current=A1,e=n(r,i)}while(Ci)}if(Fs.current=yo,t=ae!==null&&ae.next!==null,qn=0,he=ae=X=null,vo=!1,t)throw Error(P(300));return e}function hc(){var e=Gi!==0;return Gi=0,e}function gt(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return he===null?X.memoizedState=he=e:he=he.next=e,he}function it(){if(ae===null){var e=X.alternate;e=e!==null?e.memoizedState:null}else e=ae.next;var t=he===null?X.memoizedState:he.next;if(t!==null)he=t,ae=e;else{if(e===null)throw Error(P(310));ae=e,e={memoizedState:ae.memoizedState,baseState:ae.baseState,baseQueue:ae.baseQueue,queue:ae.queue,next:null},he===null?X.memoizedState=he=e:he=he.next=e}return he}function qi(e,t){return typeof t=="function"?t(e):t}function Ba(e){var t=it(),n=t.queue;if(n===null)throw Error(P(311));n.lastRenderedReducer=e;var r=ae,i=r.baseQueue,s=n.pending;if(s!==null){if(i!==null){var o=i.next;i.next=s.next,s.next=o}r.baseQueue=i=s,n.pending=null}if(i!==null){s=i.next,r=r.baseState;var a=o=null,l=null,u=s;do{var c=u.lane;if((qn&c)===c)l!==null&&(l=l.next={lane:0,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null}),r=u.hasEagerState?u.eagerState:e(r,u.action);else{var d={lane:c,action:u.action,hasEagerState:u.hasEagerState,eagerState:u.eagerState,next:null};l===null?(a=l=d,o=r):l=l.next=d,X.lanes|=c,Jn|=c}u=u.next}while(u!==null&&u!==s);l===null?o=r:l.next=a,ft(r,t.memoizedState)||(Ie=!0),t.memoizedState=r,t.baseState=o,t.baseQueue=l,n.lastRenderedState=r}if(e=n.interleaved,e!==null){i=e;do s=i.lane,X.lanes|=s,Jn|=s,i=i.next;while(i!==e)}else i===null&&(n.lanes=0);return[t.memoizedState,n.dispatch]}function Ua(e){var t=it(),n=t.queue;if(n===null)throw Error(P(311));n.lastRenderedReducer=e;var r=n.dispatch,i=n.pending,s=t.memoizedState;if(i!==null){n.pending=null;var o=i=i.next;do s=e(s,o.action),o=o.next;while(o!==i);ft(s,t.memoizedState)||(Ie=!0),t.memoizedState=s,t.baseQueue===null&&(t.baseState=s),n.lastRenderedState=s}return[s,r]}function u0(){}function c0(e,t){var n=X,r=it(),i=t(),s=!ft(r.memoizedState,i);if(s&&(r.memoizedState=i,Ie=!0),r=r.queue,fc(f0.bind(null,n,r,e),[e]),r.getSnapshot!==t||s||he!==null&&he.memoizedState.tag&1){if(n.flags|=2048,Ji(9,h0.bind(null,n,r,i,t),void 0,null),fe===null)throw Error(P(349));qn&30||d0(n,t,i)}return i}function d0(e,t,n){e.flags|=16384,e={getSnapshot:t,value:n},t=X.updateQueue,t===null?(t={lastEffect:null,stores:null},X.updateQueue=t,t.stores=[e]):(n=t.stores,n===null?t.stores=[e]:n.push(e))}function h0(e,t,n,r){t.value=n,t.getSnapshot=r,p0(t)&&m0(e)}function f0(e,t,n){return n(function(){p0(t)&&m0(e)})}function p0(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!ft(e,n)}catch{return!0}}function m0(e){var t=Dt(e,1);t!==null&&ht(t,e,1,-1)}function Wd(e){var t=gt();return typeof e=="function"&&(e=e()),t.memoizedState=t.baseState=e,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:qi,lastRenderedState:e},t.queue=e,e=e.dispatch=P1.bind(null,X,e),[t.memoizedState,e]}function Ji(e,t,n,r){return e={tag:e,create:t,destroy:n,deps:r,next:null},t=X.updateQueue,t===null?(t={lastEffect:null,stores:null},X.updateQueue=t,t.lastEffect=e.next=e):(n=t.lastEffect,n===null?t.lastEffect=e.next=e:(r=n.next,n.next=e,e.next=r,t.lastEffect=e)),e}function g0(){return it().memoizedState}function Hs(e,t,n,r){var i=gt();X.flags|=e,i.memoizedState=Ji(1|t,n,void 0,r===void 0?null:r)}function Wo(e,t,n,r){var i=it();r=r===void 0?null:r;var s=void 0;if(ae!==null){var o=ae.memoizedState;if(s=o.destroy,r!==null&&cc(r,o.deps)){i.memoizedState=Ji(t,n,s,r);return}}X.flags|=e,i.memoizedState=Ji(1|t,n,s,r)}function Kd(e,t){return Hs(8390656,8,e,t)}function fc(e,t){return Wo(2048,8,e,t)}function v0(e,t){return Wo(4,2,e,t)}function y0(e,t){return Wo(4,4,e,t)}function w0(e,t){if(typeof t=="function")return e=e(),t(e),function(){t(null)};if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function x0(e,t,n){return n=n!=null?n.concat([e]):null,Wo(4,4,w0.bind(null,t,e),n)}function pc(){}function _0(e,t){var n=it();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&cc(t,r[1])?r[0]:(n.memoizedState=[e,t],e)}function S0(e,t){var n=it();t=t===void 0?null:t;var r=n.memoizedState;return r!==null&&t!==null&&cc(t,r[1])?r[0]:(e=e(),n.memoizedState=[e,t],e)}function k0(e,t,n){return qn&21?(ft(n,t)||(n=Tp(),X.lanes|=n,Jn|=n,e.baseState=!0),t):(e.baseState&&(e.baseState=!1,Ie=!0),e.memoizedState=n)}function E1(e,t){var n=B;B=n!==0&&4>n?n:4,e(!0);var r=za.transition;za.transition={};try{e(!1),t()}finally{B=n,za.transition=r}}function C0(){return it().memoizedState}function b1(e,t,n){var r=cn(e);if(n={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null},E0(e))b0(t,n);else if(n=o0(e,t,n,r),n!==null){var i=Pe();ht(n,e,r,i),P0(n,t,r)}}function P1(e,t,n){var r=cn(e),i={lane:r,action:n,hasEagerState:!1,eagerState:null,next:null};if(E0(e))b0(t,i);else{var s=e.alternate;if(e.lanes===0&&(s===null||s.lanes===0)&&(s=t.lastRenderedReducer,s!==null))try{var o=t.lastRenderedState,a=s(o,n);if(i.hasEagerState=!0,i.eagerState=a,ft(a,o)){var l=t.interleaved;l===null?(i.next=i,sc(t)):(i.next=l.next,l.next=i),t.interleaved=i;return}}catch{}finally{}n=o0(e,t,i,r),n!==null&&(i=Pe(),ht(n,e,r,i),P0(n,t,r))}}function E0(e){var t=e.alternate;return e===X||t!==null&&t===X}function b0(e,t){Ci=vo=!0;var n=e.pending;n===null?t.next=t:(t.next=n.next,n.next=t),e.pending=t}function P0(e,t,n){if(n&4194240){var r=t.lanes;r&=e.pendingLanes,n|=r,t.lanes=n,Wu(e,n)}}var yo={readContext:rt,useCallback:we,useContext:we,useEffect:we,useImperativeHandle:we,useInsertionEffect:we,useLayoutEffect:we,useMemo:we,useReducer:we,useRef:we,useState:we,useDebugValue:we,useDeferredValue:we,useTransition:we,useMutableSource:we,useSyncExternalStore:we,useId:we,unstable_isNewReconciler:!1},T1={readContext:rt,useCallback:function(e,t){return gt().memoizedState=[e,t===void 0?null:t],e},useContext:rt,useEffect:Kd,useImperativeHandle:function(e,t,n){return n=n!=null?n.concat([e]):null,Hs(4194308,4,w0.bind(null,t,e),n)},useLayoutEffect:function(e,t){return Hs(4194308,4,e,t)},useInsertionEffect:function(e,t){return Hs(4,2,e,t)},useMemo:function(e,t){var n=gt();return t=t===void 0?null:t,e=e(),n.memoizedState=[e,t],e},useReducer:function(e,t,n){var r=gt();return t=n!==void 0?n(t):t,r.memoizedState=r.baseState=t,e={pending:null,interleaved:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:t},r.queue=e,e=e.dispatch=b1.bind(null,X,e),[r.memoizedState,e]},useRef:function(e){var t=gt();return e={current:e},t.memoizedState=e},useState:Wd,useDebugValue:pc,useDeferredValue:function(e){return gt().memoizedState=e},useTransition:function(){var e=Wd(!1),t=e[0];return e=E1.bind(null,e[1]),gt().memoizedState=e,[t,e]},useMutableSource:function(){},useSyncExternalStore:function(e,t,n){var r=X,i=gt();if(q){if(n===void 0)throw Error(P(407));n=n()}else{if(n=t(),fe===null)throw Error(P(349));qn&30||d0(r,t,n)}i.memoizedState=n;var s={value:n,getSnapshot:t};return i.queue=s,Kd(f0.bind(null,r,s,e),[e]),r.flags|=2048,Ji(9,h0.bind(null,r,s,n,t),void 0,null),n},useId:function(){var e=gt(),t=fe.identifierPrefix;if(q){var n=At,r=jt;n=(r&~(1<<32-dt(r)-1)).toString(32)+n,t=":"+t+"R"+n,n=Gi++,0<n&&(t+="H"+n.toString(32)),t+=":"}else n=C1++,t=":"+t+"r"+n.toString(32)+":";return e.memoizedState=t},unstable_isNewReconciler:!1},j1={readContext:rt,useCallback:_0,useContext:rt,useEffect:fc,useImperativeHandle:x0,useInsertionEffect:v0,useLayoutEffect:y0,useMemo:S0,useReducer:Ba,useRef:g0,useState:function(){return Ba(qi)},useDebugValue:pc,useDeferredValue:function(e){var t=it();return k0(t,ae.memoizedState,e)},useTransition:function(){var e=Ba(qi)[0],t=it().memoizedState;return[e,t]},useMutableSource:u0,useSyncExternalStore:c0,useId:C0,unstable_isNewReconciler:!1},A1={readContext:rt,useCallback:_0,useContext:rt,useEffect:fc,useImperativeHandle:x0,useInsertionEffect:v0,useLayoutEffect:y0,useMemo:S0,useReducer:Ua,useRef:g0,useState:function(){return Ua(qi)},useDebugValue:pc,useDeferredValue:function(e){var t=it();return ae===null?t.memoizedState=e:k0(t,ae.memoizedState,e)},useTransition:function(){var e=Ua(qi)[0],t=it().memoizedState;return[e,t]},useMutableSource:u0,useSyncExternalStore:c0,useId:C0,unstable_isNewReconciler:!1};function at(e,t){if(e&&e.defaultProps){t=Z({},t),e=e.defaultProps;for(var n in e)t[n]===void 0&&(t[n]=e[n]);return t}return t}function Fl(e,t,n,r){t=e.memoizedState,n=n(r,t),n=n==null?t:Z({},t,n),e.memoizedState=n,e.lanes===0&&(e.updateQueue.baseState=n)}var Ko={isMounted:function(e){return(e=e._reactInternals)?Xn(e)===e:!1},enqueueSetState:function(e,t,n){e=e._reactInternals;var r=Pe(),i=cn(e),s=Lt(r,i);s.payload=t,n!=null&&(s.callback=n),t=ln(e,s,i),t!==null&&(ht(t,e,i,r),Us(t,e,i))},enqueueReplaceState:function(e,t,n){e=e._reactInternals;var r=Pe(),i=cn(e),s=Lt(r,i);s.tag=1,s.payload=t,n!=null&&(s.callback=n),t=ln(e,s,i),t!==null&&(ht(t,e,i,r),Us(t,e,i))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var n=Pe(),r=cn(e),i=Lt(n,r);i.tag=2,t!=null&&(i.callback=t),t=ln(e,i,r),t!==null&&(ht(t,e,r,n),Us(t,e,r))}};function Gd(e,t,n,r,i,s,o){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(r,s,o):t.prototype&&t.prototype.isPureReactComponent?!Bi(n,r)||!Bi(i,s):!0}function T0(e,t,n){var r=!1,i=vn,s=t.contextType;return typeof s=="object"&&s!==null?s=rt(s):(i=Me(t)?Kn:ke.current,r=t.contextTypes,s=(r=r!=null)?Ir(e,i):vn),t=new t(n,s),e.memoizedState=t.state!==null&&t.state!==void 0?t.state:null,t.updater=Ko,e.stateNode=t,t._reactInternals=e,r&&(e=e.stateNode,e.__reactInternalMemoizedUnmaskedChildContext=i,e.__reactInternalMemoizedMaskedChildContext=s),t}function qd(e,t,n,r){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(n,r),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(n,r),t.state!==e&&Ko.enqueueReplaceState(t,t.state,null)}function Hl(e,t,n,r){var i=e.stateNode;i.props=n,i.state=e.memoizedState,i.refs={},oc(e);var s=t.contextType;typeof s=="object"&&s!==null?i.context=rt(s):(s=Me(t)?Kn:ke.current,i.context=Ir(e,s)),i.state=e.memoizedState,s=t.getDerivedStateFromProps,typeof s=="function"&&(Fl(e,t,s,n),i.state=e.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof i.getSnapshotBeforeUpdate=="function"||typeof i.UNSAFE_componentWillMount!="function"&&typeof i.componentWillMount!="function"||(t=i.state,typeof i.componentWillMount=="function"&&i.componentWillMount(),typeof i.UNSAFE_componentWillMount=="function"&&i.UNSAFE_componentWillMount(),t!==i.state&&Ko.enqueueReplaceState(i,i.state,null),mo(e,n,i,r),i.state=e.memoizedState),typeof i.componentDidMount=="function"&&(e.flags|=4194308)}function Vr(e,t){try{var n="",r=t;do n+=iy(r),r=r.return;while(r);var i=n}catch(s){i=`
Error generating stack: `+s.message+`
`+s.stack}return{value:e,source:t,stack:i,digest:null}}function Fa(e,t,n){return{value:e,source:null,stack:n??null,digest:t??null}}function Wl(e,t){try{console.error(t.value)}catch(n){setTimeout(function(){throw n})}}var R1=typeof WeakMap=="function"?WeakMap:Map;function j0(e,t,n){n=Lt(-1,n),n.tag=3,n.payload={element:null};var r=t.value;return n.callback=function(){xo||(xo=!0,tu=r),Wl(e,t)},n}function A0(e,t,n){n=Lt(-1,n),n.tag=3;var r=e.type.getDerivedStateFromError;if(typeof r=="function"){var i=t.value;n.payload=function(){return r(i)},n.callback=function(){Wl(e,t)}}var s=e.stateNode;return s!==null&&typeof s.componentDidCatch=="function"&&(n.callback=function(){Wl(e,t),typeof r!="function"&&(un===null?un=new Set([this]):un.add(this));var o=t.stack;this.componentDidCatch(t.value,{componentStack:o!==null?o:""})}),n}function Jd(e,t,n){var r=e.pingCache;if(r===null){r=e.pingCache=new R1;var i=new Set;r.set(t,i)}else i=r.get(t),i===void 0&&(i=new Set,r.set(t,i));i.has(n)||(i.add(n),e=W1.bind(null,e,t,n),t.then(e,e))}function Qd(e){do{var t;if((t=e.tag===13)&&(t=e.memoizedState,t=t!==null?t.dehydrated!==null:!0),t)return e;e=e.return}while(e!==null);return null}function Yd(e,t,n,r,i){return e.mode&1?(e.flags|=65536,e.lanes=i,e):(e===t?e.flags|=65536:(e.flags|=128,n.flags|=131072,n.flags&=-52805,n.tag===1&&(n.alternate===null?n.tag=17:(t=Lt(-1,1),t.tag=2,ln(n,t,1))),n.lanes|=1),e)}var L1=Bt.ReactCurrentOwner,Ie=!1;function be(e,t,n,r){t.child=e===null?s0(t,null,n,r):Mr(t,e.child,n,r)}function Xd(e,t,n,r,i){n=n.render;var s=t.ref;return jr(t,i),r=dc(e,t,n,r,s,i),n=hc(),e!==null&&!Ie?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,Vt(e,t,i)):(q&&n&&Zu(t),t.flags|=1,be(e,t,r,i),t.child)}function Zd(e,t,n,r,i){if(e===null){var s=n.type;return typeof s=="function"&&!Sc(s)&&s.defaultProps===void 0&&n.compare===null&&n.defaultProps===void 0?(t.tag=15,t.type=s,R0(e,t,s,r,i)):(e=qs(n.type,null,r,t,t.mode,i),e.ref=t.ref,e.return=t,t.child=e)}if(s=e.child,!(e.lanes&i)){var o=s.memoizedProps;if(n=n.compare,n=n!==null?n:Bi,n(o,r)&&e.ref===t.ref)return Vt(e,t,i)}return t.flags|=1,e=dn(s,r),e.ref=t.ref,e.return=t,t.child=e}function R0(e,t,n,r,i){if(e!==null){var s=e.memoizedProps;if(Bi(s,r)&&e.ref===t.ref)if(Ie=!1,t.pendingProps=r=s,(e.lanes&i)!==0)e.flags&131072&&(Ie=!0);else return t.lanes=e.lanes,Vt(e,t,i)}return Kl(e,t,n,r,i)}function L0(e,t,n){var r=t.pendingProps,i=r.children,s=e!==null?e.memoizedState:null;if(r.mode==="hidden")if(!(t.mode&1))t.memoizedState={baseLanes:0,cachePool:null,transitions:null},F(wr,Be),Be|=n;else{if(!(n&1073741824))return e=s!==null?s.baseLanes|n:n,t.lanes=t.childLanes=1073741824,t.memoizedState={baseLanes:e,cachePool:null,transitions:null},t.updateQueue=null,F(wr,Be),Be|=e,null;t.memoizedState={baseLanes:0,cachePool:null,transitions:null},r=s!==null?s.baseLanes:n,F(wr,Be),Be|=r}else s!==null?(r=s.baseLanes|n,t.memoizedState=null):r=n,F(wr,Be),Be|=r;return be(e,t,i,n),t.child}function O0(e,t){var n=t.ref;(e===null&&n!==null||e!==null&&e.ref!==n)&&(t.flags|=512,t.flags|=2097152)}function Kl(e,t,n,r,i){var s=Me(n)?Kn:ke.current;return s=Ir(t,s),jr(t,i),n=dc(e,t,n,r,s,i),r=hc(),e!==null&&!Ie?(t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~i,Vt(e,t,i)):(q&&r&&Zu(t),t.flags|=1,be(e,t,n,i),t.child)}function eh(e,t,n,r,i){if(Me(n)){var s=!0;uo(t)}else s=!1;if(jr(t,i),t.stateNode===null)Ws(e,t),T0(t,n,r),Hl(t,n,r,i),r=!0;else if(e===null){var o=t.stateNode,a=t.memoizedProps;o.props=a;var l=o.context,u=n.contextType;typeof u=="object"&&u!==null?u=rt(u):(u=Me(n)?Kn:ke.current,u=Ir(t,u));var c=n.getDerivedStateFromProps,d=typeof c=="function"||typeof o.getSnapshotBeforeUpdate=="function";d||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(a!==r||l!==u)&&qd(t,o,r,u),qt=!1;var h=t.memoizedState;o.state=h,mo(t,r,o,i),l=t.memoizedState,a!==r||h!==l||Ne.current||qt?(typeof c=="function"&&(Fl(t,n,c,r),l=t.memoizedState),(a=qt||Gd(t,n,a,r,h,l,u))?(d||typeof o.UNSAFE_componentWillMount!="function"&&typeof o.componentWillMount!="function"||(typeof o.componentWillMount=="function"&&o.componentWillMount(),typeof o.UNSAFE_componentWillMount=="function"&&o.UNSAFE_componentWillMount()),typeof o.componentDidMount=="function"&&(t.flags|=4194308)):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=r,t.memoizedState=l),o.props=r,o.state=l,o.context=u,r=a):(typeof o.componentDidMount=="function"&&(t.flags|=4194308),r=!1)}else{o=t.stateNode,a0(e,t),a=t.memoizedProps,u=t.type===t.elementType?a:at(t.type,a),o.props=u,d=t.pendingProps,h=o.context,l=n.contextType,typeof l=="object"&&l!==null?l=rt(l):(l=Me(n)?Kn:ke.current,l=Ir(t,l));var f=n.getDerivedStateFromProps;(c=typeof f=="function"||typeof o.getSnapshotBeforeUpdate=="function")||typeof o.UNSAFE_componentWillReceiveProps!="function"&&typeof o.componentWillReceiveProps!="function"||(a!==d||h!==l)&&qd(t,o,r,l),qt=!1,h=t.memoizedState,o.state=h,mo(t,r,o,i);var g=t.memoizedState;a!==d||h!==g||Ne.current||qt?(typeof f=="function"&&(Fl(t,n,f,r),g=t.memoizedState),(u=qt||Gd(t,n,u,r,h,g,l)||!1)?(c||typeof o.UNSAFE_componentWillUpdate!="function"&&typeof o.componentWillUpdate!="function"||(typeof o.componentWillUpdate=="function"&&o.componentWillUpdate(r,g,l),typeof o.UNSAFE_componentWillUpdate=="function"&&o.UNSAFE_componentWillUpdate(r,g,l)),typeof o.componentDidUpdate=="function"&&(t.flags|=4),typeof o.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof o.componentDidUpdate!="function"||a===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),t.memoizedProps=r,t.memoizedState=g),o.props=r,o.state=g,o.context=l,r=u):(typeof o.componentDidUpdate!="function"||a===e.memoizedProps&&h===e.memoizedState||(t.flags|=4),typeof o.getSnapshotBeforeUpdate!="function"||a===e.memoizedProps&&h===e.memoizedState||(t.flags|=1024),r=!1)}return Gl(e,t,n,r,s,i)}function Gl(e,t,n,r,i,s){O0(e,t);var o=(t.flags&128)!==0;if(!r&&!o)return i&&$d(t,n,!1),Vt(e,t,s);r=t.stateNode,L1.current=t;var a=o&&typeof n.getDerivedStateFromError!="function"?null:r.render();return t.flags|=1,e!==null&&o?(t.child=Mr(t,e.child,null,s),t.child=Mr(t,null,a,s)):be(e,t,a,s),t.memoizedState=r.state,i&&$d(t,n,!0),t.child}function I0(e){var t=e.stateNode;t.pendingContext?Vd(e,t.pendingContext,t.pendingContext!==t.context):t.context&&Vd(e,t.context,!1),ac(e,t.containerInfo)}function th(e,t,n,r,i){return Nr(),tc(i),t.flags|=256,be(e,t,n,r),t.child}var ql={dehydrated:null,treeContext:null,retryLane:0};function Jl(e){return{baseLanes:e,cachePool:null,transitions:null}}function N0(e,t,n){var r=t.pendingProps,i=J.current,s=!1,o=(t.flags&128)!==0,a;if((a=o)||(a=e!==null&&e.memoizedState===null?!1:(i&2)!==0),a?(s=!0,t.flags&=-129):(e===null||e.memoizedState!==null)&&(i|=1),F(J,i&1),e===null)return Bl(t),e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null)?(t.mode&1?e.data==="$!"?t.lanes=8:t.lanes=1073741824:t.lanes=1,null):(o=r.children,e=r.fallback,s?(r=t.mode,s=t.child,o={mode:"hidden",children:o},!(r&1)&&s!==null?(s.childLanes=0,s.pendingProps=o):s=Jo(o,r,0,null),e=Wn(e,r,n,null),s.return=t,e.return=t,s.sibling=e,t.child=s,t.child.memoizedState=Jl(n),t.memoizedState=ql,e):mc(t,o));if(i=e.memoizedState,i!==null&&(a=i.dehydrated,a!==null))return O1(e,t,o,r,a,i,n);if(s){s=r.fallback,o=t.mode,i=e.child,a=i.sibling;var l={mode:"hidden",children:r.children};return!(o&1)&&t.child!==i?(r=t.child,r.childLanes=0,r.pendingProps=l,t.deletions=null):(r=dn(i,l),r.subtreeFlags=i.subtreeFlags&14680064),a!==null?s=dn(a,s):(s=Wn(s,o,n,null),s.flags|=2),s.return=t,r.return=t,r.sibling=s,t.child=r,r=s,s=t.child,o=e.child.memoizedState,o=o===null?Jl(n):{baseLanes:o.baseLanes|n,cachePool:null,transitions:o.transitions},s.memoizedState=o,s.childLanes=e.childLanes&~n,t.memoizedState=ql,r}return s=e.child,e=s.sibling,r=dn(s,{mode:"visible",children:r.children}),!(t.mode&1)&&(r.lanes=n),r.return=t,r.sibling=null,e!==null&&(n=t.deletions,n===null?(t.deletions=[e],t.flags|=16):n.push(e)),t.child=r,t.memoizedState=null,r}function mc(e,t){return t=Jo({mode:"visible",children:t},e.mode,0,null),t.return=e,e.child=t}function Ps(e,t,n,r){return r!==null&&tc(r),Mr(t,e.child,null,n),e=mc(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function O1(e,t,n,r,i,s,o){if(n)return t.flags&256?(t.flags&=-257,r=Fa(Error(P(422))),Ps(e,t,o,r)):t.memoizedState!==null?(t.child=e.child,t.flags|=128,null):(s=r.fallback,i=t.mode,r=Jo({mode:"visible",children:r.children},i,0,null),s=Wn(s,i,o,null),s.flags|=2,r.return=t,s.return=t,r.sibling=s,t.child=r,t.mode&1&&Mr(t,e.child,null,o),t.child.memoizedState=Jl(o),t.memoizedState=ql,s);if(!(t.mode&1))return Ps(e,t,o,null);if(i.data==="$!"){if(r=i.nextSibling&&i.nextSibling.dataset,r)var a=r.dgst;return r=a,s=Error(P(419)),r=Fa(s,r,void 0),Ps(e,t,o,r)}if(a=(o&e.childLanes)!==0,Ie||a){if(r=fe,r!==null){switch(o&-o){case 4:i=2;break;case 16:i=8;break;case 64:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:case 67108864:i=32;break;case 536870912:i=268435456;break;default:i=0}i=i&(r.suspendedLanes|o)?0:i,i!==0&&i!==s.retryLane&&(s.retryLane=i,Dt(e,i),ht(r,e,i,-1))}return _c(),r=Fa(Error(P(421))),Ps(e,t,o,r)}return i.data==="$?"?(t.flags|=128,t.child=e.child,t=K1.bind(null,e),i._reactRetry=t,null):(e=s.treeContext,Ue=an(i.nextSibling),He=t,q=!0,ct=null,e!==null&&(Xe[Ze++]=jt,Xe[Ze++]=At,Xe[Ze++]=Gn,jt=e.id,At=e.overflow,Gn=t),t=mc(t,r.children),t.flags|=4096,t)}function nh(e,t,n){e.lanes|=t;var r=e.alternate;r!==null&&(r.lanes|=t),Ul(e.return,t,n)}function Ha(e,t,n,r,i){var s=e.memoizedState;s===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:r,tail:n,tailMode:i}:(s.isBackwards=t,s.rendering=null,s.renderingStartTime=0,s.last=r,s.tail=n,s.tailMode=i)}function M0(e,t,n){var r=t.pendingProps,i=r.revealOrder,s=r.tail;if(be(e,t,r.children,n),r=J.current,r&2)r=r&1|2,t.flags|=128;else{if(e!==null&&e.flags&128)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&nh(e,n,t);else if(e.tag===19)nh(e,n,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}r&=1}if(F(J,r),!(t.mode&1))t.memoizedState=null;else switch(i){case"forwards":for(n=t.child,i=null;n!==null;)e=n.alternate,e!==null&&go(e)===null&&(i=n),n=n.sibling;n=i,n===null?(i=t.child,t.child=null):(i=n.sibling,n.sibling=null),Ha(t,!1,i,n,s);break;case"backwards":for(n=null,i=t.child,t.child=null;i!==null;){if(e=i.alternate,e!==null&&go(e)===null){t.child=i;break}e=i.sibling,i.sibling=n,n=i,i=e}Ha(t,!0,n,null,s);break;case"together":Ha(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Ws(e,t){!(t.mode&1)&&e!==null&&(e.alternate=null,t.alternate=null,t.flags|=2)}function Vt(e,t,n){if(e!==null&&(t.dependencies=e.dependencies),Jn|=t.lanes,!(n&t.childLanes))return null;if(e!==null&&t.child!==e.child)throw Error(P(153));if(t.child!==null){for(e=t.child,n=dn(e,e.pendingProps),t.child=n,n.return=t;e.sibling!==null;)e=e.sibling,n=n.sibling=dn(e,e.pendingProps),n.return=t;n.sibling=null}return t.child}function I1(e,t,n){switch(t.tag){case 3:I0(t),Nr();break;case 5:l0(t);break;case 1:Me(t.type)&&uo(t);break;case 4:ac(t,t.stateNode.containerInfo);break;case 10:var r=t.type._context,i=t.memoizedProps.value;F(fo,r._currentValue),r._currentValue=i;break;case 13:if(r=t.memoizedState,r!==null)return r.dehydrated!==null?(F(J,J.current&1),t.flags|=128,null):n&t.child.childLanes?N0(e,t,n):(F(J,J.current&1),e=Vt(e,t,n),e!==null?e.sibling:null);F(J,J.current&1);break;case 19:if(r=(n&t.childLanes)!==0,e.flags&128){if(r)return M0(e,t,n);t.flags|=128}if(i=t.memoizedState,i!==null&&(i.rendering=null,i.tail=null,i.lastEffect=null),F(J,J.current),r)break;return null;case 22:case 23:return t.lanes=0,L0(e,t,n)}return Vt(e,t,n)}var D0,Ql,V0,$0;D0=function(e,t){for(var n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break;for(;n.sibling===null;){if(n.return===null||n.return===t)return;n=n.return}n.sibling.return=n.return,n=n.sibling}};Ql=function(){};V0=function(e,t,n,r){var i=e.memoizedProps;if(i!==r){e=t.stateNode,Un(xt.current);var s=null;switch(n){case"input":i=yl(e,i),r=yl(e,r),s=[];break;case"select":i=Z({},i,{value:void 0}),r=Z({},r,{value:void 0}),s=[];break;case"textarea":i=_l(e,i),r=_l(e,r),s=[];break;default:typeof i.onClick!="function"&&typeof r.onClick=="function"&&(e.onclick=ao)}kl(n,r);var o;n=null;for(u in i)if(!r.hasOwnProperty(u)&&i.hasOwnProperty(u)&&i[u]!=null)if(u==="style"){var a=i[u];for(o in a)a.hasOwnProperty(o)&&(n||(n={}),n[o]="")}else u!=="dangerouslySetInnerHTML"&&u!=="children"&&u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&u!=="autoFocus"&&(Ii.hasOwnProperty(u)?s||(s=[]):(s=s||[]).push(u,null));for(u in r){var l=r[u];if(a=i!=null?i[u]:void 0,r.hasOwnProperty(u)&&l!==a&&(l!=null||a!=null))if(u==="style")if(a){for(o in a)!a.hasOwnProperty(o)||l&&l.hasOwnProperty(o)||(n||(n={}),n[o]="");for(o in l)l.hasOwnProperty(o)&&a[o]!==l[o]&&(n||(n={}),n[o]=l[o])}else n||(s||(s=[]),s.push(u,n)),n=l;else u==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,a=a?a.__html:void 0,l!=null&&a!==l&&(s=s||[]).push(u,l)):u==="children"?typeof l!="string"&&typeof l!="number"||(s=s||[]).push(u,""+l):u!=="suppressContentEditableWarning"&&u!=="suppressHydrationWarning"&&(Ii.hasOwnProperty(u)?(l!=null&&u==="onScroll"&&W("scroll",e),s||a===l||(s=[])):(s=s||[]).push(u,l))}n&&(s=s||[]).push("style",n);var u=s;(t.updateQueue=u)&&(t.flags|=4)}};$0=function(e,t,n,r){n!==r&&(t.flags|=4)};function si(e,t){if(!q)switch(e.tailMode){case"hidden":t=e.tail;for(var n=null;t!==null;)t.alternate!==null&&(n=t),t=t.sibling;n===null?e.tail=null:n.sibling=null;break;case"collapsed":n=e.tail;for(var r=null;n!==null;)n.alternate!==null&&(r=n),n=n.sibling;r===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:r.sibling=null}}function xe(e){var t=e.alternate!==null&&e.alternate.child===e.child,n=0,r=0;if(t)for(var i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags&14680064,r|=i.flags&14680064,i.return=e,i=i.sibling;else for(i=e.child;i!==null;)n|=i.lanes|i.childLanes,r|=i.subtreeFlags,r|=i.flags,i.return=e,i=i.sibling;return e.subtreeFlags|=r,e.childLanes=n,t}function N1(e,t,n){var r=t.pendingProps;switch(ec(t),t.tag){case 2:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return xe(t),null;case 1:return Me(t.type)&&lo(),xe(t),null;case 3:return r=t.stateNode,Dr(),K(Ne),K(ke),uc(),r.pendingContext&&(r.context=r.pendingContext,r.pendingContext=null),(e===null||e.child===null)&&(Es(t)?t.flags|=4:e===null||e.memoizedState.isDehydrated&&!(t.flags&256)||(t.flags|=1024,ct!==null&&(iu(ct),ct=null))),Ql(e,t),xe(t),null;case 5:lc(t);var i=Un(Ki.current);if(n=t.type,e!==null&&t.stateNode!=null)V0(e,t,n,r,i),e.ref!==t.ref&&(t.flags|=512,t.flags|=2097152);else{if(!r){if(t.stateNode===null)throw Error(P(166));return xe(t),null}if(e=Un(xt.current),Es(t)){r=t.stateNode,n=t.type;var s=t.memoizedProps;switch(r[yt]=t,r[Hi]=s,e=(t.mode&1)!==0,n){case"dialog":W("cancel",r),W("close",r);break;case"iframe":case"object":case"embed":W("load",r);break;case"video":case"audio":for(i=0;i<fi.length;i++)W(fi[i],r);break;case"source":W("error",r);break;case"img":case"image":case"link":W("error",r),W("load",r);break;case"details":W("toggle",r);break;case"input":dd(r,s),W("invalid",r);break;case"select":r._wrapperState={wasMultiple:!!s.multiple},W("invalid",r);break;case"textarea":fd(r,s),W("invalid",r)}kl(n,s),i=null;for(var o in s)if(s.hasOwnProperty(o)){var a=s[o];o==="children"?typeof a=="string"?r.textContent!==a&&(s.suppressHydrationWarning!==!0&&Cs(r.textContent,a,e),i=["children",a]):typeof a=="number"&&r.textContent!==""+a&&(s.suppressHydrationWarning!==!0&&Cs(r.textContent,a,e),i=["children",""+a]):Ii.hasOwnProperty(o)&&a!=null&&o==="onScroll"&&W("scroll",r)}switch(n){case"input":gs(r),hd(r,s,!0);break;case"textarea":gs(r),pd(r);break;case"select":case"option":break;default:typeof s.onClick=="function"&&(r.onclick=ao)}r=i,t.updateQueue=r,r!==null&&(t.flags|=4)}else{o=i.nodeType===9?i:i.ownerDocument,e==="http://www.w3.org/1999/xhtml"&&(e=fp(n)),e==="http://www.w3.org/1999/xhtml"?n==="script"?(e=o.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild)):typeof r.is=="string"?e=o.createElement(n,{is:r.is}):(e=o.createElement(n),n==="select"&&(o=e,r.multiple?o.multiple=!0:r.size&&(o.size=r.size))):e=o.createElementNS(e,n),e[yt]=t,e[Hi]=r,D0(e,t,!1,!1),t.stateNode=e;e:{switch(o=Cl(n,r),n){case"dialog":W("cancel",e),W("close",e),i=r;break;case"iframe":case"object":case"embed":W("load",e),i=r;break;case"video":case"audio":for(i=0;i<fi.length;i++)W(fi[i],e);i=r;break;case"source":W("error",e),i=r;break;case"img":case"image":case"link":W("error",e),W("load",e),i=r;break;case"details":W("toggle",e),i=r;break;case"input":dd(e,r),i=yl(e,r),W("invalid",e);break;case"option":i=r;break;case"select":e._wrapperState={wasMultiple:!!r.multiple},i=Z({},r,{value:void 0}),W("invalid",e);break;case"textarea":fd(e,r),i=_l(e,r),W("invalid",e);break;default:i=r}kl(n,i),a=i;for(s in a)if(a.hasOwnProperty(s)){var l=a[s];s==="style"?gp(e,l):s==="dangerouslySetInnerHTML"?(l=l?l.__html:void 0,l!=null&&pp(e,l)):s==="children"?typeof l=="string"?(n!=="textarea"||l!=="")&&Ni(e,l):typeof l=="number"&&Ni(e,""+l):s!=="suppressContentEditableWarning"&&s!=="suppressHydrationWarning"&&s!=="autoFocus"&&(Ii.hasOwnProperty(s)?l!=null&&s==="onScroll"&&W("scroll",e):l!=null&&$u(e,s,l,o))}switch(n){case"input":gs(e),hd(e,r,!1);break;case"textarea":gs(e),pd(e);break;case"option":r.value!=null&&e.setAttribute("value",""+gn(r.value));break;case"select":e.multiple=!!r.multiple,s=r.value,s!=null?Er(e,!!r.multiple,s,!1):r.defaultValue!=null&&Er(e,!!r.multiple,r.defaultValue,!0);break;default:typeof i.onClick=="function"&&(e.onclick=ao)}switch(n){case"button":case"input":case"select":case"textarea":r=!!r.autoFocus;break e;case"img":r=!0;break e;default:r=!1}}r&&(t.flags|=4)}t.ref!==null&&(t.flags|=512,t.flags|=2097152)}return xe(t),null;case 6:if(e&&t.stateNode!=null)$0(e,t,e.memoizedProps,r);else{if(typeof r!="string"&&t.stateNode===null)throw Error(P(166));if(n=Un(Ki.current),Un(xt.current),Es(t)){if(r=t.stateNode,n=t.memoizedProps,r[yt]=t,(s=r.nodeValue!==n)&&(e=He,e!==null))switch(e.tag){case 3:Cs(r.nodeValue,n,(e.mode&1)!==0);break;case 5:e.memoizedProps.suppressHydrationWarning!==!0&&Cs(r.nodeValue,n,(e.mode&1)!==0)}s&&(t.flags|=4)}else r=(n.nodeType===9?n:n.ownerDocument).createTextNode(r),r[yt]=t,t.stateNode=r}return xe(t),null;case 13:if(K(J),r=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(q&&Ue!==null&&t.mode&1&&!(t.flags&128))r0(),Nr(),t.flags|=98560,s=!1;else if(s=Es(t),r!==null&&r.dehydrated!==null){if(e===null){if(!s)throw Error(P(318));if(s=t.memoizedState,s=s!==null?s.dehydrated:null,!s)throw Error(P(317));s[yt]=t}else Nr(),!(t.flags&128)&&(t.memoizedState=null),t.flags|=4;xe(t),s=!1}else ct!==null&&(iu(ct),ct=null),s=!0;if(!s)return t.flags&65536?t:null}return t.flags&128?(t.lanes=n,t):(r=r!==null,r!==(e!==null&&e.memoizedState!==null)&&r&&(t.child.flags|=8192,t.mode&1&&(e===null||J.current&1?le===0&&(le=3):_c())),t.updateQueue!==null&&(t.flags|=4),xe(t),null);case 4:return Dr(),Ql(e,t),e===null&&Ui(t.stateNode.containerInfo),xe(t),null;case 10:return ic(t.type._context),xe(t),null;case 17:return Me(t.type)&&lo(),xe(t),null;case 19:if(K(J),s=t.memoizedState,s===null)return xe(t),null;if(r=(t.flags&128)!==0,o=s.rendering,o===null)if(r)si(s,!1);else{if(le!==0||e!==null&&e.flags&128)for(e=t.child;e!==null;){if(o=go(e),o!==null){for(t.flags|=128,si(s,!1),r=o.updateQueue,r!==null&&(t.updateQueue=r,t.flags|=4),t.subtreeFlags=0,r=n,n=t.child;n!==null;)s=n,e=r,s.flags&=14680066,o=s.alternate,o===null?(s.childLanes=0,s.lanes=e,s.child=null,s.subtreeFlags=0,s.memoizedProps=null,s.memoizedState=null,s.updateQueue=null,s.dependencies=null,s.stateNode=null):(s.childLanes=o.childLanes,s.lanes=o.lanes,s.child=o.child,s.subtreeFlags=0,s.deletions=null,s.memoizedProps=o.memoizedProps,s.memoizedState=o.memoizedState,s.updateQueue=o.updateQueue,s.type=o.type,e=o.dependencies,s.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),n=n.sibling;return F(J,J.current&1|2),t.child}e=e.sibling}s.tail!==null&&re()>$r&&(t.flags|=128,r=!0,si(s,!1),t.lanes=4194304)}else{if(!r)if(e=go(o),e!==null){if(t.flags|=128,r=!0,n=e.updateQueue,n!==null&&(t.updateQueue=n,t.flags|=4),si(s,!0),s.tail===null&&s.tailMode==="hidden"&&!o.alternate&&!q)return xe(t),null}else 2*re()-s.renderingStartTime>$r&&n!==1073741824&&(t.flags|=128,r=!0,si(s,!1),t.lanes=4194304);s.isBackwards?(o.sibling=t.child,t.child=o):(n=s.last,n!==null?n.sibling=o:t.child=o,s.last=o)}return s.tail!==null?(t=s.tail,s.rendering=t,s.tail=t.sibling,s.renderingStartTime=re(),t.sibling=null,n=J.current,F(J,r?n&1|2:n&1),t):(xe(t),null);case 22:case 23:return xc(),r=t.memoizedState!==null,e!==null&&e.memoizedState!==null!==r&&(t.flags|=8192),r&&t.mode&1?Be&1073741824&&(xe(t),t.subtreeFlags&6&&(t.flags|=8192)):xe(t),null;case 24:return null;case 25:return null}throw Error(P(156,t.tag))}function M1(e,t){switch(ec(t),t.tag){case 1:return Me(t.type)&&lo(),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Dr(),K(Ne),K(ke),uc(),e=t.flags,e&65536&&!(e&128)?(t.flags=e&-65537|128,t):null;case 5:return lc(t),null;case 13:if(K(J),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(P(340));Nr()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return K(J),null;case 4:return Dr(),null;case 10:return ic(t.type._context),null;case 22:case 23:return xc(),null;case 24:return null;default:return null}}var Ts=!1,Se=!1,D1=typeof WeakSet=="function"?WeakSet:Set,j=null;function yr(e,t){var n=e.ref;if(n!==null)if(typeof n=="function")try{n(null)}catch(r){ee(e,t,r)}else n.current=null}function Yl(e,t,n){try{n()}catch(r){ee(e,t,r)}}var rh=!1;function V1(e,t){if(Il=io,e=Hp(),Xu(e)){if("selectionStart"in e)var n={start:e.selectionStart,end:e.selectionEnd};else e:{n=(n=e.ownerDocument)&&n.defaultView||window;var r=n.getSelection&&n.getSelection();if(r&&r.rangeCount!==0){n=r.anchorNode;var i=r.anchorOffset,s=r.focusNode;r=r.focusOffset;try{n.nodeType,s.nodeType}catch{n=null;break e}var o=0,a=-1,l=-1,u=0,c=0,d=e,h=null;t:for(;;){for(var f;d!==n||i!==0&&d.nodeType!==3||(a=o+i),d!==s||r!==0&&d.nodeType!==3||(l=o+r),d.nodeType===3&&(o+=d.nodeValue.length),(f=d.firstChild)!==null;)h=d,d=f;for(;;){if(d===e)break t;if(h===n&&++u===i&&(a=o),h===s&&++c===r&&(l=o),(f=d.nextSibling)!==null)break;d=h,h=d.parentNode}d=f}n=a===-1||l===-1?null:{start:a,end:l}}else n=null}n=n||{start:0,end:0}}else n=null;for(Nl={focusedElem:e,selectionRange:n},io=!1,j=t;j!==null;)if(t=j,e=t.child,(t.subtreeFlags&1028)!==0&&e!==null)e.return=t,j=e;else for(;j!==null;){t=j;try{var g=t.alternate;if(t.flags&1024)switch(t.tag){case 0:case 11:case 15:break;case 1:if(g!==null){var w=g.memoizedProps,x=g.memoizedState,y=t.stateNode,p=y.getSnapshotBeforeUpdate(t.elementType===t.type?w:at(t.type,w),x);y.__reactInternalSnapshotBeforeUpdate=p}break;case 3:var m=t.stateNode.containerInfo;m.nodeType===1?m.textContent="":m.nodeType===9&&m.documentElement&&m.removeChild(m.documentElement);break;case 5:case 6:case 4:case 17:break;default:throw Error(P(163))}}catch(S){ee(t,t.return,S)}if(e=t.sibling,e!==null){e.return=t.return,j=e;break}j=t.return}return g=rh,rh=!1,g}function Ei(e,t,n){var r=t.updateQueue;if(r=r!==null?r.lastEffect:null,r!==null){var i=r=r.next;do{if((i.tag&e)===e){var s=i.destroy;i.destroy=void 0,s!==void 0&&Yl(t,n,s)}i=i.next}while(i!==r)}}function Go(e,t){if(t=t.updateQueue,t=t!==null?t.lastEffect:null,t!==null){var n=t=t.next;do{if((n.tag&e)===e){var r=n.create;n.destroy=r()}n=n.next}while(n!==t)}}function Xl(e){var t=e.ref;if(t!==null){var n=e.stateNode;switch(e.tag){case 5:e=n;break;default:e=n}typeof t=="function"?t(e):t.current=e}}function z0(e){var t=e.alternate;t!==null&&(e.alternate=null,z0(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&(delete t[yt],delete t[Hi],delete t[Vl],delete t[x1],delete t[_1])),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}function B0(e){return e.tag===5||e.tag===3||e.tag===4}function ih(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||B0(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Zl(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.nodeType===8?n.parentNode.insertBefore(e,t):n.insertBefore(e,t):(n.nodeType===8?(t=n.parentNode,t.insertBefore(e,n)):(t=n,t.appendChild(e)),n=n._reactRootContainer,n!=null||t.onclick!==null||(t.onclick=ao));else if(r!==4&&(e=e.child,e!==null))for(Zl(e,t,n),e=e.sibling;e!==null;)Zl(e,t,n),e=e.sibling}function eu(e,t,n){var r=e.tag;if(r===5||r===6)e=e.stateNode,t?n.insertBefore(e,t):n.appendChild(e);else if(r!==4&&(e=e.child,e!==null))for(eu(e,t,n),e=e.sibling;e!==null;)eu(e,t,n),e=e.sibling}var ge=null,lt=!1;function Wt(e,t,n){for(n=n.child;n!==null;)U0(e,t,n),n=n.sibling}function U0(e,t,n){if(wt&&typeof wt.onCommitFiberUnmount=="function")try{wt.onCommitFiberUnmount($o,n)}catch{}switch(n.tag){case 5:Se||yr(n,t);case 6:var r=ge,i=lt;ge=null,Wt(e,t,n),ge=r,lt=i,ge!==null&&(lt?(e=ge,n=n.stateNode,e.nodeType===8?e.parentNode.removeChild(n):e.removeChild(n)):ge.removeChild(n.stateNode));break;case 18:ge!==null&&(lt?(e=ge,n=n.stateNode,e.nodeType===8?Da(e.parentNode,n):e.nodeType===1&&Da(e,n),$i(e)):Da(ge,n.stateNode));break;case 4:r=ge,i=lt,ge=n.stateNode.containerInfo,lt=!0,Wt(e,t,n),ge=r,lt=i;break;case 0:case 11:case 14:case 15:if(!Se&&(r=n.updateQueue,r!==null&&(r=r.lastEffect,r!==null))){i=r=r.next;do{var s=i,o=s.destroy;s=s.tag,o!==void 0&&(s&2||s&4)&&Yl(n,t,o),i=i.next}while(i!==r)}Wt(e,t,n);break;case 1:if(!Se&&(yr(n,t),r=n.stateNode,typeof r.componentWillUnmount=="function"))try{r.props=n.memoizedProps,r.state=n.memoizedState,r.componentWillUnmount()}catch(a){ee(n,t,a)}Wt(e,t,n);break;case 21:Wt(e,t,n);break;case 22:n.mode&1?(Se=(r=Se)||n.memoizedState!==null,Wt(e,t,n),Se=r):Wt(e,t,n);break;default:Wt(e,t,n)}}function sh(e){var t=e.updateQueue;if(t!==null){e.updateQueue=null;var n=e.stateNode;n===null&&(n=e.stateNode=new D1),t.forEach(function(r){var i=G1.bind(null,e,r);n.has(r)||(n.add(r),r.then(i,i))})}}function st(e,t){var n=t.deletions;if(n!==null)for(var r=0;r<n.length;r++){var i=n[r];try{var s=e,o=t,a=o;e:for(;a!==null;){switch(a.tag){case 5:ge=a.stateNode,lt=!1;break e;case 3:ge=a.stateNode.containerInfo,lt=!0;break e;case 4:ge=a.stateNode.containerInfo,lt=!0;break e}a=a.return}if(ge===null)throw Error(P(160));U0(s,o,i),ge=null,lt=!1;var l=i.alternate;l!==null&&(l.return=null),i.return=null}catch(u){ee(i,t,u)}}if(t.subtreeFlags&12854)for(t=t.child;t!==null;)F0(t,e),t=t.sibling}function F0(e,t){var n=e.alternate,r=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:if(st(t,e),mt(e),r&4){try{Ei(3,e,e.return),Go(3,e)}catch(w){ee(e,e.return,w)}try{Ei(5,e,e.return)}catch(w){ee(e,e.return,w)}}break;case 1:st(t,e),mt(e),r&512&&n!==null&&yr(n,n.return);break;case 5:if(st(t,e),mt(e),r&512&&n!==null&&yr(n,n.return),e.flags&32){var i=e.stateNode;try{Ni(i,"")}catch(w){ee(e,e.return,w)}}if(r&4&&(i=e.stateNode,i!=null)){var s=e.memoizedProps,o=n!==null?n.memoizedProps:s,a=e.type,l=e.updateQueue;if(e.updateQueue=null,l!==null)try{a==="input"&&s.type==="radio"&&s.name!=null&&dp(i,s),Cl(a,o);var u=Cl(a,s);for(o=0;o<l.length;o+=2){var c=l[o],d=l[o+1];c==="style"?gp(i,d):c==="dangerouslySetInnerHTML"?pp(i,d):c==="children"?Ni(i,d):$u(i,c,d,u)}switch(a){case"input":wl(i,s);break;case"textarea":hp(i,s);break;case"select":var h=i._wrapperState.wasMultiple;i._wrapperState.wasMultiple=!!s.multiple;var f=s.value;f!=null?Er(i,!!s.multiple,f,!1):h!==!!s.multiple&&(s.defaultValue!=null?Er(i,!!s.multiple,s.defaultValue,!0):Er(i,!!s.multiple,s.multiple?[]:"",!1))}i[Hi]=s}catch(w){ee(e,e.return,w)}}break;case 6:if(st(t,e),mt(e),r&4){if(e.stateNode===null)throw Error(P(162));i=e.stateNode,s=e.memoizedProps;try{i.nodeValue=s}catch(w){ee(e,e.return,w)}}break;case 3:if(st(t,e),mt(e),r&4&&n!==null&&n.memoizedState.isDehydrated)try{$i(t.containerInfo)}catch(w){ee(e,e.return,w)}break;case 4:st(t,e),mt(e);break;case 13:st(t,e),mt(e),i=e.child,i.flags&8192&&(s=i.memoizedState!==null,i.stateNode.isHidden=s,!s||i.alternate!==null&&i.alternate.memoizedState!==null||(yc=re())),r&4&&sh(e);break;case 22:if(c=n!==null&&n.memoizedState!==null,e.mode&1?(Se=(u=Se)||c,st(t,e),Se=u):st(t,e),mt(e),r&8192){if(u=e.memoizedState!==null,(e.stateNode.isHidden=u)&&!c&&e.mode&1)for(j=e,c=e.child;c!==null;){for(d=j=c;j!==null;){switch(h=j,f=h.child,h.tag){case 0:case 11:case 14:case 15:Ei(4,h,h.return);break;case 1:yr(h,h.return);var g=h.stateNode;if(typeof g.componentWillUnmount=="function"){r=h,n=h.return;try{t=r,g.props=t.memoizedProps,g.state=t.memoizedState,g.componentWillUnmount()}catch(w){ee(r,n,w)}}break;case 5:yr(h,h.return);break;case 22:if(h.memoizedState!==null){ah(d);continue}}f!==null?(f.return=h,j=f):ah(d)}c=c.sibling}e:for(c=null,d=e;;){if(d.tag===5){if(c===null){c=d;try{i=d.stateNode,u?(s=i.style,typeof s.setProperty=="function"?s.setProperty("display","none","important"):s.display="none"):(a=d.stateNode,l=d.memoizedProps.style,o=l!=null&&l.hasOwnProperty("display")?l.display:null,a.style.display=mp("display",o))}catch(w){ee(e,e.return,w)}}}else if(d.tag===6){if(c===null)try{d.stateNode.nodeValue=u?"":d.memoizedProps}catch(w){ee(e,e.return,w)}}else if((d.tag!==22&&d.tag!==23||d.memoizedState===null||d===e)&&d.child!==null){d.child.return=d,d=d.child;continue}if(d===e)break e;for(;d.sibling===null;){if(d.return===null||d.return===e)break e;c===d&&(c=null),d=d.return}c===d&&(c=null),d.sibling.return=d.return,d=d.sibling}}break;case 19:st(t,e),mt(e),r&4&&sh(e);break;case 21:break;default:st(t,e),mt(e)}}function mt(e){var t=e.flags;if(t&2){try{e:{for(var n=e.return;n!==null;){if(B0(n)){var r=n;break e}n=n.return}throw Error(P(160))}switch(r.tag){case 5:var i=r.stateNode;r.flags&32&&(Ni(i,""),r.flags&=-33);var s=ih(e);eu(e,s,i);break;case 3:case 4:var o=r.stateNode.containerInfo,a=ih(e);Zl(e,a,o);break;default:throw Error(P(161))}}catch(l){ee(e,e.return,l)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function $1(e,t,n){j=e,H0(e)}function H0(e,t,n){for(var r=(e.mode&1)!==0;j!==null;){var i=j,s=i.child;if(i.tag===22&&r){var o=i.memoizedState!==null||Ts;if(!o){var a=i.alternate,l=a!==null&&a.memoizedState!==null||Se;a=Ts;var u=Se;if(Ts=o,(Se=l)&&!u)for(j=i;j!==null;)o=j,l=o.child,o.tag===22&&o.memoizedState!==null?lh(i):l!==null?(l.return=o,j=l):lh(i);for(;s!==null;)j=s,H0(s),s=s.sibling;j=i,Ts=a,Se=u}oh(e)}else i.subtreeFlags&8772&&s!==null?(s.return=i,j=s):oh(e)}}function oh(e){for(;j!==null;){var t=j;if(t.flags&8772){var n=t.alternate;try{if(t.flags&8772)switch(t.tag){case 0:case 11:case 15:Se||Go(5,t);break;case 1:var r=t.stateNode;if(t.flags&4&&!Se)if(n===null)r.componentDidMount();else{var i=t.elementType===t.type?n.memoizedProps:at(t.type,n.memoizedProps);r.componentDidUpdate(i,n.memoizedState,r.__reactInternalSnapshotBeforeUpdate)}var s=t.updateQueue;s!==null&&Hd(t,s,r);break;case 3:var o=t.updateQueue;if(o!==null){if(n=null,t.child!==null)switch(t.child.tag){case 5:n=t.child.stateNode;break;case 1:n=t.child.stateNode}Hd(t,o,n)}break;case 5:var a=t.stateNode;if(n===null&&t.flags&4){n=a;var l=t.memoizedProps;switch(t.type){case"button":case"input":case"select":case"textarea":l.autoFocus&&n.focus();break;case"img":l.src&&(n.src=l.src)}}break;case 6:break;case 4:break;case 12:break;case 13:if(t.memoizedState===null){var u=t.alternate;if(u!==null){var c=u.memoizedState;if(c!==null){var d=c.dehydrated;d!==null&&$i(d)}}}break;case 19:case 17:case 21:case 22:case 23:case 25:break;default:throw Error(P(163))}Se||t.flags&512&&Xl(t)}catch(h){ee(t,t.return,h)}}if(t===e){j=null;break}if(n=t.sibling,n!==null){n.return=t.return,j=n;break}j=t.return}}function ah(e){for(;j!==null;){var t=j;if(t===e){j=null;break}var n=t.sibling;if(n!==null){n.return=t.return,j=n;break}j=t.return}}function lh(e){for(;j!==null;){var t=j;try{switch(t.tag){case 0:case 11:case 15:var n=t.return;try{Go(4,t)}catch(l){ee(t,n,l)}break;case 1:var r=t.stateNode;if(typeof r.componentDidMount=="function"){var i=t.return;try{r.componentDidMount()}catch(l){ee(t,i,l)}}var s=t.return;try{Xl(t)}catch(l){ee(t,s,l)}break;case 5:var o=t.return;try{Xl(t)}catch(l){ee(t,o,l)}}}catch(l){ee(t,t.return,l)}if(t===e){j=null;break}var a=t.sibling;if(a!==null){a.return=t.return,j=a;break}j=t.return}}var z1=Math.ceil,wo=Bt.ReactCurrentDispatcher,gc=Bt.ReactCurrentOwner,tt=Bt.ReactCurrentBatchConfig,z=0,fe=null,oe=null,ve=0,Be=0,wr=_n(0),le=0,Qi=null,Jn=0,qo=0,vc=0,bi=null,Le=null,yc=0,$r=1/0,bt=null,xo=!1,tu=null,un=null,js=!1,tn=null,_o=0,Pi=0,nu=null,Ks=-1,Gs=0;function Pe(){return z&6?re():Ks!==-1?Ks:Ks=re()}function cn(e){return e.mode&1?z&2&&ve!==0?ve&-ve:k1.transition!==null?(Gs===0&&(Gs=Tp()),Gs):(e=B,e!==0||(e=window.event,e=e===void 0?16:Np(e.type)),e):1}function ht(e,t,n,r){if(50<Pi)throw Pi=0,nu=null,Error(P(185));rs(e,n,r),(!(z&2)||e!==fe)&&(e===fe&&(!(z&2)&&(qo|=n),le===4&&Xt(e,ve)),De(e,r),n===1&&z===0&&!(t.mode&1)&&($r=re()+500,Ho&&Sn()))}function De(e,t){var n=e.callbackNode;ky(e,t);var r=ro(e,e===fe?ve:0);if(r===0)n!==null&&vd(n),e.callbackNode=null,e.callbackPriority=0;else if(t=r&-r,e.callbackPriority!==t){if(n!=null&&vd(n),t===1)e.tag===0?S1(uh.bind(null,e)):e0(uh.bind(null,e)),y1(function(){!(z&6)&&Sn()}),n=null;else{switch(jp(r)){case 1:n=Hu;break;case 4:n=bp;break;case 16:n=no;break;case 536870912:n=Pp;break;default:n=no}n=X0(n,W0.bind(null,e))}e.callbackPriority=t,e.callbackNode=n}}function W0(e,t){if(Ks=-1,Gs=0,z&6)throw Error(P(327));var n=e.callbackNode;if(Ar()&&e.callbackNode!==n)return null;var r=ro(e,e===fe?ve:0);if(r===0)return null;if(r&30||r&e.expiredLanes||t)t=So(e,r);else{t=r;var i=z;z|=2;var s=G0();(fe!==e||ve!==t)&&(bt=null,$r=re()+500,Hn(e,t));do try{F1();break}catch(a){K0(e,a)}while(1);rc(),wo.current=s,z=i,oe!==null?t=0:(fe=null,ve=0,t=le)}if(t!==0){if(t===2&&(i=jl(e),i!==0&&(r=i,t=ru(e,i))),t===1)throw n=Qi,Hn(e,0),Xt(e,r),De(e,re()),n;if(t===6)Xt(e,r);else{if(i=e.current.alternate,!(r&30)&&!B1(i)&&(t=So(e,r),t===2&&(s=jl(e),s!==0&&(r=s,t=ru(e,s))),t===1))throw n=Qi,Hn(e,0),Xt(e,r),De(e,re()),n;switch(e.finishedWork=i,e.finishedLanes=r,t){case 0:case 1:throw Error(P(345));case 2:On(e,Le,bt);break;case 3:if(Xt(e,r),(r&130023424)===r&&(t=yc+500-re(),10<t)){if(ro(e,0)!==0)break;if(i=e.suspendedLanes,(i&r)!==r){Pe(),e.pingedLanes|=e.suspendedLanes&i;break}e.timeoutHandle=Dl(On.bind(null,e,Le,bt),t);break}On(e,Le,bt);break;case 4:if(Xt(e,r),(r&4194240)===r)break;for(t=e.eventTimes,i=-1;0<r;){var o=31-dt(r);s=1<<o,o=t[o],o>i&&(i=o),r&=~s}if(r=i,r=re()-r,r=(120>r?120:480>r?480:1080>r?1080:1920>r?1920:3e3>r?3e3:4320>r?4320:1960*z1(r/1960))-r,10<r){e.timeoutHandle=Dl(On.bind(null,e,Le,bt),r);break}On(e,Le,bt);break;case 5:On(e,Le,bt);break;default:throw Error(P(329))}}}return De(e,re()),e.callbackNode===n?W0.bind(null,e):null}function ru(e,t){var n=bi;return e.current.memoizedState.isDehydrated&&(Hn(e,t).flags|=256),e=So(e,t),e!==2&&(t=Le,Le=n,t!==null&&iu(t)),e}function iu(e){Le===null?Le=e:Le.push.apply(Le,e)}function B1(e){for(var t=e;;){if(t.flags&16384){var n=t.updateQueue;if(n!==null&&(n=n.stores,n!==null))for(var r=0;r<n.length;r++){var i=n[r],s=i.getSnapshot;i=i.value;try{if(!ft(s(),i))return!1}catch{return!1}}}if(n=t.child,t.subtreeFlags&16384&&n!==null)n.return=t,t=n;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Xt(e,t){for(t&=~vc,t&=~qo,e.suspendedLanes|=t,e.pingedLanes&=~t,e=e.expirationTimes;0<t;){var n=31-dt(t),r=1<<n;e[n]=-1,t&=~r}}function uh(e){if(z&6)throw Error(P(327));Ar();var t=ro(e,0);if(!(t&1))return De(e,re()),null;var n=So(e,t);if(e.tag!==0&&n===2){var r=jl(e);r!==0&&(t=r,n=ru(e,r))}if(n===1)throw n=Qi,Hn(e,0),Xt(e,t),De(e,re()),n;if(n===6)throw Error(P(345));return e.finishedWork=e.current.alternate,e.finishedLanes=t,On(e,Le,bt),De(e,re()),null}function wc(e,t){var n=z;z|=1;try{return e(t)}finally{z=n,z===0&&($r=re()+500,Ho&&Sn())}}function Qn(e){tn!==null&&tn.tag===0&&!(z&6)&&Ar();var t=z;z|=1;var n=tt.transition,r=B;try{if(tt.transition=null,B=1,e)return e()}finally{B=r,tt.transition=n,z=t,!(z&6)&&Sn()}}function xc(){Be=wr.current,K(wr)}function Hn(e,t){e.finishedWork=null,e.finishedLanes=0;var n=e.timeoutHandle;if(n!==-1&&(e.timeoutHandle=-1,v1(n)),oe!==null)for(n=oe.return;n!==null;){var r=n;switch(ec(r),r.tag){case 1:r=r.type.childContextTypes,r!=null&&lo();break;case 3:Dr(),K(Ne),K(ke),uc();break;case 5:lc(r);break;case 4:Dr();break;case 13:K(J);break;case 19:K(J);break;case 10:ic(r.type._context);break;case 22:case 23:xc()}n=n.return}if(fe=e,oe=e=dn(e.current,null),ve=Be=t,le=0,Qi=null,vc=qo=Jn=0,Le=bi=null,Bn!==null){for(t=0;t<Bn.length;t++)if(n=Bn[t],r=n.interleaved,r!==null){n.interleaved=null;var i=r.next,s=n.pending;if(s!==null){var o=s.next;s.next=i,r.next=o}n.pending=r}Bn=null}return e}function K0(e,t){do{var n=oe;try{if(rc(),Fs.current=yo,vo){for(var r=X.memoizedState;r!==null;){var i=r.queue;i!==null&&(i.pending=null),r=r.next}vo=!1}if(qn=0,he=ae=X=null,Ci=!1,Gi=0,gc.current=null,n===null||n.return===null){le=1,Qi=t,oe=null;break}e:{var s=e,o=n.return,a=n,l=t;if(t=ve,a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){var u=l,c=a,d=c.tag;if(!(c.mode&1)&&(d===0||d===11||d===15)){var h=c.alternate;h?(c.updateQueue=h.updateQueue,c.memoizedState=h.memoizedState,c.lanes=h.lanes):(c.updateQueue=null,c.memoizedState=null)}var f=Qd(o);if(f!==null){f.flags&=-257,Yd(f,o,a,s,t),f.mode&1&&Jd(s,u,t),t=f,l=u;var g=t.updateQueue;if(g===null){var w=new Set;w.add(l),t.updateQueue=w}else g.add(l);break e}else{if(!(t&1)){Jd(s,u,t),_c();break e}l=Error(P(426))}}else if(q&&a.mode&1){var x=Qd(o);if(x!==null){!(x.flags&65536)&&(x.flags|=256),Yd(x,o,a,s,t),tc(Vr(l,a));break e}}s=l=Vr(l,a),le!==4&&(le=2),bi===null?bi=[s]:bi.push(s),s=o;do{switch(s.tag){case 3:s.flags|=65536,t&=-t,s.lanes|=t;var y=j0(s,l,t);Fd(s,y);break e;case 1:a=l;var p=s.type,m=s.stateNode;if(!(s.flags&128)&&(typeof p.getDerivedStateFromError=="function"||m!==null&&typeof m.componentDidCatch=="function"&&(un===null||!un.has(m)))){s.flags|=65536,t&=-t,s.lanes|=t;var S=A0(s,a,t);Fd(s,S);break e}}s=s.return}while(s!==null)}J0(n)}catch(k){t=k,oe===n&&n!==null&&(oe=n=n.return);continue}break}while(1)}function G0(){var e=wo.current;return wo.current=yo,e===null?yo:e}function _c(){(le===0||le===3||le===2)&&(le=4),fe===null||!(Jn&268435455)&&!(qo&268435455)||Xt(fe,ve)}function So(e,t){var n=z;z|=2;var r=G0();(fe!==e||ve!==t)&&(bt=null,Hn(e,t));do try{U1();break}catch(i){K0(e,i)}while(1);if(rc(),z=n,wo.current=r,oe!==null)throw Error(P(261));return fe=null,ve=0,le}function U1(){for(;oe!==null;)q0(oe)}function F1(){for(;oe!==null&&!py();)q0(oe)}function q0(e){var t=Y0(e.alternate,e,Be);e.memoizedProps=e.pendingProps,t===null?J0(e):oe=t,gc.current=null}function J0(e){var t=e;do{var n=t.alternate;if(e=t.return,t.flags&32768){if(n=M1(n,t),n!==null){n.flags&=32767,oe=n;return}if(e!==null)e.flags|=32768,e.subtreeFlags=0,e.deletions=null;else{le=6,oe=null;return}}else if(n=N1(n,t,Be),n!==null){oe=n;return}if(t=t.sibling,t!==null){oe=t;return}oe=t=e}while(t!==null);le===0&&(le=5)}function On(e,t,n){var r=B,i=tt.transition;try{tt.transition=null,B=1,H1(e,t,n,r)}finally{tt.transition=i,B=r}return null}function H1(e,t,n,r){do Ar();while(tn!==null);if(z&6)throw Error(P(327));n=e.finishedWork;var i=e.finishedLanes;if(n===null)return null;if(e.finishedWork=null,e.finishedLanes=0,n===e.current)throw Error(P(177));e.callbackNode=null,e.callbackPriority=0;var s=n.lanes|n.childLanes;if(Cy(e,s),e===fe&&(oe=fe=null,ve=0),!(n.subtreeFlags&2064)&&!(n.flags&2064)||js||(js=!0,X0(no,function(){return Ar(),null})),s=(n.flags&15990)!==0,n.subtreeFlags&15990||s){s=tt.transition,tt.transition=null;var o=B;B=1;var a=z;z|=4,gc.current=null,V1(e,n),F0(n,e),c1(Nl),io=!!Il,Nl=Il=null,e.current=n,$1(n),my(),z=a,B=o,tt.transition=s}else e.current=n;if(js&&(js=!1,tn=e,_o=i),s=e.pendingLanes,s===0&&(un=null),yy(n.stateNode),De(e,re()),t!==null)for(r=e.onRecoverableError,n=0;n<t.length;n++)i=t[n],r(i.value,{componentStack:i.stack,digest:i.digest});if(xo)throw xo=!1,e=tu,tu=null,e;return _o&1&&e.tag!==0&&Ar(),s=e.pendingLanes,s&1?e===nu?Pi++:(Pi=0,nu=e):Pi=0,Sn(),null}function Ar(){if(tn!==null){var e=jp(_o),t=tt.transition,n=B;try{if(tt.transition=null,B=16>e?16:e,tn===null)var r=!1;else{if(e=tn,tn=null,_o=0,z&6)throw Error(P(331));var i=z;for(z|=4,j=e.current;j!==null;){var s=j,o=s.child;if(j.flags&16){var a=s.deletions;if(a!==null){for(var l=0;l<a.length;l++){var u=a[l];for(j=u;j!==null;){var c=j;switch(c.tag){case 0:case 11:case 15:Ei(8,c,s)}var d=c.child;if(d!==null)d.return=c,j=d;else for(;j!==null;){c=j;var h=c.sibling,f=c.return;if(z0(c),c===u){j=null;break}if(h!==null){h.return=f,j=h;break}j=f}}}var g=s.alternate;if(g!==null){var w=g.child;if(w!==null){g.child=null;do{var x=w.sibling;w.sibling=null,w=x}while(w!==null)}}j=s}}if(s.subtreeFlags&2064&&o!==null)o.return=s,j=o;else e:for(;j!==null;){if(s=j,s.flags&2048)switch(s.tag){case 0:case 11:case 15:Ei(9,s,s.return)}var y=s.sibling;if(y!==null){y.return=s.return,j=y;break e}j=s.return}}var p=e.current;for(j=p;j!==null;){o=j;var m=o.child;if(o.subtreeFlags&2064&&m!==null)m.return=o,j=m;else e:for(o=p;j!==null;){if(a=j,a.flags&2048)try{switch(a.tag){case 0:case 11:case 15:Go(9,a)}}catch(k){ee(a,a.return,k)}if(a===o){j=null;break e}var S=a.sibling;if(S!==null){S.return=a.return,j=S;break e}j=a.return}}if(z=i,Sn(),wt&&typeof wt.onPostCommitFiberRoot=="function")try{wt.onPostCommitFiberRoot($o,e)}catch{}r=!0}return r}finally{B=n,tt.transition=t}}return!1}function ch(e,t,n){t=Vr(n,t),t=j0(e,t,1),e=ln(e,t,1),t=Pe(),e!==null&&(rs(e,1,t),De(e,t))}function ee(e,t,n){if(e.tag===3)ch(e,e,n);else for(;t!==null;){if(t.tag===3){ch(t,e,n);break}else if(t.tag===1){var r=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof r.componentDidCatch=="function"&&(un===null||!un.has(r))){e=Vr(n,e),e=A0(t,e,1),t=ln(t,e,1),e=Pe(),t!==null&&(rs(t,1,e),De(t,e));break}}t=t.return}}function W1(e,t,n){var r=e.pingCache;r!==null&&r.delete(t),t=Pe(),e.pingedLanes|=e.suspendedLanes&n,fe===e&&(ve&n)===n&&(le===4||le===3&&(ve&130023424)===ve&&500>re()-yc?Hn(e,0):vc|=n),De(e,t)}function Q0(e,t){t===0&&(e.mode&1?(t=ws,ws<<=1,!(ws&130023424)&&(ws=4194304)):t=1);var n=Pe();e=Dt(e,t),e!==null&&(rs(e,t,n),De(e,n))}function K1(e){var t=e.memoizedState,n=0;t!==null&&(n=t.retryLane),Q0(e,n)}function G1(e,t){var n=0;switch(e.tag){case 13:var r=e.stateNode,i=e.memoizedState;i!==null&&(n=i.retryLane);break;case 19:r=e.stateNode;break;default:throw Error(P(314))}r!==null&&r.delete(t),Q0(e,n)}var Y0;Y0=function(e,t,n){if(e!==null)if(e.memoizedProps!==t.pendingProps||Ne.current)Ie=!0;else{if(!(e.lanes&n)&&!(t.flags&128))return Ie=!1,I1(e,t,n);Ie=!!(e.flags&131072)}else Ie=!1,q&&t.flags&1048576&&t0(t,ho,t.index);switch(t.lanes=0,t.tag){case 2:var r=t.type;Ws(e,t),e=t.pendingProps;var i=Ir(t,ke.current);jr(t,n),i=dc(null,t,r,e,i,n);var s=hc();return t.flags|=1,typeof i=="object"&&i!==null&&typeof i.render=="function"&&i.$$typeof===void 0?(t.tag=1,t.memoizedState=null,t.updateQueue=null,Me(r)?(s=!0,uo(t)):s=!1,t.memoizedState=i.state!==null&&i.state!==void 0?i.state:null,oc(t),i.updater=Ko,t.stateNode=i,i._reactInternals=t,Hl(t,r,e,n),t=Gl(null,t,r,!0,s,n)):(t.tag=0,q&&s&&Zu(t),be(null,t,i,n),t=t.child),t;case 16:r=t.elementType;e:{switch(Ws(e,t),e=t.pendingProps,i=r._init,r=i(r._payload),t.type=r,i=t.tag=J1(r),e=at(r,e),i){case 0:t=Kl(null,t,r,e,n);break e;case 1:t=eh(null,t,r,e,n);break e;case 11:t=Xd(null,t,r,e,n);break e;case 14:t=Zd(null,t,r,at(r.type,e),n);break e}throw Error(P(306,r,""))}return t;case 0:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:at(r,i),Kl(e,t,r,i,n);case 1:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:at(r,i),eh(e,t,r,i,n);case 3:e:{if(I0(t),e===null)throw Error(P(387));r=t.pendingProps,s=t.memoizedState,i=s.element,a0(e,t),mo(t,r,null,n);var o=t.memoizedState;if(r=o.element,s.isDehydrated)if(s={element:r,isDehydrated:!1,cache:o.cache,pendingSuspenseBoundaries:o.pendingSuspenseBoundaries,transitions:o.transitions},t.updateQueue.baseState=s,t.memoizedState=s,t.flags&256){i=Vr(Error(P(423)),t),t=th(e,t,r,n,i);break e}else if(r!==i){i=Vr(Error(P(424)),t),t=th(e,t,r,n,i);break e}else for(Ue=an(t.stateNode.containerInfo.firstChild),He=t,q=!0,ct=null,n=s0(t,null,r,n),t.child=n;n;)n.flags=n.flags&-3|4096,n=n.sibling;else{if(Nr(),r===i){t=Vt(e,t,n);break e}be(e,t,r,n)}t=t.child}return t;case 5:return l0(t),e===null&&Bl(t),r=t.type,i=t.pendingProps,s=e!==null?e.memoizedProps:null,o=i.children,Ml(r,i)?o=null:s!==null&&Ml(r,s)&&(t.flags|=32),O0(e,t),be(e,t,o,n),t.child;case 6:return e===null&&Bl(t),null;case 13:return N0(e,t,n);case 4:return ac(t,t.stateNode.containerInfo),r=t.pendingProps,e===null?t.child=Mr(t,null,r,n):be(e,t,r,n),t.child;case 11:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:at(r,i),Xd(e,t,r,i,n);case 7:return be(e,t,t.pendingProps,n),t.child;case 8:return be(e,t,t.pendingProps.children,n),t.child;case 12:return be(e,t,t.pendingProps.children,n),t.child;case 10:e:{if(r=t.type._context,i=t.pendingProps,s=t.memoizedProps,o=i.value,F(fo,r._currentValue),r._currentValue=o,s!==null)if(ft(s.value,o)){if(s.children===i.children&&!Ne.current){t=Vt(e,t,n);break e}}else for(s=t.child,s!==null&&(s.return=t);s!==null;){var a=s.dependencies;if(a!==null){o=s.child;for(var l=a.firstContext;l!==null;){if(l.context===r){if(s.tag===1){l=Lt(-1,n&-n),l.tag=2;var u=s.updateQueue;if(u!==null){u=u.shared;var c=u.pending;c===null?l.next=l:(l.next=c.next,c.next=l),u.pending=l}}s.lanes|=n,l=s.alternate,l!==null&&(l.lanes|=n),Ul(s.return,n,t),a.lanes|=n;break}l=l.next}}else if(s.tag===10)o=s.type===t.type?null:s.child;else if(s.tag===18){if(o=s.return,o===null)throw Error(P(341));o.lanes|=n,a=o.alternate,a!==null&&(a.lanes|=n),Ul(o,n,t),o=s.sibling}else o=s.child;if(o!==null)o.return=s;else for(o=s;o!==null;){if(o===t){o=null;break}if(s=o.sibling,s!==null){s.return=o.return,o=s;break}o=o.return}s=o}be(e,t,i.children,n),t=t.child}return t;case 9:return i=t.type,r=t.pendingProps.children,jr(t,n),i=rt(i),r=r(i),t.flags|=1,be(e,t,r,n),t.child;case 14:return r=t.type,i=at(r,t.pendingProps),i=at(r.type,i),Zd(e,t,r,i,n);case 15:return R0(e,t,t.type,t.pendingProps,n);case 17:return r=t.type,i=t.pendingProps,i=t.elementType===r?i:at(r,i),Ws(e,t),t.tag=1,Me(r)?(e=!0,uo(t)):e=!1,jr(t,n),T0(t,r,i),Hl(t,r,i,n),Gl(null,t,r,!0,e,n);case 19:return M0(e,t,n);case 22:return L0(e,t,n)}throw Error(P(156,t.tag))};function X0(e,t){return Ep(e,t)}function q1(e,t,n,r){this.tag=e,this.key=n,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=r,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function et(e,t,n,r){return new q1(e,t,n,r)}function Sc(e){return e=e.prototype,!(!e||!e.isReactComponent)}function J1(e){if(typeof e=="function")return Sc(e)?1:0;if(e!=null){if(e=e.$$typeof,e===Bu)return 11;if(e===Uu)return 14}return 2}function dn(e,t){var n=e.alternate;return n===null?(n=et(e.tag,t,e.key,e.mode),n.elementType=e.elementType,n.type=e.type,n.stateNode=e.stateNode,n.alternate=e,e.alternate=n):(n.pendingProps=t,n.type=e.type,n.flags=0,n.subtreeFlags=0,n.deletions=null),n.flags=e.flags&14680064,n.childLanes=e.childLanes,n.lanes=e.lanes,n.child=e.child,n.memoizedProps=e.memoizedProps,n.memoizedState=e.memoizedState,n.updateQueue=e.updateQueue,t=e.dependencies,n.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},n.sibling=e.sibling,n.index=e.index,n.ref=e.ref,n}function qs(e,t,n,r,i,s){var o=2;if(r=e,typeof e=="function")Sc(e)&&(o=1);else if(typeof e=="string")o=5;else e:switch(e){case ur:return Wn(n.children,i,s,t);case zu:o=8,i|=8;break;case pl:return e=et(12,n,t,i|2),e.elementType=pl,e.lanes=s,e;case ml:return e=et(13,n,t,i),e.elementType=ml,e.lanes=s,e;case gl:return e=et(19,n,t,i),e.elementType=gl,e.lanes=s,e;case lp:return Jo(n,i,s,t);default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case op:o=10;break e;case ap:o=9;break e;case Bu:o=11;break e;case Uu:o=14;break e;case Gt:o=16,r=null;break e}throw Error(P(130,e==null?e:typeof e,""))}return t=et(o,n,t,i),t.elementType=e,t.type=r,t.lanes=s,t}function Wn(e,t,n,r){return e=et(7,e,r,t),e.lanes=n,e}function Jo(e,t,n,r){return e=et(22,e,r,t),e.elementType=lp,e.lanes=n,e.stateNode={isHidden:!1},e}function Wa(e,t,n){return e=et(6,e,null,t),e.lanes=n,e}function Ka(e,t,n){return t=et(4,e.children!==null?e.children:[],e.key,t),t.lanes=n,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}function Q1(e,t,n,r,i){this.tag=t,this.containerInfo=e,this.finishedWork=this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.pendingContext=this.context=null,this.callbackPriority=0,this.eventTimes=ba(0),this.expirationTimes=ba(-1),this.entangledLanes=this.finishedLanes=this.mutableReadLanes=this.expiredLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=ba(0),this.identifierPrefix=r,this.onRecoverableError=i,this.mutableSourceEagerHydrationData=null}function kc(e,t,n,r,i,s,o,a,l){return e=new Q1(e,t,n,a,l),t===1?(t=1,s===!0&&(t|=8)):t=0,s=et(3,null,null,t),e.current=s,s.stateNode=e,s.memoizedState={element:r,isDehydrated:n,cache:null,transitions:null,pendingSuspenseBoundaries:null},oc(s),e}function Y1(e,t,n){var r=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:lr,key:r==null?null:""+r,children:e,containerInfo:t,implementation:n}}function Z0(e){if(!e)return vn;e=e._reactInternals;e:{if(Xn(e)!==e||e.tag!==1)throw Error(P(170));var t=e;do{switch(t.tag){case 3:t=t.stateNode.context;break e;case 1:if(Me(t.type)){t=t.stateNode.__reactInternalMemoizedMergedChildContext;break e}}t=t.return}while(t!==null);throw Error(P(171))}if(e.tag===1){var n=e.type;if(Me(n))return Zp(e,n,t)}return t}function em(e,t,n,r,i,s,o,a,l){return e=kc(n,r,!0,e,i,s,o,a,l),e.context=Z0(null),n=e.current,r=Pe(),i=cn(n),s=Lt(r,i),s.callback=t??null,ln(n,s,i),e.current.lanes=i,rs(e,i,r),De(e,r),e}function Qo(e,t,n,r){var i=t.current,s=Pe(),o=cn(i);return n=Z0(n),t.context===null?t.context=n:t.pendingContext=n,t=Lt(s,o),t.payload={element:e},r=r===void 0?null:r,r!==null&&(t.callback=r),e=ln(i,t,o),e!==null&&(ht(e,i,o,s),Us(e,i,o)),o}function ko(e){if(e=e.current,!e.child)return null;switch(e.child.tag){case 5:return e.child.stateNode;default:return e.child.stateNode}}function dh(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var n=e.retryLane;e.retryLane=n!==0&&n<t?n:t}}function Cc(e,t){dh(e,t),(e=e.alternate)&&dh(e,t)}function X1(){return null}var tm=typeof reportError=="function"?reportError:function(e){console.error(e)};function Ec(e){this._internalRoot=e}Yo.prototype.render=Ec.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(P(409));Qo(e,t,null,null)};Yo.prototype.unmount=Ec.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;Qn(function(){Qo(null,e,null,null)}),t[Mt]=null}};function Yo(e){this._internalRoot=e}Yo.prototype.unstable_scheduleHydration=function(e){if(e){var t=Lp();e={blockedOn:null,target:e,priority:t};for(var n=0;n<Yt.length&&t!==0&&t<Yt[n].priority;n++);Yt.splice(n,0,e),n===0&&Ip(e)}};function bc(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Xo(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11&&(e.nodeType!==8||e.nodeValue!==" react-mount-point-unstable "))}function hh(){}function Z1(e,t,n,r,i){if(i){if(typeof r=="function"){var s=r;r=function(){var u=ko(o);s.call(u)}}var o=em(t,r,e,0,null,!1,!1,"",hh);return e._reactRootContainer=o,e[Mt]=o.current,Ui(e.nodeType===8?e.parentNode:e),Qn(),o}for(;i=e.lastChild;)e.removeChild(i);if(typeof r=="function"){var a=r;r=function(){var u=ko(l);a.call(u)}}var l=kc(e,0,!1,null,null,!1,!1,"",hh);return e._reactRootContainer=l,e[Mt]=l.current,Ui(e.nodeType===8?e.parentNode:e),Qn(function(){Qo(t,l,n,r)}),l}function Zo(e,t,n,r,i){var s=n._reactRootContainer;if(s){var o=s;if(typeof i=="function"){var a=i;i=function(){var l=ko(o);a.call(l)}}Qo(t,o,e,i)}else o=Z1(n,t,e,i,r);return ko(o)}Ap=function(e){switch(e.tag){case 3:var t=e.stateNode;if(t.current.memoizedState.isDehydrated){var n=hi(t.pendingLanes);n!==0&&(Wu(t,n|1),De(t,re()),!(z&6)&&($r=re()+500,Sn()))}break;case 13:Qn(function(){var r=Dt(e,1);if(r!==null){var i=Pe();ht(r,e,1,i)}}),Cc(e,1)}};Ku=function(e){if(e.tag===13){var t=Dt(e,134217728);if(t!==null){var n=Pe();ht(t,e,134217728,n)}Cc(e,134217728)}};Rp=function(e){if(e.tag===13){var t=cn(e),n=Dt(e,t);if(n!==null){var r=Pe();ht(n,e,t,r)}Cc(e,t)}};Lp=function(){return B};Op=function(e,t){var n=B;try{return B=e,t()}finally{B=n}};bl=function(e,t,n){switch(t){case"input":if(wl(e,n),t=n.name,n.type==="radio"&&t!=null){for(n=e;n.parentNode;)n=n.parentNode;for(n=n.querySelectorAll("input[name="+JSON.stringify(""+t)+'][type="radio"]'),t=0;t<n.length;t++){var r=n[t];if(r!==e&&r.form===e.form){var i=Fo(r);if(!i)throw Error(P(90));cp(r),wl(r,i)}}}break;case"textarea":hp(e,n);break;case"select":t=n.value,t!=null&&Er(e,!!n.multiple,t,!1)}};wp=wc;xp=Qn;var ew={usingClientEntryPoint:!1,Events:[ss,fr,Fo,vp,yp,wc]},oi={findFiberByHostInstance:zn,bundleType:0,version:"18.3.1",rendererPackageName:"react-dom"},tw={bundleType:oi.bundleType,version:oi.version,rendererPackageName:oi.rendererPackageName,rendererConfig:oi.rendererConfig,overrideHookState:null,overrideHookStateDeletePath:null,overrideHookStateRenamePath:null,overrideProps:null,overridePropsDeletePath:null,overridePropsRenamePath:null,setErrorHandler:null,setSuspenseHandler:null,scheduleUpdate:null,currentDispatcherRef:Bt.ReactCurrentDispatcher,findHostInstanceByFiber:function(e){return e=kp(e),e===null?null:e.stateNode},findFiberByHostInstance:oi.findFiberByHostInstance||X1,findHostInstancesForRefresh:null,scheduleRefresh:null,scheduleRoot:null,setRefreshHandler:null,getCurrentFiber:null,reconcilerVersion:"18.3.1-next-f1338f8080-20240426"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var As=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!As.isDisabled&&As.supportsFiber)try{$o=As.inject(tw),wt=As}catch{}}Ge.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED=ew;Ge.createPortal=function(e,t){var n=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!bc(t))throw Error(P(200));return Y1(e,t,null,n)};Ge.createRoot=function(e,t){if(!bc(e))throw Error(P(299));var n=!1,r="",i=tm;return t!=null&&(t.unstable_strictMode===!0&&(n=!0),t.identifierPrefix!==void 0&&(r=t.identifierPrefix),t.onRecoverableError!==void 0&&(i=t.onRecoverableError)),t=kc(e,1,!1,null,null,n,!1,r,i),e[Mt]=t.current,Ui(e.nodeType===8?e.parentNode:e),new Ec(t)};Ge.findDOMNode=function(e){if(e==null)return null;if(e.nodeType===1)return e;var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(P(188)):(e=Object.keys(e).join(","),Error(P(268,e)));return e=kp(t),e=e===null?null:e.stateNode,e};Ge.flushSync=function(e){return Qn(e)};Ge.hydrate=function(e,t,n){if(!Xo(t))throw Error(P(200));return Zo(null,e,t,!0,n)};Ge.hydrateRoot=function(e,t,n){if(!bc(e))throw Error(P(405));var r=n!=null&&n.hydratedSources||null,i=!1,s="",o=tm;if(n!=null&&(n.unstable_strictMode===!0&&(i=!0),n.identifierPrefix!==void 0&&(s=n.identifierPrefix),n.onRecoverableError!==void 0&&(o=n.onRecoverableError)),t=em(t,null,e,1,n??null,i,!1,s,o),e[Mt]=t.current,Ui(e),r)for(e=0;e<r.length;e++)n=r[e],i=n._getVersion,i=i(n._source),t.mutableSourceEagerHydrationData==null?t.mutableSourceEagerHydrationData=[n,i]:t.mutableSourceEagerHydrationData.push(n,i);return new Yo(t)};Ge.render=function(e,t,n){if(!Xo(t))throw Error(P(200));return Zo(null,e,t,!1,n)};Ge.unmountComponentAtNode=function(e){if(!Xo(e))throw Error(P(40));return e._reactRootContainer?(Qn(function(){Zo(null,null,e,!1,function(){e._reactRootContainer=null,e[Mt]=null})}),!0):!1};Ge.unstable_batchedUpdates=wc;Ge.unstable_renderSubtreeIntoContainer=function(e,t,n,r){if(!Xo(n))throw Error(P(200));if(e==null||e._reactInternals===void 0)throw Error(P(38));return Zo(e,t,n,!1,r)};Ge.version="18.3.1-next-f1338f8080-20240426";function nm(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(nm)}catch(e){console.error(e)}}nm(),np.exports=Ge;var nw=np.exports,fh=nw;hl.createRoot=fh.createRoot,hl.hydrateRoot=fh.hydrateRoot;const rw="modulepreload",iw=function(e){return"/"+e},ph={},$e=function(t,n,r){if(!n||n.length===0)return t();const i=document.getElementsByTagName("link");return Promise.all(n.map(s=>{if(s=iw(s),s in ph)return;ph[s]=!0;const o=s.endsWith(".css"),a=o?'[rel="stylesheet"]':"";if(!!r)for(let c=i.length-1;c>=0;c--){const d=i[c];if(d.href===s&&(!o||d.rel==="stylesheet"))return}else if(document.querySelector(`link[href="${s}"]${a}`))return;const u=document.createElement("link");if(u.rel=o?"stylesheet":rw,o||(u.as="script",u.crossOrigin=""),u.href=s,document.head.appendChild(u),o)return new Promise((c,d)=>{u.addEventListener("load",c),u.addEventListener("error",()=>d(new Error(`Unable to preload CSS for ${s}`)))})})).then(()=>t()).catch(s=>{const o=new Event("vite:preloadError",{cancelable:!0});if(o.payload=s,window.dispatchEvent(o),!o.defaultPrevented)throw s})};/**
 * @remix-run/router v1.23.0
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Yi(){return Yi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Yi.apply(this,arguments)}var nn;(function(e){e.Pop="POP",e.Push="PUSH",e.Replace="REPLACE"})(nn||(nn={}));const mh="popstate";function sw(e){e===void 0&&(e={});function t(r,i){let{pathname:s,search:o,hash:a}=r.location;return su("",{pathname:s,search:o,hash:a},i.state&&i.state.usr||null,i.state&&i.state.key||"default")}function n(r,i){return typeof i=="string"?i:Co(i)}return aw(t,n,null,e)}function ie(e,t){if(e===!1||e===null||typeof e>"u")throw new Error(t)}function rm(e,t){if(!e){typeof console<"u"&&console.warn(t);try{throw new Error(t)}catch{}}}function ow(){return Math.random().toString(36).substr(2,8)}function gh(e,t){return{usr:e.state,key:e.key,idx:t}}function su(e,t,n,r){return n===void 0&&(n=null),Yi({pathname:typeof e=="string"?e:e.pathname,search:"",hash:""},typeof t=="string"?qr(t):t,{state:n,key:t&&t.key||r||ow()})}function Co(e){let{pathname:t="/",search:n="",hash:r=""}=e;return n&&n!=="?"&&(t+=n.charAt(0)==="?"?n:"?"+n),r&&r!=="#"&&(t+=r.charAt(0)==="#"?r:"#"+r),t}function qr(e){let t={};if(e){let n=e.indexOf("#");n>=0&&(t.hash=e.substr(n),e=e.substr(0,n));let r=e.indexOf("?");r>=0&&(t.search=e.substr(r),e=e.substr(0,r)),e&&(t.pathname=e)}return t}function aw(e,t,n,r){r===void 0&&(r={});let{window:i=document.defaultView,v5Compat:s=!1}=r,o=i.history,a=nn.Pop,l=null,u=c();u==null&&(u=0,o.replaceState(Yi({},o.state,{idx:u}),""));function c(){return(o.state||{idx:null}).idx}function d(){a=nn.Pop;let x=c(),y=x==null?null:x-u;u=x,l&&l({action:a,location:w.location,delta:y})}function h(x,y){a=nn.Push;let p=su(w.location,x,y);n&&n(p,x),u=c()+1;let m=gh(p,u),S=w.createHref(p);try{o.pushState(m,"",S)}catch(k){if(k instanceof DOMException&&k.name==="DataCloneError")throw k;i.location.assign(S)}s&&l&&l({action:a,location:w.location,delta:1})}function f(x,y){a=nn.Replace;let p=su(w.location,x,y);n&&n(p,x),u=c();let m=gh(p,u),S=w.createHref(p);o.replaceState(m,"",S),s&&l&&l({action:a,location:w.location,delta:0})}function g(x){let y=i.location.origin!=="null"?i.location.origin:i.location.href,p=typeof x=="string"?x:Co(x);return p=p.replace(/ $/,"%20"),ie(y,"No window.location.(origin|href) available to create URL for href: "+p),new URL(p,y)}let w={get action(){return a},get location(){return e(i,o)},listen(x){if(l)throw new Error("A history only accepts one active listener");return i.addEventListener(mh,d),l=x,()=>{i.removeEventListener(mh,d),l=null}},createHref(x){return t(i,x)},createURL:g,encodeLocation(x){let y=g(x);return{pathname:y.pathname,search:y.search,hash:y.hash}},push:h,replace:f,go(x){return o.go(x)}};return w}var vh;(function(e){e.data="data",e.deferred="deferred",e.redirect="redirect",e.error="error"})(vh||(vh={}));function lw(e,t,n){return n===void 0&&(n="/"),uw(e,t,n,!1)}function uw(e,t,n,r){let i=typeof t=="string"?qr(t):t,s=Pc(i.pathname||"/",n);if(s==null)return null;let o=im(e);cw(o);let a=null;for(let l=0;a==null&&l<o.length;++l){let u=_w(s);a=ww(o[l],u,r)}return a}function im(e,t,n,r){t===void 0&&(t=[]),n===void 0&&(n=[]),r===void 0&&(r="");let i=(s,o,a)=>{let l={relativePath:a===void 0?s.path||"":a,caseSensitive:s.caseSensitive===!0,childrenIndex:o,route:s};l.relativePath.startsWith("/")&&(ie(l.relativePath.startsWith(r),'Absolute route path "'+l.relativePath+'" nested under path '+('"'+r+'" is not valid. An absolute child route path ')+"must start with the combined path of all its parent routes."),l.relativePath=l.relativePath.slice(r.length));let u=hn([r,l.relativePath]),c=n.concat(l);s.children&&s.children.length>0&&(ie(s.index!==!0,"Index routes must not have child routes. Please remove "+('all child routes from route path "'+u+'".')),im(s.children,t,c,u)),!(s.path==null&&!s.index)&&t.push({path:u,score:vw(u,s.index),routesMeta:c})};return e.forEach((s,o)=>{var a;if(s.path===""||!((a=s.path)!=null&&a.includes("?")))i(s,o);else for(let l of sm(s.path))i(s,o,l)}),t}function sm(e){let t=e.split("/");if(t.length===0)return[];let[n,...r]=t,i=n.endsWith("?"),s=n.replace(/\?$/,"");if(r.length===0)return i?[s,""]:[s];let o=sm(r.join("/")),a=[];return a.push(...o.map(l=>l===""?s:[s,l].join("/"))),i&&a.push(...o),a.map(l=>e.startsWith("/")&&l===""?"/":l)}function cw(e){e.sort((t,n)=>t.score!==n.score?n.score-t.score:yw(t.routesMeta.map(r=>r.childrenIndex),n.routesMeta.map(r=>r.childrenIndex)))}const dw=/^:[\w-]+$/,hw=3,fw=2,pw=1,mw=10,gw=-2,yh=e=>e==="*";function vw(e,t){let n=e.split("/"),r=n.length;return n.some(yh)&&(r+=gw),t&&(r+=fw),n.filter(i=>!yh(i)).reduce((i,s)=>i+(dw.test(s)?hw:s===""?pw:mw),r)}function yw(e,t){return e.length===t.length&&e.slice(0,-1).every((r,i)=>r===t[i])?e[e.length-1]-t[t.length-1]:0}function ww(e,t,n){n===void 0&&(n=!1);let{routesMeta:r}=e,i={},s="/",o=[];for(let a=0;a<r.length;++a){let l=r[a],u=a===r.length-1,c=s==="/"?t:t.slice(s.length)||"/",d=wh({path:l.relativePath,caseSensitive:l.caseSensitive,end:u},c),h=l.route;if(!d&&u&&n&&!r[r.length-1].route.index&&(d=wh({path:l.relativePath,caseSensitive:l.caseSensitive,end:!1},c)),!d)return null;Object.assign(i,d.params),o.push({params:i,pathname:hn([s,d.pathname]),pathnameBase:Ew(hn([s,d.pathnameBase])),route:h}),d.pathnameBase!=="/"&&(s=hn([s,d.pathnameBase]))}return o}function wh(e,t){typeof e=="string"&&(e={path:e,caseSensitive:!1,end:!0});let[n,r]=xw(e.path,e.caseSensitive,e.end),i=t.match(n);if(!i)return null;let s=i[0],o=s.replace(/(.)\/+$/,"$1"),a=i.slice(1);return{params:r.reduce((u,c,d)=>{let{paramName:h,isOptional:f}=c;if(h==="*"){let w=a[d]||"";o=s.slice(0,s.length-w.length).replace(/(.)\/+$/,"$1")}const g=a[d];return f&&!g?u[h]=void 0:u[h]=(g||"").replace(/%2F/g,"/"),u},{}),pathname:s,pathnameBase:o,pattern:e}}function xw(e,t,n){t===void 0&&(t=!1),n===void 0&&(n=!0),rm(e==="*"||!e.endsWith("*")||e.endsWith("/*"),'Route path "'+e+'" will be treated as if it were '+('"'+e.replace(/\*$/,"/*")+'" because the `*` character must ')+"always follow a `/` in the pattern. To get rid of this warning, "+('please change the route path to "'+e.replace(/\*$/,"/*")+'".'));let r=[],i="^"+e.replace(/\/*\*?$/,"").replace(/^\/*/,"/").replace(/[\\.*+^${}|()[\]]/g,"\\$&").replace(/\/:([\w-]+)(\?)?/g,(o,a,l)=>(r.push({paramName:a,isOptional:l!=null}),l?"/?([^\\/]+)?":"/([^\\/]+)"));return e.endsWith("*")?(r.push({paramName:"*"}),i+=e==="*"||e==="/*"?"(.*)$":"(?:\\/(.+)|\\/*)$"):n?i+="\\/*$":e!==""&&e!=="/"&&(i+="(?:(?=\\/|$))"),[new RegExp(i,t?void 0:"i"),r]}function _w(e){try{return e.split("/").map(t=>decodeURIComponent(t).replace(/\//g,"%2F")).join("/")}catch(t){return rm(!1,'The URL path "'+e+'" could not be decoded because it is is a malformed URL segment. This is probably due to a bad percent '+("encoding ("+t+").")),e}}function Pc(e,t){if(t==="/")return e;if(!e.toLowerCase().startsWith(t.toLowerCase()))return null;let n=t.endsWith("/")?t.length-1:t.length,r=e.charAt(n);return r&&r!=="/"?null:e.slice(n)||"/"}function Sw(e,t){t===void 0&&(t="/");let{pathname:n,search:r="",hash:i=""}=typeof e=="string"?qr(e):e;return{pathname:n?n.startsWith("/")?n:kw(n,t):t,search:bw(r),hash:Pw(i)}}function kw(e,t){let n=t.replace(/\/+$/,"").split("/");return e.split("/").forEach(i=>{i===".."?n.length>1&&n.pop():i!=="."&&n.push(i)}),n.length>1?n.join("/"):"/"}function Ga(e,t,n,r){return"Cannot include a '"+e+"' character in a manually specified "+("`to."+t+"` field ["+JSON.stringify(r)+"].  Please separate it out to the ")+("`to."+n+"` field. Alternatively you may provide the full path as ")+'a string in <Link to="..."> and the router will parse it for you.'}function Cw(e){return e.filter((t,n)=>n===0||t.route.path&&t.route.path.length>0)}function Tc(e,t){let n=Cw(e);return t?n.map((r,i)=>i===n.length-1?r.pathname:r.pathnameBase):n.map(r=>r.pathnameBase)}function jc(e,t,n,r){r===void 0&&(r=!1);let i;typeof e=="string"?i=qr(e):(i=Yi({},e),ie(!i.pathname||!i.pathname.includes("?"),Ga("?","pathname","search",i)),ie(!i.pathname||!i.pathname.includes("#"),Ga("#","pathname","hash",i)),ie(!i.search||!i.search.includes("#"),Ga("#","search","hash",i)));let s=e===""||i.pathname==="",o=s?"/":i.pathname,a;if(o==null)a=n;else{let d=t.length-1;if(!r&&o.startsWith("..")){let h=o.split("/");for(;h[0]==="..";)h.shift(),d-=1;i.pathname=h.join("/")}a=d>=0?t[d]:"/"}let l=Sw(i,a),u=o&&o!=="/"&&o.endsWith("/"),c=(s||o===".")&&n.endsWith("/");return!l.pathname.endsWith("/")&&(u||c)&&(l.pathname+="/"),l}const hn=e=>e.join("/").replace(/\/\/+/g,"/"),Ew=e=>e.replace(/\/+$/,"").replace(/^\/*/,"/"),bw=e=>!e||e==="?"?"":e.startsWith("?")?e:"?"+e,Pw=e=>!e||e==="#"?"":e.startsWith("#")?e:"#"+e;function Tw(e){return e!=null&&typeof e.status=="number"&&typeof e.statusText=="string"&&typeof e.internal=="boolean"&&"data"in e}const om=["post","put","patch","delete"];new Set(om);const jw=["get",...om];new Set(jw);/**
 * React Router v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function Xi(){return Xi=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},Xi.apply(this,arguments)}const Ac=_.createContext(null),Aw=_.createContext(null),kn=_.createContext(null),ea=_.createContext(null),Ut=_.createContext({outlet:null,matches:[],isDataRoute:!1}),am=_.createContext(null);function Rw(e,t){let{relative:n}=t===void 0?{}:t;Jr()||ie(!1);let{basename:r,navigator:i}=_.useContext(kn),{hash:s,pathname:o,search:a}=um(e,{relative:n}),l=o;return r!=="/"&&(l=o==="/"?r:hn([r,o])),i.createHref({pathname:l,search:a,hash:s})}function Jr(){return _.useContext(ea)!=null}function Cn(){return Jr()||ie(!1),_.useContext(ea).location}function lm(e){_.useContext(kn).static||_.useLayoutEffect(e)}function En(){let{isDataRoute:e}=_.useContext(Ut);return e?Hw():Lw()}function Lw(){Jr()||ie(!1);let e=_.useContext(Ac),{basename:t,future:n,navigator:r}=_.useContext(kn),{matches:i}=_.useContext(Ut),{pathname:s}=Cn(),o=JSON.stringify(Tc(i,n.v7_relativeSplatPath)),a=_.useRef(!1);return lm(()=>{a.current=!0}),_.useCallback(function(u,c){if(c===void 0&&(c={}),!a.current)return;if(typeof u=="number"){r.go(u);return}let d=jc(u,JSON.parse(o),s,c.relative==="path");e==null&&t!=="/"&&(d.pathname=d.pathname==="/"?t:hn([t,d.pathname])),(c.replace?r.replace:r.push)(d,c.state,c)},[t,r,o,s,e])}function Fb(){let{matches:e}=_.useContext(Ut),t=e[e.length-1];return t?t.params:{}}function um(e,t){let{relative:n}=t===void 0?{}:t,{future:r}=_.useContext(kn),{matches:i}=_.useContext(Ut),{pathname:s}=Cn(),o=JSON.stringify(Tc(i,r.v7_relativeSplatPath));return _.useMemo(()=>jc(e,JSON.parse(o),s,n==="path"),[e,o,s,n])}function Ow(e,t){return Iw(e,t)}function Iw(e,t,n,r){Jr()||ie(!1);let{navigator:i}=_.useContext(kn),{matches:s}=_.useContext(Ut),o=s[s.length-1],a=o?o.params:{};o&&o.pathname;let l=o?o.pathnameBase:"/";o&&o.route;let u=Cn(),c;if(t){var d;let x=typeof t=="string"?qr(t):t;l==="/"||(d=x.pathname)!=null&&d.startsWith(l)||ie(!1),c=x}else c=u;let h=c.pathname||"/",f=h;if(l!=="/"){let x=l.replace(/^\//,"").split("/");f="/"+h.replace(/^\//,"").split("/").slice(x.length).join("/")}let g=lw(e,{pathname:f}),w=$w(g&&g.map(x=>Object.assign({},x,{params:Object.assign({},a,x.params),pathname:hn([l,i.encodeLocation?i.encodeLocation(x.pathname).pathname:x.pathname]),pathnameBase:x.pathnameBase==="/"?l:hn([l,i.encodeLocation?i.encodeLocation(x.pathnameBase).pathname:x.pathnameBase])})),s,n,r);return t&&w?_.createElement(ea.Provider,{value:{location:Xi({pathname:"/",search:"",hash:"",state:null,key:"default"},c),navigationType:nn.Pop}},w):w}function Nw(){let e=Fw(),t=Tw(e)?e.status+" "+e.statusText:e instanceof Error?e.message:JSON.stringify(e),n=e instanceof Error?e.stack:null,i={padding:"0.5rem",backgroundColor:"rgba(200,200,200, 0.5)"},s=null;return _.createElement(_.Fragment,null,_.createElement("h2",null,"Unexpected Application Error!"),_.createElement("h3",{style:{fontStyle:"italic"}},t),n?_.createElement("pre",{style:i},n):null,s)}const Mw=_.createElement(Nw,null);class Dw extends _.Component{constructor(t){super(t),this.state={location:t.location,revalidation:t.revalidation,error:t.error}}static getDerivedStateFromError(t){return{error:t}}static getDerivedStateFromProps(t,n){return n.location!==t.location||n.revalidation!=="idle"&&t.revalidation==="idle"?{error:t.error,location:t.location,revalidation:t.revalidation}:{error:t.error!==void 0?t.error:n.error,location:n.location,revalidation:t.revalidation||n.revalidation}}componentDidCatch(t,n){console.error("React Router caught the following error during render",t,n)}render(){return this.state.error!==void 0?_.createElement(Ut.Provider,{value:this.props.routeContext},_.createElement(am.Provider,{value:this.state.error,children:this.props.component})):this.props.children}}function Vw(e){let{routeContext:t,match:n,children:r}=e,i=_.useContext(Ac);return i&&i.static&&i.staticContext&&(n.route.errorElement||n.route.ErrorBoundary)&&(i.staticContext._deepestRenderedBoundaryId=n.route.id),_.createElement(Ut.Provider,{value:t},r)}function $w(e,t,n,r){var i;if(t===void 0&&(t=[]),n===void 0&&(n=null),r===void 0&&(r=null),e==null){var s;if(!n)return null;if(n.errors)e=n.matches;else if((s=r)!=null&&s.v7_partialHydration&&t.length===0&&!n.initialized&&n.matches.length>0)e=n.matches;else return null}let o=e,a=(i=n)==null?void 0:i.errors;if(a!=null){let c=o.findIndex(d=>d.route.id&&(a==null?void 0:a[d.route.id])!==void 0);c>=0||ie(!1),o=o.slice(0,Math.min(o.length,c+1))}let l=!1,u=-1;if(n&&r&&r.v7_partialHydration)for(let c=0;c<o.length;c++){let d=o[c];if((d.route.HydrateFallback||d.route.hydrateFallbackElement)&&(u=c),d.route.id){let{loaderData:h,errors:f}=n,g=d.route.loader&&h[d.route.id]===void 0&&(!f||f[d.route.id]===void 0);if(d.route.lazy||g){l=!0,u>=0?o=o.slice(0,u+1):o=[o[0]];break}}}return o.reduceRight((c,d,h)=>{let f,g=!1,w=null,x=null;n&&(f=a&&d.route.id?a[d.route.id]:void 0,w=d.route.errorElement||Mw,l&&(u<0&&h===0?(Ww("route-fallback",!1),g=!0,x=null):u===h&&(g=!0,x=d.route.hydrateFallbackElement||null)));let y=t.concat(o.slice(0,h+1)),p=()=>{let m;return f?m=w:g?m=x:d.route.Component?m=_.createElement(d.route.Component,null):d.route.element?m=d.route.element:m=c,_.createElement(Vw,{match:d,routeContext:{outlet:c,matches:y,isDataRoute:n!=null},children:m})};return n&&(d.route.ErrorBoundary||d.route.errorElement||h===0)?_.createElement(Dw,{location:n.location,revalidation:n.revalidation,component:w,error:f,children:p(),routeContext:{outlet:null,matches:y,isDataRoute:!0}}):p()},null)}var cm=function(e){return e.UseBlocker="useBlocker",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e}(cm||{}),Eo=function(e){return e.UseBlocker="useBlocker",e.UseLoaderData="useLoaderData",e.UseActionData="useActionData",e.UseRouteError="useRouteError",e.UseNavigation="useNavigation",e.UseRouteLoaderData="useRouteLoaderData",e.UseMatches="useMatches",e.UseRevalidator="useRevalidator",e.UseNavigateStable="useNavigate",e.UseRouteId="useRouteId",e}(Eo||{});function zw(e){let t=_.useContext(Ac);return t||ie(!1),t}function Bw(e){let t=_.useContext(Aw);return t||ie(!1),t}function Uw(e){let t=_.useContext(Ut);return t||ie(!1),t}function dm(e){let t=Uw(),n=t.matches[t.matches.length-1];return n.route.id||ie(!1),n.route.id}function Fw(){var e;let t=_.useContext(am),n=Bw(Eo.UseRouteError),r=dm(Eo.UseRouteError);return t!==void 0?t:(e=n.errors)==null?void 0:e[r]}function Hw(){let{router:e}=zw(cm.UseNavigateStable),t=dm(Eo.UseNavigateStable),n=_.useRef(!1);return lm(()=>{n.current=!0}),_.useCallback(function(i,s){s===void 0&&(s={}),n.current&&(typeof i=="number"?e.navigate(i):e.navigate(i,Xi({fromRouteId:t},s)))},[e,t])}const xh={};function Ww(e,t,n){!t&&!xh[e]&&(xh[e]=!0)}function Kw(e,t){e==null||e.v7_startTransition,(e==null?void 0:e.v7_relativeSplatPath)===void 0&&(!t||t.v7_relativeSplatPath),t&&(t.v7_fetcherPersist,t.v7_normalizeFormMethod,t.v7_partialHydration,t.v7_skipActionErrorRevalidation)}function ou(e){let{to:t,replace:n,state:r,relative:i}=e;Jr()||ie(!1);let{future:s,static:o}=_.useContext(kn),{matches:a}=_.useContext(Ut),{pathname:l}=Cn(),u=En(),c=jc(t,Tc(a,s.v7_relativeSplatPath),l,i==="path"),d=JSON.stringify(c);return _.useEffect(()=>u(JSON.parse(d),{replace:n,state:r,relative:i}),[u,d,i,n,r]),null}function Re(e){ie(!1)}function Gw(e){let{basename:t="/",children:n=null,location:r,navigationType:i=nn.Pop,navigator:s,static:o=!1,future:a}=e;Jr()&&ie(!1);let l=t.replace(/^\/*/,"/"),u=_.useMemo(()=>({basename:l,navigator:s,static:o,future:Xi({v7_relativeSplatPath:!1},a)}),[l,a,s,o]);typeof r=="string"&&(r=qr(r));let{pathname:c="/",search:d="",hash:h="",state:f=null,key:g="default"}=r,w=_.useMemo(()=>{let x=Pc(c,l);return x==null?null:{location:{pathname:x,search:d,hash:h,state:f,key:g},navigationType:i}},[l,c,d,h,f,g,i]);return w==null?null:_.createElement(kn.Provider,{value:u},_.createElement(ea.Provider,{children:n,value:w}))}function qw(e){let{children:t,location:n}=e;return Ow(au(t),n)}new Promise(()=>{});function au(e,t){t===void 0&&(t=[]);let n=[];return _.Children.forEach(e,(r,i)=>{if(!_.isValidElement(r))return;let s=[...t,i];if(r.type===_.Fragment){n.push.apply(n,au(r.props.children,s));return}r.type!==Re&&ie(!1),!r.props.index||!r.props.children||ie(!1);let o={id:r.props.id||s.join("-"),caseSensitive:r.props.caseSensitive,element:r.props.element,Component:r.props.Component,index:r.props.index,path:r.props.path,loader:r.props.loader,action:r.props.action,errorElement:r.props.errorElement,ErrorBoundary:r.props.ErrorBoundary,hasErrorBoundary:r.props.ErrorBoundary!=null||r.props.errorElement!=null,shouldRevalidate:r.props.shouldRevalidate,handle:r.props.handle,lazy:r.props.lazy};r.props.children&&(o.children=au(r.props.children,s)),n.push(o)}),n}/**
 * React Router DOM v6.30.1
 *
 * Copyright (c) Remix Software Inc.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE.md file in the root directory of this source tree.
 *
 * @license MIT
 */function lu(){return lu=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e},lu.apply(this,arguments)}function Jw(e,t){if(e==null)return{};var n={},r=Object.keys(e),i,s;for(s=0;s<r.length;s++)i=r[s],!(t.indexOf(i)>=0)&&(n[i]=e[i]);return n}function Qw(e){return!!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)}function Yw(e,t){return e.button===0&&(!t||t==="_self")&&!Qw(e)}const Xw=["onClick","relative","reloadDocument","replace","state","target","to","preventScrollReset","viewTransition"],Zw="6";try{window.__reactRouterVersion=Zw}catch{}const ex="startTransition",_h=Wv[ex];function tx(e){let{basename:t,children:n,future:r,window:i}=e,s=_.useRef();s.current==null&&(s.current=sw({window:i,v5Compat:!0}));let o=s.current,[a,l]=_.useState({action:o.action,location:o.location}),{v7_startTransition:u}=r||{},c=_.useCallback(d=>{u&&_h?_h(()=>l(d)):l(d)},[l,u]);return _.useLayoutEffect(()=>o.listen(c),[o,c]),_.useEffect(()=>Kw(r),[r]),_.createElement(Gw,{basename:t,children:n,location:a.location,navigationType:a.action,navigator:o,future:r})}const nx=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u",rx=/^(?:[a-z][a-z0-9+.-]*:|\/\/)/i,hm=_.forwardRef(function(t,n){let{onClick:r,relative:i,reloadDocument:s,replace:o,state:a,target:l,to:u,preventScrollReset:c,viewTransition:d}=t,h=Jw(t,Xw),{basename:f}=_.useContext(kn),g,w=!1;if(typeof u=="string"&&rx.test(u)&&(g=u,nx))try{let m=new URL(window.location.href),S=u.startsWith("//")?new URL(m.protocol+u):new URL(u),k=Pc(S.pathname,f);S.origin===m.origin&&k!=null?u=k+S.search+S.hash:w=!0}catch{}let x=Rw(u,{relative:i}),y=ix(u,{replace:o,state:a,target:l,preventScrollReset:c,relative:i,viewTransition:d});function p(m){r&&r(m),m.defaultPrevented||y(m)}return _.createElement("a",lu({},h,{href:g||x,onClick:w||s?r:p,ref:n,target:l}))});var Sh;(function(e){e.UseScrollRestoration="useScrollRestoration",e.UseSubmit="useSubmit",e.UseSubmitFetcher="useSubmitFetcher",e.UseFetcher="useFetcher",e.useViewTransitionState="useViewTransitionState"})(Sh||(Sh={}));var kh;(function(e){e.UseFetcher="useFetcher",e.UseFetchers="useFetchers",e.UseScrollRestoration="useScrollRestoration"})(kh||(kh={}));function ix(e,t){let{target:n,replace:r,state:i,preventScrollReset:s,relative:o,viewTransition:a}=t===void 0?{}:t,l=En(),u=Cn(),c=um(e,{relative:o});return _.useCallback(d=>{if(Yw(d,n)){d.preventDefault();let h=r!==void 0?r:Co(u)===Co(c);l(e,{replace:h,state:i,preventScrollReset:s,relative:o,viewTransition:a})}},[u,l,c,r,i,n,e,s,o,a])}const Ch=e=>{let t;const n=new Set,r=(c,d)=>{const h=typeof c=="function"?c(t):c;if(!Object.is(h,t)){const f=t;t=d??(typeof h!="object"||h===null)?h:Object.assign({},t,h),n.forEach(g=>g(t,f))}},i=()=>t,l={setState:r,getState:i,getInitialState:()=>u,subscribe:c=>(n.add(c),()=>n.delete(c)),destroy:()=>{n.clear()}},u=t=e(r,i,l);return l},sx=e=>e?Ch(e):Ch;var fm={exports:{}},pm={},mm={exports:{}},gm={};/**
 * @license React
 * use-sync-external-store-shim.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var zr=_;function ox(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var ax=typeof Object.is=="function"?Object.is:ox,lx=zr.useState,ux=zr.useEffect,cx=zr.useLayoutEffect,dx=zr.useDebugValue;function hx(e,t){var n=t(),r=lx({inst:{value:n,getSnapshot:t}}),i=r[0].inst,s=r[1];return cx(function(){i.value=n,i.getSnapshot=t,qa(i)&&s({inst:i})},[e,n,t]),ux(function(){return qa(i)&&s({inst:i}),e(function(){qa(i)&&s({inst:i})})},[e]),dx(n),n}function qa(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!ax(e,n)}catch{return!0}}function fx(e,t){return t()}var px=typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"?fx:hx;gm.useSyncExternalStore=zr.useSyncExternalStore!==void 0?zr.useSyncExternalStore:px;mm.exports=gm;var mx=mm.exports;/**
 * @license React
 * use-sync-external-store-shim/with-selector.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ta=_,gx=mx;function vx(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var yx=typeof Object.is=="function"?Object.is:vx,wx=gx.useSyncExternalStore,xx=ta.useRef,_x=ta.useEffect,Sx=ta.useMemo,kx=ta.useDebugValue;pm.useSyncExternalStoreWithSelector=function(e,t,n,r,i){var s=xx(null);if(s.current===null){var o={hasValue:!1,value:null};s.current=o}else o=s.current;s=Sx(function(){function l(f){if(!u){if(u=!0,c=f,f=r(f),i!==void 0&&o.hasValue){var g=o.value;if(i(g,f))return d=g}return d=f}if(g=d,yx(c,f))return g;var w=r(f);return i!==void 0&&i(g,w)?(c=f,g):(c=f,d=w)}var u=!1,c,d,h=n===void 0?null:n;return[function(){return l(t())},h===null?void 0:function(){return l(h())}]},[t,n,r,i]);var a=wx(e,s[0],s[1]);return _x(function(){o.hasValue=!0,o.value=a},[a]),kx(a),a};fm.exports=pm;var Cx=fm.exports;const Ex=Hf(Cx),{useDebugValue:bx}=Fe,{useSyncExternalStoreWithSelector:Px}=Ex;const Tx=e=>e;function jx(e,t=Tx,n){const r=Px(e.subscribe,e.getState,e.getServerState||e.getInitialState,t,n);return bx(r),r}const Eh=e=>{const t=typeof e=="function"?sx(e):e,n=(r,i)=>jx(t,r,i);return Object.assign(n,t),n},Ax=e=>e?Eh(e):Eh,Rx=e=>{let t;return e?t=e:typeof fetch>"u"?t=(...n)=>$e(()=>Promise.resolve().then(()=>Qr),void 0).then(({default:r})=>r(...n)):t=fetch,(...n)=>t(...n)};class na extends Error{constructor(t,n="FunctionsError",r){super(t),this.name=n,this.context=r}}class vm extends na{constructor(t){super("Failed to send a request to the Edge Function","FunctionsFetchError",t)}}class ym extends na{constructor(t){super("Relay Error invoking the Edge Function","FunctionsRelayError",t)}}class wm extends na{constructor(t){super("Edge Function returned a non-2xx status code","FunctionsHttpError",t)}}var bo;(function(e){e.Any="any",e.ApNortheast1="ap-northeast-1",e.ApNortheast2="ap-northeast-2",e.ApSouth1="ap-south-1",e.ApSoutheast1="ap-southeast-1",e.ApSoutheast2="ap-southeast-2",e.CaCentral1="ca-central-1",e.EuCentral1="eu-central-1",e.EuWest1="eu-west-1",e.EuWest2="eu-west-2",e.EuWest3="eu-west-3",e.SaEast1="sa-east-1",e.UsEast1="us-east-1",e.UsWest1="us-west-1",e.UsWest2="us-west-2"})(bo||(bo={}));var Lx=globalThis&&globalThis.__awaiter||function(e,t,n,r){function i(s){return s instanceof n?s:new n(function(o){o(s)})}return new(n||(n=Promise))(function(s,o){function a(c){try{u(r.next(c))}catch(d){o(d)}}function l(c){try{u(r.throw(c))}catch(d){o(d)}}function u(c){c.done?s(c.value):i(c.value).then(a,l)}u((r=r.apply(e,t||[])).next())})};class Ox{constructor(t,{headers:n={},customFetch:r,region:i=bo.Any}={}){this.url=t,this.headers=n,this.region=i,this.fetch=Rx(r)}setAuth(t){this.headers.Authorization=`Bearer ${t}`}invoke(t,n={}){var r;return Lx(this,void 0,void 0,function*(){try{const{headers:i,method:s,body:o}=n;let a={},{region:l}=n;l||(l=this.region),l&&l!=="any"&&(a["x-region"]=l);let u;o&&(i&&!Object.prototype.hasOwnProperty.call(i,"Content-Type")||!i)&&(typeof Blob<"u"&&o instanceof Blob||o instanceof ArrayBuffer?(a["Content-Type"]="application/octet-stream",u=o):typeof o=="string"?(a["Content-Type"]="text/plain",u=o):typeof FormData<"u"&&o instanceof FormData?u=o:(a["Content-Type"]="application/json",u=JSON.stringify(o)));const c=yield this.fetch(`${this.url}/${t}`,{method:s||"POST",headers:Object.assign(Object.assign(Object.assign({},a),this.headers),i),body:u}).catch(g=>{throw new vm(g)}),d=c.headers.get("x-relay-error");if(d&&d==="true")throw new ym(c);if(!c.ok)throw new wm(c);let h=((r=c.headers.get("Content-Type"))!==null&&r!==void 0?r:"text/plain").split(";")[0].trim(),f;return h==="application/json"?f=yield c.json():h==="application/octet-stream"?f=yield c.blob():h==="text/event-stream"?f=c:h==="multipart/form-data"?f=yield c.formData():f=yield c.text(),{data:f,error:null}}catch(i){return{data:null,error:i}}})}}var Oe={},Rc={},ra={},as={},ia={},sa={},Ix=function(){if(typeof self<"u")return self;if(typeof window<"u")return window;if(typeof global<"u")return global;throw new Error("unable to locate global object")},Br=Ix();const Nx=Br.fetch,xm=Br.fetch.bind(Br),_m=Br.Headers,Mx=Br.Request,Dx=Br.Response,Qr=Object.freeze(Object.defineProperty({__proto__:null,Headers:_m,Request:Mx,Response:Dx,default:xm,fetch:Nx},Symbol.toStringTag,{value:"Module"})),Vx=jv(Qr);var oa={};Object.defineProperty(oa,"__esModule",{value:!0});let $x=class extends Error{constructor(t){super(t.message),this.name="PostgrestError",this.details=t.details,this.hint=t.hint,this.code=t.code}};oa.default=$x;var Sm=nt&&nt.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(sa,"__esModule",{value:!0});const zx=Sm(Vx),Bx=Sm(oa);let Ux=class{constructor(t){this.shouldThrowOnError=!1,this.method=t.method,this.url=t.url,this.headers=t.headers,this.schema=t.schema,this.body=t.body,this.shouldThrowOnError=t.shouldThrowOnError,this.signal=t.signal,this.isMaybeSingle=t.isMaybeSingle,t.fetch?this.fetch=t.fetch:typeof fetch>"u"?this.fetch=zx.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(t,n){return this.headers=Object.assign({},this.headers),this.headers[t]=n,this}then(t,n){this.schema===void 0||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),this.method!=="GET"&&this.method!=="HEAD"&&(this.headers["Content-Type"]="application/json");const r=this.fetch;let i=r(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(async s=>{var o,a,l;let u=null,c=null,d=null,h=s.status,f=s.statusText;if(s.ok){if(this.method!=="HEAD"){const y=await s.text();y===""||(this.headers.Accept==="text/csv"||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?c=y:c=JSON.parse(y))}const w=(o=this.headers.Prefer)===null||o===void 0?void 0:o.match(/count=(exact|planned|estimated)/),x=(a=s.headers.get("content-range"))===null||a===void 0?void 0:a.split("/");w&&x&&x.length>1&&(d=parseInt(x[1])),this.isMaybeSingle&&this.method==="GET"&&Array.isArray(c)&&(c.length>1?(u={code:"PGRST116",details:`Results contain ${c.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},c=null,d=null,h=406,f="Not Acceptable"):c.length===1?c=c[0]:c=null)}else{const w=await s.text();try{u=JSON.parse(w),Array.isArray(u)&&s.status===404&&(c=[],u=null,h=200,f="OK")}catch{s.status===404&&w===""?(h=204,f="No Content"):u={message:w}}if(u&&this.isMaybeSingle&&(!((l=u==null?void 0:u.details)===null||l===void 0)&&l.includes("0 rows"))&&(u=null,h=200,f="OK"),u&&this.shouldThrowOnError)throw new Bx.default(u)}return{error:u,data:c,count:d,status:h,statusText:f}});return this.shouldThrowOnError||(i=i.catch(s=>{var o,a,l;return{error:{message:`${(o=s==null?void 0:s.name)!==null&&o!==void 0?o:"FetchError"}: ${s==null?void 0:s.message}`,details:`${(a=s==null?void 0:s.stack)!==null&&a!==void 0?a:""}`,hint:"",code:`${(l=s==null?void 0:s.code)!==null&&l!==void 0?l:""}`},data:null,count:null,status:0,statusText:""}})),i.then(t,n)}returns(){return this}overrideTypes(){return this}};sa.default=Ux;var Fx=nt&&nt.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(ia,"__esModule",{value:!0});const Hx=Fx(sa);let Wx=class extends Hx.default{select(t){let n=!1;const r=(t??"*").split("").map(i=>/\s/.test(i)&&!n?"":(i==='"'&&(n=!n),i)).join("");return this.url.searchParams.set("select",r),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(t,{ascending:n=!0,nullsFirst:r,foreignTable:i,referencedTable:s=i}={}){const o=s?`${s}.order`:"order",a=this.url.searchParams.get(o);return this.url.searchParams.set(o,`${a?`${a},`:""}${t}.${n?"asc":"desc"}${r===void 0?"":r?".nullsfirst":".nullslast"}`),this}limit(t,{foreignTable:n,referencedTable:r=n}={}){const i=typeof r>"u"?"limit":`${r}.limit`;return this.url.searchParams.set(i,`${t}`),this}range(t,n,{foreignTable:r,referencedTable:i=r}={}){const s=typeof i>"u"?"offset":`${i}.offset`,o=typeof i>"u"?"limit":`${i}.limit`;return this.url.searchParams.set(s,`${t}`),this.url.searchParams.set(o,`${n-t+1}`),this}abortSignal(t){return this.signal=t,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return this.method==="GET"?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:t=!1,verbose:n=!1,settings:r=!1,buffers:i=!1,wal:s=!1,format:o="text"}={}){var a;const l=[t?"analyze":null,n?"verbose":null,r?"settings":null,i?"buffers":null,s?"wal":null].filter(Boolean).join("|"),u=(a=this.headers.Accept)!==null&&a!==void 0?a:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${o}; for="${u}"; options=${l};`,o==="json"?this:this}rollback(){var t;return((t=this.headers.Prefer)!==null&&t!==void 0?t:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}};ia.default=Wx;var Kx=nt&&nt.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(as,"__esModule",{value:!0});const Gx=Kx(ia);let qx=class extends Gx.default{eq(t,n){return this.url.searchParams.append(t,`eq.${n}`),this}neq(t,n){return this.url.searchParams.append(t,`neq.${n}`),this}gt(t,n){return this.url.searchParams.append(t,`gt.${n}`),this}gte(t,n){return this.url.searchParams.append(t,`gte.${n}`),this}lt(t,n){return this.url.searchParams.append(t,`lt.${n}`),this}lte(t,n){return this.url.searchParams.append(t,`lte.${n}`),this}like(t,n){return this.url.searchParams.append(t,`like.${n}`),this}likeAllOf(t,n){return this.url.searchParams.append(t,`like(all).{${n.join(",")}}`),this}likeAnyOf(t,n){return this.url.searchParams.append(t,`like(any).{${n.join(",")}}`),this}ilike(t,n){return this.url.searchParams.append(t,`ilike.${n}`),this}ilikeAllOf(t,n){return this.url.searchParams.append(t,`ilike(all).{${n.join(",")}}`),this}ilikeAnyOf(t,n){return this.url.searchParams.append(t,`ilike(any).{${n.join(",")}}`),this}is(t,n){return this.url.searchParams.append(t,`is.${n}`),this}in(t,n){const r=Array.from(new Set(n)).map(i=>typeof i=="string"&&new RegExp("[,()]").test(i)?`"${i}"`:`${i}`).join(",");return this.url.searchParams.append(t,`in.(${r})`),this}contains(t,n){return typeof n=="string"?this.url.searchParams.append(t,`cs.${n}`):Array.isArray(n)?this.url.searchParams.append(t,`cs.{${n.join(",")}}`):this.url.searchParams.append(t,`cs.${JSON.stringify(n)}`),this}containedBy(t,n){return typeof n=="string"?this.url.searchParams.append(t,`cd.${n}`):Array.isArray(n)?this.url.searchParams.append(t,`cd.{${n.join(",")}}`):this.url.searchParams.append(t,`cd.${JSON.stringify(n)}`),this}rangeGt(t,n){return this.url.searchParams.append(t,`sr.${n}`),this}rangeGte(t,n){return this.url.searchParams.append(t,`nxl.${n}`),this}rangeLt(t,n){return this.url.searchParams.append(t,`sl.${n}`),this}rangeLte(t,n){return this.url.searchParams.append(t,`nxr.${n}`),this}rangeAdjacent(t,n){return this.url.searchParams.append(t,`adj.${n}`),this}overlaps(t,n){return typeof n=="string"?this.url.searchParams.append(t,`ov.${n}`):this.url.searchParams.append(t,`ov.{${n.join(",")}}`),this}textSearch(t,n,{config:r,type:i}={}){let s="";i==="plain"?s="pl":i==="phrase"?s="ph":i==="websearch"&&(s="w");const o=r===void 0?"":`(${r})`;return this.url.searchParams.append(t,`${s}fts${o}.${n}`),this}match(t){return Object.entries(t).forEach(([n,r])=>{this.url.searchParams.append(n,`eq.${r}`)}),this}not(t,n,r){return this.url.searchParams.append(t,`not.${n}.${r}`),this}or(t,{foreignTable:n,referencedTable:r=n}={}){const i=r?`${r}.or`:"or";return this.url.searchParams.append(i,`(${t})`),this}filter(t,n,r){return this.url.searchParams.append(t,`${n}.${r}`),this}};as.default=qx;var Jx=nt&&nt.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(ra,"__esModule",{value:!0});const ai=Jx(as);let Qx=class{constructor(t,{headers:n={},schema:r,fetch:i}){this.url=t,this.headers=n,this.schema=r,this.fetch=i}select(t,{head:n=!1,count:r}={}){const i=n?"HEAD":"GET";let s=!1;const o=(t??"*").split("").map(a=>/\s/.test(a)&&!s?"":(a==='"'&&(s=!s),a)).join("");return this.url.searchParams.set("select",o),r&&(this.headers.Prefer=`count=${r}`),new ai.default({method:i,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(t,{count:n,defaultToNull:r=!0}={}){const i="POST",s=[];if(this.headers.Prefer&&s.push(this.headers.Prefer),n&&s.push(`count=${n}`),r||s.push("missing=default"),this.headers.Prefer=s.join(","),Array.isArray(t)){const o=t.reduce((a,l)=>a.concat(Object.keys(l)),[]);if(o.length>0){const a=[...new Set(o)].map(l=>`"${l}"`);this.url.searchParams.set("columns",a.join(","))}}return new ai.default({method:i,url:this.url,headers:this.headers,schema:this.schema,body:t,fetch:this.fetch,allowEmpty:!1})}upsert(t,{onConflict:n,ignoreDuplicates:r=!1,count:i,defaultToNull:s=!0}={}){const o="POST",a=[`resolution=${r?"ignore":"merge"}-duplicates`];if(n!==void 0&&this.url.searchParams.set("on_conflict",n),this.headers.Prefer&&a.push(this.headers.Prefer),i&&a.push(`count=${i}`),s||a.push("missing=default"),this.headers.Prefer=a.join(","),Array.isArray(t)){const l=t.reduce((u,c)=>u.concat(Object.keys(c)),[]);if(l.length>0){const u=[...new Set(l)].map(c=>`"${c}"`);this.url.searchParams.set("columns",u.join(","))}}return new ai.default({method:o,url:this.url,headers:this.headers,schema:this.schema,body:t,fetch:this.fetch,allowEmpty:!1})}update(t,{count:n}={}){const r="PATCH",i=[];return this.headers.Prefer&&i.push(this.headers.Prefer),n&&i.push(`count=${n}`),this.headers.Prefer=i.join(","),new ai.default({method:r,url:this.url,headers:this.headers,schema:this.schema,body:t,fetch:this.fetch,allowEmpty:!1})}delete({count:t}={}){const n="DELETE",r=[];return t&&r.push(`count=${t}`),this.headers.Prefer&&r.unshift(this.headers.Prefer),this.headers.Prefer=r.join(","),new ai.default({method:n,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}};ra.default=Qx;var aa={},la={};Object.defineProperty(la,"__esModule",{value:!0});la.version=void 0;la.version="0.0.0-automated";Object.defineProperty(aa,"__esModule",{value:!0});aa.DEFAULT_HEADERS=void 0;const Yx=la;aa.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${Yx.version}`};var km=nt&&nt.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Rc,"__esModule",{value:!0});const Xx=km(ra),Zx=km(as),e_=aa;let t_=class Cm{constructor(t,{headers:n={},schema:r,fetch:i}={}){this.url=t,this.headers=Object.assign(Object.assign({},e_.DEFAULT_HEADERS),n),this.schemaName=r,this.fetch=i}from(t){const n=new URL(`${this.url}/${t}`);return new Xx.default(n,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(t){return new Cm(this.url,{headers:this.headers,schema:t,fetch:this.fetch})}rpc(t,n={},{head:r=!1,get:i=!1,count:s}={}){let o;const a=new URL(`${this.url}/rpc/${t}`);let l;r||i?(o=r?"HEAD":"GET",Object.entries(n).filter(([c,d])=>d!==void 0).map(([c,d])=>[c,Array.isArray(d)?`{${d.join(",")}}`:`${d}`]).forEach(([c,d])=>{a.searchParams.append(c,d)})):(o="POST",l=n);const u=Object.assign({},this.headers);return s&&(u.Prefer=`count=${s}`),new Zx.default({method:o,url:a,headers:u,schema:this.schemaName,body:l,fetch:this.fetch,allowEmpty:!1})}};Rc.default=t_;var Yr=nt&&nt.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Oe,"__esModule",{value:!0});Oe.PostgrestError=Oe.PostgrestBuilder=Oe.PostgrestTransformBuilder=Oe.PostgrestFilterBuilder=Oe.PostgrestQueryBuilder=Oe.PostgrestClient=void 0;const Em=Yr(Rc);Oe.PostgrestClient=Em.default;const bm=Yr(ra);Oe.PostgrestQueryBuilder=bm.default;const Pm=Yr(as);Oe.PostgrestFilterBuilder=Pm.default;const Tm=Yr(ia);Oe.PostgrestTransformBuilder=Tm.default;const jm=Yr(sa);Oe.PostgrestBuilder=jm.default;const Am=Yr(oa);Oe.PostgrestError=Am.default;var n_=Oe.default={PostgrestClient:Em.default,PostgrestQueryBuilder:bm.default,PostgrestFilterBuilder:Pm.default,PostgrestTransformBuilder:Tm.default,PostgrestBuilder:jm.default,PostgrestError:Am.default};const{PostgrestClient:r_,PostgrestQueryBuilder:Jb,PostgrestFilterBuilder:Qb,PostgrestTransformBuilder:Yb,PostgrestBuilder:Xb,PostgrestError:i_}=n_;function s_(){if(typeof WebSocket<"u")return WebSocket;if(typeof global.WebSocket<"u")return global.WebSocket;if(typeof window.WebSocket<"u")return window.WebSocket;if(typeof self.WebSocket<"u")return self.WebSocket;throw new Error("`WebSocket` is not supported in this environment")}const o_=s_(),a_="2.11.15",l_=`realtime-js/${a_}`,u_="1.0.0",Rm=1e4,c_=1e3;var Ti;(function(e){e[e.connecting=0]="connecting",e[e.open=1]="open",e[e.closing=2]="closing",e[e.closed=3]="closed"})(Ti||(Ti={}));var me;(function(e){e.closed="closed",e.errored="errored",e.joined="joined",e.joining="joining",e.leaving="leaving"})(me||(me={}));var ut;(function(e){e.close="phx_close",e.error="phx_error",e.join="phx_join",e.reply="phx_reply",e.leave="phx_leave",e.access_token="access_token"})(ut||(ut={}));var uu;(function(e){e.websocket="websocket"})(uu||(uu={}));var $n;(function(e){e.Connecting="connecting",e.Open="open",e.Closing="closing",e.Closed="closed"})($n||($n={}));class d_{constructor(){this.HEADER_LENGTH=1}decode(t,n){return t.constructor===ArrayBuffer?n(this._binaryDecode(t)):n(typeof t=="string"?JSON.parse(t):{})}_binaryDecode(t){const n=new DataView(t),r=new TextDecoder;return this._decodeBroadcast(t,n,r)}_decodeBroadcast(t,n,r){const i=n.getUint8(1),s=n.getUint8(2);let o=this.HEADER_LENGTH+2;const a=r.decode(t.slice(o,o+i));o=o+i;const l=r.decode(t.slice(o,o+s));o=o+s;const u=JSON.parse(r.decode(t.slice(o,t.byteLength)));return{ref:null,topic:a,event:l,payload:u}}}class Lm{constructor(t,n){this.callback=t,this.timerCalc=n,this.timer=void 0,this.tries=0,this.callback=t,this.timerCalc=n}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}var U;(function(e){e.abstime="abstime",e.bool="bool",e.date="date",e.daterange="daterange",e.float4="float4",e.float8="float8",e.int2="int2",e.int4="int4",e.int4range="int4range",e.int8="int8",e.int8range="int8range",e.json="json",e.jsonb="jsonb",e.money="money",e.numeric="numeric",e.oid="oid",e.reltime="reltime",e.text="text",e.time="time",e.timestamp="timestamp",e.timestamptz="timestamptz",e.timetz="timetz",e.tsrange="tsrange",e.tstzrange="tstzrange"})(U||(U={}));const bh=(e,t,n={})=>{var r;const i=(r=n.skipTypes)!==null&&r!==void 0?r:[];return Object.keys(t).reduce((s,o)=>(s[o]=h_(o,e,t,i),s),{})},h_=(e,t,n,r)=>{const i=t.find(a=>a.name===e),s=i==null?void 0:i.type,o=n[e];return s&&!r.includes(s)?Om(s,o):cu(o)},Om=(e,t)=>{if(e.charAt(0)==="_"){const n=e.slice(1,e.length);return g_(t,n)}switch(e){case U.bool:return f_(t);case U.float4:case U.float8:case U.int2:case U.int4:case U.int8:case U.numeric:case U.oid:return p_(t);case U.json:case U.jsonb:return m_(t);case U.timestamp:return v_(t);case U.abstime:case U.date:case U.daterange:case U.int4range:case U.int8range:case U.money:case U.reltime:case U.text:case U.time:case U.timestamptz:case U.timetz:case U.tsrange:case U.tstzrange:return cu(t);default:return cu(t)}},cu=e=>e,f_=e=>{switch(e){case"t":return!0;case"f":return!1;default:return e}},p_=e=>{if(typeof e=="string"){const t=parseFloat(e);if(!Number.isNaN(t))return t}return e},m_=e=>{if(typeof e=="string")try{return JSON.parse(e)}catch(t){return console.log(`JSON parse error: ${t}`),e}return e},g_=(e,t)=>{if(typeof e!="string")return e;const n=e.length-1,r=e[n];if(e[0]==="{"&&r==="}"){let s;const o=e.slice(1,n);try{s=JSON.parse("["+o+"]")}catch{s=o?o.split(","):[]}return s.map(a=>Om(t,a))}return e},v_=e=>typeof e=="string"?e.replace(" ","T"):e,Im=e=>{let t=e;return t=t.replace(/^ws/i,"http"),t=t.replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,""),t.replace(/\/+$/,"")};class Ja{constructor(t,n,r={},i=Rm){this.channel=t,this.event=n,this.payload=r,this.timeout=i,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(t){this.timeout=t,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(t){this.payload=Object.assign(Object.assign({},this.payload),t)}receive(t,n){var r;return this._hasReceived(t)&&n((r=this.receivedResp)===null||r===void 0?void 0:r.response),this.recHooks.push({status:t,callback:n}),this}startTimeout(){if(this.timeoutTimer)return;this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref);const t=n=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=n,this._matchReceive(n)};this.channel._on(this.refEvent,{},t),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout)}trigger(t,n){this.refEvent&&this.channel._trigger(this.refEvent,{status:t,response:n})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:t,response:n}){this.recHooks.filter(r=>r.status===t).forEach(r=>r.callback(n))}_hasReceived(t){return this.receivedResp&&this.receivedResp.status===t}}var du;(function(e){e.SYNC="sync",e.JOIN="join",e.LEAVE="leave"})(du||(du={}));class Rr{constructor(t,n){this.channel=t,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};const r=(n==null?void 0:n.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(r.state,{},i=>{const{onJoin:s,onLeave:o,onSync:a}=this.caller;this.joinRef=this.channel._joinRef(),this.state=Rr.syncState(this.state,i,s,o),this.pendingDiffs.forEach(l=>{this.state=Rr.syncDiff(this.state,l,s,o)}),this.pendingDiffs=[],a()}),this.channel._on(r.diff,{},i=>{const{onJoin:s,onLeave:o,onSync:a}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(i):(this.state=Rr.syncDiff(this.state,i,s,o),a())}),this.onJoin((i,s,o)=>{this.channel._trigger("presence",{event:"join",key:i,currentPresences:s,newPresences:o})}),this.onLeave((i,s,o)=>{this.channel._trigger("presence",{event:"leave",key:i,currentPresences:s,leftPresences:o})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(t,n,r,i){const s=this.cloneDeep(t),o=this.transformState(n),a={},l={};return this.map(s,(u,c)=>{o[u]||(l[u]=c)}),this.map(o,(u,c)=>{const d=s[u];if(d){const h=c.map(x=>x.presence_ref),f=d.map(x=>x.presence_ref),g=c.filter(x=>f.indexOf(x.presence_ref)<0),w=d.filter(x=>h.indexOf(x.presence_ref)<0);g.length>0&&(a[u]=g),w.length>0&&(l[u]=w)}else a[u]=c}),this.syncDiff(s,{joins:a,leaves:l},r,i)}static syncDiff(t,n,r,i){const{joins:s,leaves:o}={joins:this.transformState(n.joins),leaves:this.transformState(n.leaves)};return r||(r=()=>{}),i||(i=()=>{}),this.map(s,(a,l)=>{var u;const c=(u=t[a])!==null&&u!==void 0?u:[];if(t[a]=this.cloneDeep(l),c.length>0){const d=t[a].map(f=>f.presence_ref),h=c.filter(f=>d.indexOf(f.presence_ref)<0);t[a].unshift(...h)}r(a,c,l)}),this.map(o,(a,l)=>{let u=t[a];if(!u)return;const c=l.map(d=>d.presence_ref);u=u.filter(d=>c.indexOf(d.presence_ref)<0),t[a]=u,i(a,u,l),u.length===0&&delete t[a]}),t}static map(t,n){return Object.getOwnPropertyNames(t).map(r=>n(r,t[r]))}static transformState(t){return t=this.cloneDeep(t),Object.getOwnPropertyNames(t).reduce((n,r)=>{const i=t[r];return"metas"in i?n[r]=i.metas.map(s=>(s.presence_ref=s.phx_ref,delete s.phx_ref,delete s.phx_ref_prev,s)):n[r]=i,n},{})}static cloneDeep(t){return JSON.parse(JSON.stringify(t))}onJoin(t){this.caller.onJoin=t}onLeave(t){this.caller.onLeave=t}onSync(t){this.caller.onSync=t}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}var hu;(function(e){e.ALL="*",e.INSERT="INSERT",e.UPDATE="UPDATE",e.DELETE="DELETE"})(hu||(hu={}));var fu;(function(e){e.BROADCAST="broadcast",e.PRESENCE="presence",e.POSTGRES_CHANGES="postgres_changes",e.SYSTEM="system"})(fu||(fu={}));var vt;(function(e){e.SUBSCRIBED="SUBSCRIBED",e.TIMED_OUT="TIMED_OUT",e.CLOSED="CLOSED",e.CHANNEL_ERROR="CHANNEL_ERROR"})(vt||(vt={}));const y_=me;class ua{constructor(t,n={config:{}},r){this.topic=t,this.params=n,this.socket=r,this.bindings={},this.state=me.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=t.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},n.config),this.timeout=this.socket.timeout,this.joinPush=new Ja(this,ut.join,this.params,this.timeout),this.rejoinTimer=new Lm(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=me.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(i=>i.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=me.closed,this.socket._remove(this)}),this._onError(i=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,i),this.state=me.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=me.errored,this.rejoinTimer.scheduleTimeout())}),this._on(ut.reply,{},(i,s)=>{this._trigger(this._replyEventName(s),i)}),this.presence=new Rr(this),this.broadcastEndpointURL=Im(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(t,n=this.timeout){var r,i;if(this.socket.isConnected()||this.socket.connect(),this.state==me.closed){const{config:{broadcast:s,presence:o,private:a}}=this.params;this._onError(c=>t==null?void 0:t(vt.CHANNEL_ERROR,c)),this._onClose(()=>t==null?void 0:t(vt.CLOSED));const l={},u={broadcast:s,presence:o,postgres_changes:(i=(r=this.bindings.postgres_changes)===null||r===void 0?void 0:r.map(c=>c.filter))!==null&&i!==void 0?i:[],private:a};this.socket.accessTokenValue&&(l.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:u},l)),this.joinedOnce=!0,this._rejoin(n),this.joinPush.receive("ok",async({postgres_changes:c})=>{var d;if(this.socket.setAuth(),c===void 0){t==null||t(vt.SUBSCRIBED);return}else{const h=this.bindings.postgres_changes,f=(d=h==null?void 0:h.length)!==null&&d!==void 0?d:0,g=[];for(let w=0;w<f;w++){const x=h[w],{filter:{event:y,schema:p,table:m,filter:S}}=x,k=c&&c[w];if(k&&k.event===y&&k.schema===p&&k.table===m&&k.filter===S)g.push(Object.assign(Object.assign({},x),{id:k.id}));else{this.unsubscribe(),this.state=me.errored,t==null||t(vt.CHANNEL_ERROR,new Error("mismatch between server and client bindings for postgres changes"));return}}this.bindings.postgres_changes=g,t&&t(vt.SUBSCRIBED);return}}).receive("error",c=>{this.state=me.errored,t==null||t(vt.CHANNEL_ERROR,new Error(JSON.stringify(Object.values(c).join(", ")||"error")))}).receive("timeout",()=>{t==null||t(vt.TIMED_OUT)})}return this}presenceState(){return this.presence.state}async track(t,n={}){return await this.send({type:"presence",event:"track",payload:t},n.timeout||this.timeout)}async untrack(t={}){return await this.send({type:"presence",event:"untrack"},t)}on(t,n,r){return this._on(t,n,r)}async send(t,n={}){var r,i;if(!this._canPush()&&t.type==="broadcast"){const{event:s,payload:o}=t,l={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:s,payload:o,private:this.private}]})};try{const u=await this._fetchWithTimeout(this.broadcastEndpointURL,l,(r=n.timeout)!==null&&r!==void 0?r:this.timeout);return await((i=u.body)===null||i===void 0?void 0:i.cancel()),u.ok?"ok":"error"}catch(u){return u.name==="AbortError"?"timed out":"error"}}else return new Promise(s=>{var o,a,l;const u=this._push(t.type,t,n.timeout||this.timeout);t.type==="broadcast"&&!(!((l=(a=(o=this.params)===null||o===void 0?void 0:o.config)===null||a===void 0?void 0:a.broadcast)===null||l===void 0)&&l.ack)&&s("ok"),u.receive("ok",()=>s("ok")),u.receive("error",()=>s("error")),u.receive("timeout",()=>s("timed out"))})}updateJoinPayload(t){this.joinPush.updatePayload(t)}unsubscribe(t=this.timeout){this.state=me.leaving;const n=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(ut.close,"leave",this._joinRef())};this.joinPush.destroy();let r=null;return new Promise(i=>{r=new Ja(this,ut.leave,{},t),r.receive("ok",()=>{n(),i("ok")}).receive("timeout",()=>{n(),i("timed out")}).receive("error",()=>{i("error")}),r.send(),this._canPush()||r.trigger("ok",{})}).finally(()=>{r==null||r.destroy()})}teardown(){this.pushBuffer.forEach(t=>t.destroy()),this.rejoinTimer&&clearTimeout(this.rejoinTimer.timer),this.joinPush.destroy()}async _fetchWithTimeout(t,n,r){const i=new AbortController,s=setTimeout(()=>i.abort(),r),o=await this.socket.fetch(t,Object.assign(Object.assign({},n),{signal:i.signal}));return clearTimeout(s),o}_push(t,n,r=this.timeout){if(!this.joinedOnce)throw`tried to push '${t}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let i=new Ja(this,t,n,r);return this._canPush()?i.send():(i.startTimeout(),this.pushBuffer.push(i)),i}_onMessage(t,n,r){return n}_isMember(t){return this.topic===t}_joinRef(){return this.joinPush.ref}_trigger(t,n,r){var i,s;const o=t.toLocaleLowerCase(),{close:a,error:l,leave:u,join:c}=ut;if(r&&[a,l,u,c].indexOf(o)>=0&&r!==this._joinRef())return;let h=this._onMessage(o,n,r);if(n&&!h)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(o)?(i=this.bindings.postgres_changes)===null||i===void 0||i.filter(f=>{var g,w,x;return((g=f.filter)===null||g===void 0?void 0:g.event)==="*"||((x=(w=f.filter)===null||w===void 0?void 0:w.event)===null||x===void 0?void 0:x.toLocaleLowerCase())===o}).map(f=>f.callback(h,r)):(s=this.bindings[o])===null||s===void 0||s.filter(f=>{var g,w,x,y,p,m;if(["broadcast","presence","postgres_changes"].includes(o))if("id"in f){const S=f.id,k=(g=f.filter)===null||g===void 0?void 0:g.event;return S&&((w=n.ids)===null||w===void 0?void 0:w.includes(S))&&(k==="*"||(k==null?void 0:k.toLocaleLowerCase())===((x=n.data)===null||x===void 0?void 0:x.type.toLocaleLowerCase()))}else{const S=(p=(y=f==null?void 0:f.filter)===null||y===void 0?void 0:y.event)===null||p===void 0?void 0:p.toLocaleLowerCase();return S==="*"||S===((m=n==null?void 0:n.event)===null||m===void 0?void 0:m.toLocaleLowerCase())}else return f.type.toLocaleLowerCase()===o}).map(f=>{if(typeof h=="object"&&"ids"in h){const g=h.data,{schema:w,table:x,commit_timestamp:y,type:p,errors:m}=g;h=Object.assign(Object.assign({},{schema:w,table:x,commit_timestamp:y,eventType:p,new:{},old:{},errors:m}),this._getPayloadRecords(g))}f.callback(h,r)})}_isClosed(){return this.state===me.closed}_isJoined(){return this.state===me.joined}_isJoining(){return this.state===me.joining}_isLeaving(){return this.state===me.leaving}_replyEventName(t){return`chan_reply_${t}`}_on(t,n,r){const i=t.toLocaleLowerCase(),s={type:i,filter:n,callback:r};return this.bindings[i]?this.bindings[i].push(s):this.bindings[i]=[s],this}_off(t,n){const r=t.toLocaleLowerCase();return this.bindings[r]=this.bindings[r].filter(i=>{var s;return!(((s=i.type)===null||s===void 0?void 0:s.toLocaleLowerCase())===r&&ua.isEqual(i.filter,n))}),this}static isEqual(t,n){if(Object.keys(t).length!==Object.keys(n).length)return!1;for(const r in t)if(t[r]!==n[r])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(t){this._on(ut.close,{},t)}_onError(t){this._on(ut.error,{},n=>t(n))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(t=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=me.joining,this.joinPush.resend(t))}_getPayloadRecords(t){const n={new:{},old:{}};return(t.type==="INSERT"||t.type==="UPDATE")&&(n.new=bh(t.columns,t.record)),(t.type==="UPDATE"||t.type==="DELETE")&&(n.old=bh(t.columns,t.old_record)),n}}const Ph=()=>{},w_=`
  addEventListener("message", (e) => {
    if (e.data.event === "start") {
      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);
    }
  });`;class Nm{constructor(t,n){var r;this.accessTokenValue=null,this.apiKey=null,this.channels=new Array,this.endPoint="",this.httpEndpoint="",this.headers={},this.params={},this.timeout=Rm,this.heartbeatIntervalMs=25e3,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.heartbeatCallback=Ph,this.ref=0,this.logger=Ph,this.conn=null,this.sendBuffer=[],this.serializer=new d_,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=s=>{let o;return s?o=s:typeof fetch>"u"?o=(...a)=>$e(()=>Promise.resolve().then(()=>Qr),void 0).then(({default:l})=>l(...a)):o=fetch,(...a)=>o(...a)},this.endPoint=`${t}/${uu.websocket}`,this.httpEndpoint=Im(t),n!=null&&n.transport?this.transport=n.transport:this.transport=null,n!=null&&n.params&&(this.params=n.params),n!=null&&n.timeout&&(this.timeout=n.timeout),n!=null&&n.logger&&(this.logger=n.logger),(n!=null&&n.logLevel||n!=null&&n.log_level)&&(this.logLevel=n.logLevel||n.log_level,this.params=Object.assign(Object.assign({},this.params),{log_level:this.logLevel})),n!=null&&n.heartbeatIntervalMs&&(this.heartbeatIntervalMs=n.heartbeatIntervalMs);const i=(r=n==null?void 0:n.params)===null||r===void 0?void 0:r.apikey;if(i&&(this.accessTokenValue=i,this.apiKey=i),this.reconnectAfterMs=n!=null&&n.reconnectAfterMs?n.reconnectAfterMs:s=>[1e3,2e3,5e3,1e4][s-1]||1e4,this.encode=n!=null&&n.encode?n.encode:(s,o)=>o(JSON.stringify(s)),this.decode=n!=null&&n.decode?n.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new Lm(async()=>{this.disconnect(),this.connect()},this.reconnectAfterMs),this.fetch=this._resolveFetch(n==null?void 0:n.fetch),n!=null&&n.worker){if(typeof window<"u"&&!window.Worker)throw new Error("Web Worker is not supported");this.worker=(n==null?void 0:n.worker)||!1,this.workerUrl=n==null?void 0:n.workerUrl}this.accessToken=(n==null?void 0:n.accessToken)||null}connect(){if(!this.conn){if(this.transport||(this.transport=o_),!this.transport)throw new Error("No transport provided");this.conn=new this.transport(this.endpointURL()),this.setupConnection()}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:u_}))}disconnect(t,n){this.conn&&(this.conn.onclose=function(){},t?this.conn.close(t,n??""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset(),this.channels.forEach(r=>r.teardown()))}getChannels(){return this.channels}async removeChannel(t){const n=await t.unsubscribe();return this.channels.length===0&&this.disconnect(),n}async removeAllChannels(){const t=await Promise.all(this.channels.map(n=>n.unsubscribe()));return this.channels=[],this.disconnect(),t}log(t,n,r){this.logger(t,n,r)}connectionState(){switch(this.conn&&this.conn.readyState){case Ti.connecting:return $n.Connecting;case Ti.open:return $n.Open;case Ti.closing:return $n.Closing;default:return $n.Closed}}isConnected(){return this.connectionState()===$n.Open}channel(t,n={config:{}}){const r=`realtime:${t}`,i=this.getChannels().find(s=>s.topic===r);if(i)return i;{const s=new ua(`realtime:${t}`,n,this);return this.channels.push(s),s}}push(t){const{topic:n,event:r,payload:i,ref:s}=t,o=()=>{this.encode(t,a=>{var l;(l=this.conn)===null||l===void 0||l.send(a)})};this.log("push",`${n} ${r} (${s})`,i),this.isConnected()?o():this.sendBuffer.push(o)}async setAuth(t=null){let n=t||this.accessToken&&await this.accessToken()||this.accessTokenValue;this.accessTokenValue!=n&&(this.accessTokenValue=n,this.channels.forEach(r=>{const i={access_token:n,version:l_};n&&r.updateJoinPayload(i),r.joinedOnce&&r._isJoined()&&r._push(ut.access_token,{access_token:n})}))}async sendHeartbeat(){var t;if(!this.isConnected()){this.heartbeatCallback("disconnected");return}if(this.pendingHeartbeatRef){this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),this.heartbeatCallback("timeout"),(t=this.conn)===null||t===void 0||t.close(c_,"hearbeat timeout");return}this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.heartbeatCallback("sent"),await this.setAuth()}onHeartbeat(t){this.heartbeatCallback=t}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(t=>t()),this.sendBuffer=[])}_makeRef(){let t=this.ref+1;return t===this.ref?this.ref=0:this.ref=t,this.ref.toString()}_leaveOpenTopic(t){let n=this.channels.find(r=>r.topic===t&&(r._isJoined()||r._isJoining()));n&&(this.log("transport",`leaving duplicate topic "${t}"`),n.unsubscribe())}_remove(t){this.channels=this.channels.filter(n=>n.topic!==t.topic)}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=t=>this._onConnError(t),this.conn.onmessage=t=>this._onConnMessage(t),this.conn.onclose=t=>this._onConnClose(t))}_onConnMessage(t){this.decode(t.data,n=>{let{topic:r,event:i,payload:s,ref:o}=n;r==="phoenix"&&i==="phx_reply"&&this.heartbeatCallback(n.payload.status=="ok"?"ok":"error"),o&&o===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${s.status||""} ${r} ${i} ${o&&"("+o+")"||""}`,s),Array.from(this.channels).filter(a=>a._isMember(r)).forEach(a=>a._trigger(i,s,o)),this.stateChangeCallbacks.message.forEach(a=>a(n))})}_onConnOpen(){this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),this.worker?this.workerRef||this._startWorkerHeartbeat():this._startHeartbeat(),this.stateChangeCallbacks.open.forEach(t=>t())}_startHeartbeat(){this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs)}_startWorkerHeartbeat(){this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");const t=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(t),this.workerRef.onerror=n=>{this.log("worker","worker error",n.message),this.workerRef.terminate()},this.workerRef.onmessage=n=>{n.data.event==="keepAlive"&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}_onConnClose(t){this.log("transport","close",t),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(n=>n(t))}_onConnError(t){this.log("transport",`${t}`),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(n=>n(t))}_triggerChanError(){this.channels.forEach(t=>t._trigger(ut.error))}_appendParams(t,n){if(Object.keys(n).length===0)return t;const r=t.match(/\?/)?"&":"?",i=new URLSearchParams(n);return`${t}${r}${i}`}_workerObjectUrl(t){let n;if(t)n=t;else{const r=new Blob([w_],{type:"application/javascript"});n=URL.createObjectURL(r)}return n}}class Lc extends Error{constructor(t){super(t),this.__isStorageError=!0,this.name="StorageError"}}function de(e){return typeof e=="object"&&e!==null&&"__isStorageError"in e}class x_ extends Lc{constructor(t,n){super(t),this.name="StorageApiError",this.status=n}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class pu extends Lc{constructor(t,n){super(t),this.name="StorageUnknownError",this.originalError=n}}var __=globalThis&&globalThis.__awaiter||function(e,t,n,r){function i(s){return s instanceof n?s:new n(function(o){o(s)})}return new(n||(n=Promise))(function(s,o){function a(c){try{u(r.next(c))}catch(d){o(d)}}function l(c){try{u(r.throw(c))}catch(d){o(d)}}function u(c){c.done?s(c.value):i(c.value).then(a,l)}u((r=r.apply(e,t||[])).next())})};const Mm=e=>{let t;return e?t=e:typeof fetch>"u"?t=(...n)=>$e(()=>Promise.resolve().then(()=>Qr),void 0).then(({default:r})=>r(...n)):t=fetch,(...n)=>t(...n)},S_=()=>__(void 0,void 0,void 0,function*(){return typeof Response>"u"?(yield $e(()=>Promise.resolve().then(()=>Qr),void 0)).Response:Response}),mu=e=>{if(Array.isArray(e))return e.map(n=>mu(n));if(typeof e=="function"||e!==Object(e))return e;const t={};return Object.entries(e).forEach(([n,r])=>{const i=n.replace(/([-_][a-z])/gi,s=>s.toUpperCase().replace(/[-_]/g,""));t[i]=mu(r)}),t};var Zn=globalThis&&globalThis.__awaiter||function(e,t,n,r){function i(s){return s instanceof n?s:new n(function(o){o(s)})}return new(n||(n=Promise))(function(s,o){function a(c){try{u(r.next(c))}catch(d){o(d)}}function l(c){try{u(r.throw(c))}catch(d){o(d)}}function u(c){c.done?s(c.value):i(c.value).then(a,l)}u((r=r.apply(e,t||[])).next())})};const Qa=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),k_=(e,t,n)=>Zn(void 0,void 0,void 0,function*(){const r=yield S_();e instanceof r&&!(n!=null&&n.noResolveJson)?e.json().then(i=>{t(new x_(Qa(i),e.status||500))}).catch(i=>{t(new pu(Qa(i),i))}):t(new pu(Qa(e),e))}),C_=(e,t,n,r)=>{const i={method:e,headers:(t==null?void 0:t.headers)||{}};return e==="GET"?i:(i.headers=Object.assign({"Content-Type":"application/json"},t==null?void 0:t.headers),r&&(i.body=JSON.stringify(r)),Object.assign(Object.assign({},i),n))};function ls(e,t,n,r,i,s){return Zn(this,void 0,void 0,function*(){return new Promise((o,a)=>{e(n,C_(t,r,i,s)).then(l=>{if(!l.ok)throw l;return r!=null&&r.noResolveJson?l:l.json()}).then(l=>o(l)).catch(l=>k_(l,a,r))})})}function Po(e,t,n,r){return Zn(this,void 0,void 0,function*(){return ls(e,"GET",t,n,r)})}function Jt(e,t,n,r,i){return Zn(this,void 0,void 0,function*(){return ls(e,"POST",t,r,i,n)})}function E_(e,t,n,r,i){return Zn(this,void 0,void 0,function*(){return ls(e,"PUT",t,r,i,n)})}function b_(e,t,n,r){return Zn(this,void 0,void 0,function*(){return ls(e,"HEAD",t,Object.assign(Object.assign({},n),{noResolveJson:!0}),r)})}function Dm(e,t,n,r,i){return Zn(this,void 0,void 0,function*(){return ls(e,"DELETE",t,r,i,n)})}var Ae=globalThis&&globalThis.__awaiter||function(e,t,n,r){function i(s){return s instanceof n?s:new n(function(o){o(s)})}return new(n||(n=Promise))(function(s,o){function a(c){try{u(r.next(c))}catch(d){o(d)}}function l(c){try{u(r.throw(c))}catch(d){o(d)}}function u(c){c.done?s(c.value):i(c.value).then(a,l)}u((r=r.apply(e,t||[])).next())})};const P_={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},Th={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class T_{constructor(t,n={},r,i){this.url=t,this.headers=n,this.bucketId=r,this.fetch=Mm(i)}uploadOrUpdate(t,n,r,i){return Ae(this,void 0,void 0,function*(){try{let s;const o=Object.assign(Object.assign({},Th),i);let a=Object.assign(Object.assign({},this.headers),t==="POST"&&{"x-upsert":String(o.upsert)});const l=o.metadata;typeof Blob<"u"&&r instanceof Blob?(s=new FormData,s.append("cacheControl",o.cacheControl),l&&s.append("metadata",this.encodeMetadata(l)),s.append("",r)):typeof FormData<"u"&&r instanceof FormData?(s=r,s.append("cacheControl",o.cacheControl),l&&s.append("metadata",this.encodeMetadata(l))):(s=r,a["cache-control"]=`max-age=${o.cacheControl}`,a["content-type"]=o.contentType,l&&(a["x-metadata"]=this.toBase64(this.encodeMetadata(l)))),i!=null&&i.headers&&(a=Object.assign(Object.assign({},a),i.headers));const u=this._removeEmptyFolders(n),c=this._getFinalPath(u),d=yield this.fetch(`${this.url}/object/${c}`,Object.assign({method:t,body:s,headers:a},o!=null&&o.duplex?{duplex:o.duplex}:{})),h=yield d.json();return d.ok?{data:{path:u,id:h.Id,fullPath:h.Key},error:null}:{data:null,error:h}}catch(s){if(de(s))return{data:null,error:s};throw s}})}upload(t,n,r){return Ae(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",t,n,r)})}uploadToSignedUrl(t,n,r,i){return Ae(this,void 0,void 0,function*(){const s=this._removeEmptyFolders(t),o=this._getFinalPath(s),a=new URL(this.url+`/object/upload/sign/${o}`);a.searchParams.set("token",n);try{let l;const u=Object.assign({upsert:Th.upsert},i),c=Object.assign(Object.assign({},this.headers),{"x-upsert":String(u.upsert)});typeof Blob<"u"&&r instanceof Blob?(l=new FormData,l.append("cacheControl",u.cacheControl),l.append("",r)):typeof FormData<"u"&&r instanceof FormData?(l=r,l.append("cacheControl",u.cacheControl)):(l=r,c["cache-control"]=`max-age=${u.cacheControl}`,c["content-type"]=u.contentType);const d=yield this.fetch(a.toString(),{method:"PUT",body:l,headers:c}),h=yield d.json();return d.ok?{data:{path:s,fullPath:h.Key},error:null}:{data:null,error:h}}catch(l){if(de(l))return{data:null,error:l};throw l}})}createSignedUploadUrl(t,n){return Ae(this,void 0,void 0,function*(){try{let r=this._getFinalPath(t);const i=Object.assign({},this.headers);n!=null&&n.upsert&&(i["x-upsert"]="true");const s=yield Jt(this.fetch,`${this.url}/object/upload/sign/${r}`,{},{headers:i}),o=new URL(this.url+s.url),a=o.searchParams.get("token");if(!a)throw new Lc("No token returned by API");return{data:{signedUrl:o.toString(),path:t,token:a},error:null}}catch(r){if(de(r))return{data:null,error:r};throw r}})}update(t,n,r){return Ae(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",t,n,r)})}move(t,n,r){return Ae(this,void 0,void 0,function*(){try{return{data:yield Jt(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:t,destinationKey:n,destinationBucket:r==null?void 0:r.destinationBucket},{headers:this.headers}),error:null}}catch(i){if(de(i))return{data:null,error:i};throw i}})}copy(t,n,r){return Ae(this,void 0,void 0,function*(){try{return{data:{path:(yield Jt(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:t,destinationKey:n,destinationBucket:r==null?void 0:r.destinationBucket},{headers:this.headers})).Key},error:null}}catch(i){if(de(i))return{data:null,error:i};throw i}})}createSignedUrl(t,n,r){return Ae(this,void 0,void 0,function*(){try{let i=this._getFinalPath(t),s=yield Jt(this.fetch,`${this.url}/object/sign/${i}`,Object.assign({expiresIn:n},r!=null&&r.transform?{transform:r.transform}:{}),{headers:this.headers});const o=r!=null&&r.download?`&download=${r.download===!0?"":r.download}`:"";return s={signedUrl:encodeURI(`${this.url}${s.signedURL}${o}`)},{data:s,error:null}}catch(i){if(de(i))return{data:null,error:i};throw i}})}createSignedUrls(t,n,r){return Ae(this,void 0,void 0,function*(){try{const i=yield Jt(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:n,paths:t},{headers:this.headers}),s=r!=null&&r.download?`&download=${r.download===!0?"":r.download}`:"";return{data:i.map(o=>Object.assign(Object.assign({},o),{signedUrl:o.signedURL?encodeURI(`${this.url}${o.signedURL}${s}`):null})),error:null}}catch(i){if(de(i))return{data:null,error:i};throw i}})}download(t,n){return Ae(this,void 0,void 0,function*(){const i=typeof(n==null?void 0:n.transform)<"u"?"render/image/authenticated":"object",s=this.transformOptsToQueryString((n==null?void 0:n.transform)||{}),o=s?`?${s}`:"";try{const a=this._getFinalPath(t);return{data:yield(yield Po(this.fetch,`${this.url}/${i}/${a}${o}`,{headers:this.headers,noResolveJson:!0})).blob(),error:null}}catch(a){if(de(a))return{data:null,error:a};throw a}})}info(t){return Ae(this,void 0,void 0,function*(){const n=this._getFinalPath(t);try{const r=yield Po(this.fetch,`${this.url}/object/info/${n}`,{headers:this.headers});return{data:mu(r),error:null}}catch(r){if(de(r))return{data:null,error:r};throw r}})}exists(t){return Ae(this,void 0,void 0,function*(){const n=this._getFinalPath(t);try{return yield b_(this.fetch,`${this.url}/object/${n}`,{headers:this.headers}),{data:!0,error:null}}catch(r){if(de(r)&&r instanceof pu){const i=r.originalError;if([400,404].includes(i==null?void 0:i.status))return{data:!1,error:r}}throw r}})}getPublicUrl(t,n){const r=this._getFinalPath(t),i=[],s=n!=null&&n.download?`download=${n.download===!0?"":n.download}`:"";s!==""&&i.push(s);const a=typeof(n==null?void 0:n.transform)<"u"?"render/image":"object",l=this.transformOptsToQueryString((n==null?void 0:n.transform)||{});l!==""&&i.push(l);let u=i.join("&");return u!==""&&(u=`?${u}`),{data:{publicUrl:encodeURI(`${this.url}/${a}/public/${r}${u}`)}}}remove(t){return Ae(this,void 0,void 0,function*(){try{return{data:yield Dm(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:t},{headers:this.headers}),error:null}}catch(n){if(de(n))return{data:null,error:n};throw n}})}list(t,n,r){return Ae(this,void 0,void 0,function*(){try{const i=Object.assign(Object.assign(Object.assign({},P_),n),{prefix:t||""});return{data:yield Jt(this.fetch,`${this.url}/object/list/${this.bucketId}`,i,{headers:this.headers},r),error:null}}catch(i){if(de(i))return{data:null,error:i};throw i}})}encodeMetadata(t){return JSON.stringify(t)}toBase64(t){return typeof Buffer<"u"?Buffer.from(t).toString("base64"):btoa(t)}_getFinalPath(t){return`${this.bucketId}/${t}`}_removeEmptyFolders(t){return t.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(t){const n=[];return t.width&&n.push(`width=${t.width}`),t.height&&n.push(`height=${t.height}`),t.resize&&n.push(`resize=${t.resize}`),t.format&&n.push(`format=${t.format}`),t.quality&&n.push(`quality=${t.quality}`),n.join("&")}}const j_="2.7.1",A_={"X-Client-Info":`storage-js/${j_}`};var ir=globalThis&&globalThis.__awaiter||function(e,t,n,r){function i(s){return s instanceof n?s:new n(function(o){o(s)})}return new(n||(n=Promise))(function(s,o){function a(c){try{u(r.next(c))}catch(d){o(d)}}function l(c){try{u(r.throw(c))}catch(d){o(d)}}function u(c){c.done?s(c.value):i(c.value).then(a,l)}u((r=r.apply(e,t||[])).next())})};class R_{constructor(t,n={},r){this.url=t,this.headers=Object.assign(Object.assign({},A_),n),this.fetch=Mm(r)}listBuckets(){return ir(this,void 0,void 0,function*(){try{return{data:yield Po(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(t){if(de(t))return{data:null,error:t};throw t}})}getBucket(t){return ir(this,void 0,void 0,function*(){try{return{data:yield Po(this.fetch,`${this.url}/bucket/${t}`,{headers:this.headers}),error:null}}catch(n){if(de(n))return{data:null,error:n};throw n}})}createBucket(t,n={public:!1}){return ir(this,void 0,void 0,function*(){try{return{data:yield Jt(this.fetch,`${this.url}/bucket`,{id:t,name:t,public:n.public,file_size_limit:n.fileSizeLimit,allowed_mime_types:n.allowedMimeTypes},{headers:this.headers}),error:null}}catch(r){if(de(r))return{data:null,error:r};throw r}})}updateBucket(t,n){return ir(this,void 0,void 0,function*(){try{return{data:yield E_(this.fetch,`${this.url}/bucket/${t}`,{id:t,name:t,public:n.public,file_size_limit:n.fileSizeLimit,allowed_mime_types:n.allowedMimeTypes},{headers:this.headers}),error:null}}catch(r){if(de(r))return{data:null,error:r};throw r}})}emptyBucket(t){return ir(this,void 0,void 0,function*(){try{return{data:yield Jt(this.fetch,`${this.url}/bucket/${t}/empty`,{},{headers:this.headers}),error:null}}catch(n){if(de(n))return{data:null,error:n};throw n}})}deleteBucket(t){return ir(this,void 0,void 0,function*(){try{return{data:yield Dm(this.fetch,`${this.url}/bucket/${t}`,{},{headers:this.headers}),error:null}}catch(n){if(de(n))return{data:null,error:n};throw n}})}}class L_ extends R_{constructor(t,n={},r){super(t,n,r)}from(t){return new T_(this.url,this.headers,t,this.fetch)}}const O_="2.50.2";let pi="";typeof Deno<"u"?pi="deno":typeof document<"u"?pi="web":typeof navigator<"u"&&navigator.product==="ReactNative"?pi="react-native":pi="node";const I_={"X-Client-Info":`supabase-js-${pi}/${O_}`},N_={headers:I_},M_={schema:"public"},D_={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},V_={};var $_=globalThis&&globalThis.__awaiter||function(e,t,n,r){function i(s){return s instanceof n?s:new n(function(o){o(s)})}return new(n||(n=Promise))(function(s,o){function a(c){try{u(r.next(c))}catch(d){o(d)}}function l(c){try{u(r.throw(c))}catch(d){o(d)}}function u(c){c.done?s(c.value):i(c.value).then(a,l)}u((r=r.apply(e,t||[])).next())})};const z_=e=>{let t;return e?t=e:typeof fetch>"u"?t=xm:t=fetch,(...n)=>t(...n)},B_=()=>typeof Headers>"u"?_m:Headers,U_=(e,t,n)=>{const r=z_(n),i=B_();return(s,o)=>$_(void 0,void 0,void 0,function*(){var a;const l=(a=yield t())!==null&&a!==void 0?a:e;let u=new i(o==null?void 0:o.headers);return u.has("apikey")||u.set("apikey",e),u.has("Authorization")||u.set("Authorization",`Bearer ${l}`),r(s,Object.assign(Object.assign({},o),{headers:u}))})};var F_=globalThis&&globalThis.__awaiter||function(e,t,n,r){function i(s){return s instanceof n?s:new n(function(o){o(s)})}return new(n||(n=Promise))(function(s,o){function a(c){try{u(r.next(c))}catch(d){o(d)}}function l(c){try{u(r.throw(c))}catch(d){o(d)}}function u(c){c.done?s(c.value):i(c.value).then(a,l)}u((r=r.apply(e,t||[])).next())})};function H_(e){return e.endsWith("/")?e:e+"/"}function W_(e,t){var n,r;const{db:i,auth:s,realtime:o,global:a}=e,{db:l,auth:u,realtime:c,global:d}=t,h={db:Object.assign(Object.assign({},l),i),auth:Object.assign(Object.assign({},u),s),realtime:Object.assign(Object.assign({},c),o),global:Object.assign(Object.assign(Object.assign({},d),a),{headers:Object.assign(Object.assign({},(n=d==null?void 0:d.headers)!==null&&n!==void 0?n:{}),(r=a==null?void 0:a.headers)!==null&&r!==void 0?r:{})}),accessToken:()=>F_(this,void 0,void 0,function*(){return""})};return e.accessToken?h.accessToken=e.accessToken:delete h.accessToken,h}const Vm="2.70.0",ar=30*1e3,gu=3,Ya=gu*ar,K_="http://localhost:9999",G_="supabase.auth.token",q_={"X-Client-Info":`gotrue-js/${Vm}`},vu="X-Supabase-Api-Version",$m={"2024-01-01":{timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"}},J_=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i,Q_=6e5;class ca extends Error{constructor(t,n,r){super(t),this.__isAuthError=!0,this.name="AuthError",this.status=n,this.code=r}}function M(e){return typeof e=="object"&&e!==null&&"__isAuthError"in e}class zm extends ca{constructor(t,n,r){super(t,n,r),this.name="AuthApiError",this.status=n,this.code=r}}function Bm(e){return M(e)&&e.name==="AuthApiError"}class Oc extends ca{constructor(t,n){super(t),this.name="AuthUnknownError",this.originalError=n}}class Ft extends ca{constructor(t,n,r,i){super(t,r,i),this.name=n,this.status=r}}class Pt extends Ft{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}function Um(e){return M(e)&&e.name==="AuthSessionMissingError"}class mi extends Ft{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class gi extends Ft{constructor(t){super(t,"AuthInvalidCredentialsError",400,void 0)}}class vi extends Ft{constructor(t,n=null){super(t,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=n}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}function Fm(e){return M(e)&&e.name==="AuthImplicitGrantRedirectError"}class yu extends Ft{constructor(t,n=null){super(t,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=n}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class To extends Ft{constructor(t,n){super(t,"AuthRetryableFetchError",n,void 0)}}function Js(e){return M(e)&&e.name==="AuthRetryableFetchError"}class wu extends Ft{constructor(t,n,r){super(t,"AuthWeakPasswordError",n,"weak_password"),this.reasons=r}}function Y_(e){return M(e)&&e.name==="AuthWeakPasswordError"}class Lr extends Ft{constructor(t){super(t,"AuthInvalidJwtError",400,"invalid_jwt")}}const jo="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),jh=` 	
\r=`.split(""),X_=(()=>{const e=new Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<jh.length;t+=1)e[jh[t].charCodeAt(0)]=-2;for(let t=0;t<jo.length;t+=1)e[jo[t].charCodeAt(0)]=t;return e})();function Ah(e,t,n){if(e!==null)for(t.queue=t.queue<<8|e,t.queuedBits+=8;t.queuedBits>=6;){const r=t.queue>>t.queuedBits-6&63;n(jo[r]),t.queuedBits-=6}else if(t.queuedBits>0)for(t.queue=t.queue<<6-t.queuedBits,t.queuedBits=6;t.queuedBits>=6;){const r=t.queue>>t.queuedBits-6&63;n(jo[r]),t.queuedBits-=6}}function Hm(e,t,n){const r=X_[e];if(r>-1)for(t.queue=t.queue<<6|r,t.queuedBits+=6;t.queuedBits>=8;)n(t.queue>>t.queuedBits-8&255),t.queuedBits-=8;else{if(r===-2)return;throw new Error(`Invalid Base64-URL character "${String.fromCharCode(e)}"`)}}function Rh(e){const t=[],n=o=>{t.push(String.fromCodePoint(o))},r={utf8seq:0,codepoint:0},i={queue:0,queuedBits:0},s=o=>{tS(o,r,n)};for(let o=0;o<e.length;o+=1)Hm(e.charCodeAt(o),i,s);return t.join("")}function Z_(e,t){if(e<=127){t(e);return}else if(e<=2047){t(192|e>>6),t(128|e&63);return}else if(e<=65535){t(224|e>>12),t(128|e>>6&63),t(128|e&63);return}else if(e<=1114111){t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),t(128|e&63);return}throw new Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}function eS(e,t){for(let n=0;n<e.length;n+=1){let r=e.charCodeAt(n);if(r>55295&&r<=56319){const i=(r-55296)*1024&65535;r=(e.charCodeAt(n+1)-56320&65535|i)+65536,n+=1}Z_(r,t)}}function tS(e,t,n){if(t.utf8seq===0){if(e<=127){n(e);return}for(let r=1;r<6;r+=1)if(!(e>>7-r&1)){t.utf8seq=r;break}if(t.utf8seq===2)t.codepoint=e&31;else if(t.utf8seq===3)t.codepoint=e&15;else if(t.utf8seq===4)t.codepoint=e&7;else throw new Error("Invalid UTF-8 sequence");t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw new Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|e&63,t.utf8seq-=1,t.utf8seq===0&&n(t.codepoint)}}function nS(e){const t=[],n={queue:0,queuedBits:0},r=i=>{t.push(i)};for(let i=0;i<e.length;i+=1)Hm(e.charCodeAt(i),n,r);return new Uint8Array(t)}function rS(e){const t=[];return eS(e,n=>t.push(n)),new Uint8Array(t)}function iS(e){const t=[],n={queue:0,queuedBits:0},r=i=>{t.push(i)};return e.forEach(i=>Ah(i,n,r)),Ah(null,n,r),t.join("")}function sS(e){return Math.round(Date.now()/1e3)+e}function oS(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){const t=Math.random()*16|0;return(e=="x"?t:t&3|8).toString(16)})}const ot=()=>typeof window<"u"&&typeof document<"u",An={tested:!1,writable:!1},ji=()=>{if(!ot())return!1;try{if(typeof globalThis.localStorage!="object")return!1}catch{return!1}if(An.tested)return An.writable;const e=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(e,e),globalThis.localStorage.removeItem(e),An.tested=!0,An.writable=!0}catch{An.tested=!0,An.writable=!1}return An.writable};function aS(e){const t={},n=new URL(e);if(n.hash&&n.hash[0]==="#")try{new URLSearchParams(n.hash.substring(1)).forEach((i,s)=>{t[s]=i})}catch{}return n.searchParams.forEach((r,i)=>{t[i]=r}),t}const Wm=e=>{let t;return e?t=e:typeof fetch>"u"?t=(...n)=>$e(()=>Promise.resolve().then(()=>Qr),void 0).then(({default:r})=>r(...n)):t=fetch,(...n)=>t(...n)},lS=e=>typeof e=="object"&&e!==null&&"status"in e&&"ok"in e&&"json"in e&&typeof e.json=="function",Km=async(e,t,n)=>{await e.setItem(t,JSON.stringify(n))},Rs=async(e,t)=>{const n=await e.getItem(t);if(!n)return null;try{return JSON.parse(n)}catch{return n}},Ls=async(e,t)=>{await e.removeItem(t)};class da{constructor(){this.promise=new da.promiseConstructor((t,n)=>{this.resolve=t,this.reject=n})}}da.promiseConstructor=Promise;function Xa(e){const t=e.split(".");if(t.length!==3)throw new Lr("Invalid JWT structure");for(let r=0;r<t.length;r++)if(!J_.test(t[r]))throw new Lr("JWT not in base64url format");return{header:JSON.parse(Rh(t[0])),payload:JSON.parse(Rh(t[1])),signature:nS(t[2]),raw:{header:t[0],payload:t[1]}}}async function uS(e){return await new Promise(t=>{setTimeout(()=>t(null),e)})}function cS(e,t){return new Promise((r,i)=>{(async()=>{for(let s=0;s<1/0;s++)try{const o=await e(s);if(!t(s,null,o)){r(o);return}}catch(o){if(!t(s,o)){i(o);return}}})()})}function dS(e){return("0"+e.toString(16)).substr(-2)}function hS(){const t=new Uint32Array(56);if(typeof crypto>"u"){const n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",r=n.length;let i="";for(let s=0;s<56;s++)i+=n.charAt(Math.floor(Math.random()*r));return i}return crypto.getRandomValues(t),Array.from(t,dS).join("")}async function fS(e){const n=new TextEncoder().encode(e),r=await crypto.subtle.digest("SHA-256",n),i=new Uint8Array(r);return Array.from(i).map(s=>String.fromCharCode(s)).join("")}async function pS(e){if(!(typeof crypto<"u"&&typeof crypto.subtle<"u"&&typeof TextEncoder<"u"))return console.warn("WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256."),e;const n=await fS(e);return btoa(n).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}async function sr(e,t,n=!1){const r=hS();let i=r;n&&(i+="/PASSWORD_RECOVERY"),await Km(e,`${t}-code-verifier`,i);const s=await pS(r);return[s,r===s?"plain":"s256"]}const mS=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;function gS(e){const t=e.headers.get(vu);if(!t||!t.match(mS))return null;try{return new Date(`${t}T00:00:00.0Z`)}catch{return null}}function vS(e){if(!e)throw new Error("Missing exp claim");const t=Math.floor(Date.now()/1e3);if(e<=t)throw new Error("JWT has expired")}function yS(e){switch(e){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw new Error("Invalid alg claim")}}const wS=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;function or(e){if(!wS.test(e))throw new Error("@supabase/auth-js: Expected parameter to be UUID but is not")}var xS=globalThis&&globalThis.__rest||function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,r=Object.getOwnPropertySymbols(e);i<r.length;i++)t.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(n[r[i]]=e[r[i]]);return n};const In=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),_S=[502,503,504];async function Lh(e){var t;if(!lS(e))throw new To(In(e),0);if(_S.includes(e.status))throw new To(In(e),e.status);let n;try{n=await e.json()}catch(s){throw new Oc(In(s),s)}let r;const i=gS(e);if(i&&i.getTime()>=$m["2024-01-01"].timestamp&&typeof n=="object"&&n&&typeof n.code=="string"?r=n.code:typeof n=="object"&&n&&typeof n.error_code=="string"&&(r=n.error_code),r){if(r==="weak_password")throw new wu(In(n),e.status,((t=n.weak_password)===null||t===void 0?void 0:t.reasons)||[]);if(r==="session_not_found")throw new Pt}else if(typeof n=="object"&&n&&typeof n.weak_password=="object"&&n.weak_password&&Array.isArray(n.weak_password.reasons)&&n.weak_password.reasons.length&&n.weak_password.reasons.reduce((s,o)=>s&&typeof o=="string",!0))throw new wu(In(n),e.status,n.weak_password.reasons);throw new zm(In(n),e.status||500,r)}const SS=(e,t,n,r)=>{const i={method:e,headers:(t==null?void 0:t.headers)||{}};return e==="GET"?i:(i.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},t==null?void 0:t.headers),i.body=JSON.stringify(r),Object.assign(Object.assign({},i),n))};async function V(e,t,n,r){var i;const s=Object.assign({},r==null?void 0:r.headers);s[vu]||(s[vu]=$m["2024-01-01"].name),r!=null&&r.jwt&&(s.Authorization=`Bearer ${r.jwt}`);const o=(i=r==null?void 0:r.query)!==null&&i!==void 0?i:{};r!=null&&r.redirectTo&&(o.redirect_to=r.redirectTo);const a=Object.keys(o).length?"?"+new URLSearchParams(o).toString():"",l=await kS(e,t,n+a,{headers:s,noResolveJson:r==null?void 0:r.noResolveJson},{},r==null?void 0:r.body);return r!=null&&r.xform?r==null?void 0:r.xform(l):{data:Object.assign({},l),error:null}}async function kS(e,t,n,r,i,s){const o=SS(t,r,i,s);let a;try{a=await e(n,Object.assign({},o))}catch(l){throw console.error(l),new To(In(l),0)}if(a.ok||await Lh(a),r!=null&&r.noResolveJson)return a;try{return await a.json()}catch(l){await Lh(l)}}function Et(e){var t;let n=null;PS(e)&&(n=Object.assign({},e),e.expires_at||(n.expires_at=sS(e.expires_in)));const r=(t=e.user)!==null&&t!==void 0?t:e;return{data:{session:n,user:r},error:null}}function Oh(e){const t=Et(e);return!t.error&&e.weak_password&&typeof e.weak_password=="object"&&Array.isArray(e.weak_password.reasons)&&e.weak_password.reasons.length&&e.weak_password.message&&typeof e.weak_password.message=="string"&&e.weak_password.reasons.reduce((n,r)=>n&&typeof r=="string",!0)&&(t.data.weak_password=e.weak_password),t}function Zt(e){var t;return{data:{user:(t=e.user)!==null&&t!==void 0?t:e},error:null}}function CS(e){return{data:e,error:null}}function ES(e){const{action_link:t,email_otp:n,hashed_token:r,redirect_to:i,verification_type:s}=e,o=xS(e,["action_link","email_otp","hashed_token","redirect_to","verification_type"]),a={action_link:t,email_otp:n,hashed_token:r,redirect_to:i,verification_type:s},l=Object.assign({},o);return{data:{properties:a,user:l},error:null}}function bS(e){return e}function PS(e){return e.access_token&&e.refresh_token&&e.expires_in}const Qs=["global","local","others"];var TS=globalThis&&globalThis.__rest||function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,r=Object.getOwnPropertySymbols(e);i<r.length;i++)t.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(n[r[i]]=e[r[i]]);return n};class Ic{constructor({url:t="",headers:n={},fetch:r}){this.url=t,this.headers=n,this.fetch=Wm(r),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}async signOut(t,n=Qs[0]){if(Qs.indexOf(n)<0)throw new Error(`@supabase/auth-js: Parameter scope must be one of ${Qs.join(", ")}`);try{return await V(this.fetch,"POST",`${this.url}/logout?scope=${n}`,{headers:this.headers,jwt:t,noResolveJson:!0}),{data:null,error:null}}catch(r){if(M(r))return{data:null,error:r};throw r}}async inviteUserByEmail(t,n={}){try{return await V(this.fetch,"POST",`${this.url}/invite`,{body:{email:t,data:n.data},headers:this.headers,redirectTo:n.redirectTo,xform:Zt})}catch(r){if(M(r))return{data:{user:null},error:r};throw r}}async generateLink(t){try{const{options:n}=t,r=TS(t,["options"]),i=Object.assign(Object.assign({},r),n);return"newEmail"in r&&(i.new_email=r==null?void 0:r.newEmail,delete i.newEmail),await V(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:i,headers:this.headers,xform:ES,redirectTo:n==null?void 0:n.redirectTo})}catch(n){if(M(n))return{data:{properties:null,user:null},error:n};throw n}}async createUser(t){try{return await V(this.fetch,"POST",`${this.url}/admin/users`,{body:t,headers:this.headers,xform:Zt})}catch(n){if(M(n))return{data:{user:null},error:n};throw n}}async listUsers(t){var n,r,i,s,o,a,l;try{const u={nextPage:null,lastPage:0,total:0},c=await V(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:(r=(n=t==null?void 0:t.page)===null||n===void 0?void 0:n.toString())!==null&&r!==void 0?r:"",per_page:(s=(i=t==null?void 0:t.perPage)===null||i===void 0?void 0:i.toString())!==null&&s!==void 0?s:""},xform:bS});if(c.error)throw c.error;const d=await c.json(),h=(o=c.headers.get("x-total-count"))!==null&&o!==void 0?o:0,f=(l=(a=c.headers.get("link"))===null||a===void 0?void 0:a.split(","))!==null&&l!==void 0?l:[];return f.length>0&&(f.forEach(g=>{const w=parseInt(g.split(";")[0].split("=")[1].substring(0,1)),x=JSON.parse(g.split(";")[1].split("=")[1]);u[`${x}Page`]=w}),u.total=parseInt(h)),{data:Object.assign(Object.assign({},d),u),error:null}}catch(u){if(M(u))return{data:{users:[]},error:u};throw u}}async getUserById(t){or(t);try{return await V(this.fetch,"GET",`${this.url}/admin/users/${t}`,{headers:this.headers,xform:Zt})}catch(n){if(M(n))return{data:{user:null},error:n};throw n}}async updateUserById(t,n){or(t);try{return await V(this.fetch,"PUT",`${this.url}/admin/users/${t}`,{body:n,headers:this.headers,xform:Zt})}catch(r){if(M(r))return{data:{user:null},error:r};throw r}}async deleteUser(t,n=!1){or(t);try{return await V(this.fetch,"DELETE",`${this.url}/admin/users/${t}`,{headers:this.headers,body:{should_soft_delete:n},xform:Zt})}catch(r){if(M(r))return{data:{user:null},error:r};throw r}}async _listFactors(t){or(t.userId);try{const{data:n,error:r}=await V(this.fetch,"GET",`${this.url}/admin/users/${t.userId}/factors`,{headers:this.headers,xform:i=>({data:{factors:i},error:null})});return{data:n,error:r}}catch(n){if(M(n))return{data:null,error:n};throw n}}async _deleteFactor(t){or(t.userId),or(t.id);try{return{data:await V(this.fetch,"DELETE",`${this.url}/admin/users/${t.userId}/factors/${t.id}`,{headers:this.headers}),error:null}}catch(n){if(M(n))return{data:null,error:n};throw n}}}const jS={getItem:e=>ji()?globalThis.localStorage.getItem(e):null,setItem:(e,t)=>{ji()&&globalThis.localStorage.setItem(e,t)},removeItem:e=>{ji()&&globalThis.localStorage.removeItem(e)}};function Ih(e={}){return{getItem:t=>e[t]||null,setItem:(t,n)=>{e[t]=n},removeItem:t=>{delete e[t]}}}function AS(){if(typeof globalThis!="object")try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch{typeof self<"u"&&(self.globalThis=self)}}const Nn={debug:!!(globalThis&&ji()&&globalThis.localStorage&&globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug")==="true")};class Nc extends Error{constructor(t){super(t),this.isAcquireTimeout=!0}}class Gm extends Nc{}class RS extends Nc{}async function qm(e,t,n){Nn.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquire lock",e,t);const r=new globalThis.AbortController;return t>0&&setTimeout(()=>{r.abort(),Nn.debug&&console.log("@supabase/gotrue-js: navigatorLock acquire timed out",e)},t),await Promise.resolve().then(()=>globalThis.navigator.locks.request(e,t===0?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:r.signal},async i=>{if(i){Nn.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquired",e,i.name);try{return await n()}finally{Nn.debug&&console.log("@supabase/gotrue-js: navigatorLock: released",e,i.name)}}else{if(t===0)throw Nn.debug&&console.log("@supabase/gotrue-js: navigatorLock: not immediately available",e),new Gm(`Acquiring an exclusive Navigator LockManager lock "${e}" immediately failed`);if(Nn.debug)try{const s=await globalThis.navigator.locks.query();console.log("@supabase/gotrue-js: Navigator LockManager state",JSON.stringify(s,null,"  "))}catch(s){console.warn("@supabase/gotrue-js: Error when querying Navigator LockManager state",s)}return console.warn("@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request"),await n()}}))}const Nh={};async function LS(e,t,n){var r;const i=(r=Nh[e])!==null&&r!==void 0?r:Promise.resolve(),s=Promise.race([i.catch(()=>null),t>=0?new Promise((o,a)=>{setTimeout(()=>{a(new RS(`Acquring process lock with name "${e}" timed out`))},t)}):null].filter(o=>o)).catch(o=>{if(o&&o.isAcquireTimeout)throw o;return null}).then(async()=>await n());return Nh[e]=s.catch(async o=>{if(o&&o.isAcquireTimeout)return await i,null;throw o}),await s}AS();const OS={url:K_,storageKey:G_,autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:q_,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};async function Mh(e,t,n){return await n()}class Ur{constructor(t){var n,r;this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=Ur.nextInstanceID,Ur.nextInstanceID+=1,this.instanceID>0&&ot()&&console.warn("Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.");const i=Object.assign(Object.assign({},OS),t);if(this.logDebugMessages=!!i.debug,typeof i.debug=="function"&&(this.logger=i.debug),this.persistSession=i.persistSession,this.storageKey=i.storageKey,this.autoRefreshToken=i.autoRefreshToken,this.admin=new Ic({url:i.url,headers:i.headers,fetch:i.fetch}),this.url=i.url,this.headers=i.headers,this.fetch=Wm(i.fetch),this.lock=i.lock||Mh,this.detectSessionInUrl=i.detectSessionInUrl,this.flowType=i.flowType,this.hasCustomAuthorizationHeader=i.hasCustomAuthorizationHeader,i.lock?this.lock=i.lock:ot()&&(!((n=globalThis==null?void 0:globalThis.navigator)===null||n===void 0)&&n.locks)?this.lock=qm:this.lock=Mh,this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER,this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?i.storage?this.storage=i.storage:ji()?this.storage=jS:(this.memoryStorage={},this.storage=Ih(this.memoryStorage)):(this.memoryStorage={},this.storage=Ih(this.memoryStorage)),ot()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(s){console.error("Failed to create a new BroadcastChannel, multi-tab state changes will not be available",s)}(r=this.broadcastChannel)===null||r===void 0||r.addEventListener("message",async s=>{this._debug("received broadcast notification from other tab or client",s),await this._notifyAllSubscribers(s.data.event,s.data.session,!1)})}this.initialize()}_debug(...t){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${Vm}) ${new Date().toISOString()}`,...t),this}async initialize(){return this.initializePromise?await this.initializePromise:(this.initializePromise=(async()=>await this._acquireLock(-1,async()=>await this._initialize()))(),await this.initializePromise)}async _initialize(){var t;try{const n=aS(window.location.href);let r="none";if(this._isImplicitGrantCallback(n)?r="implicit":await this._isPKCECallback(n)&&(r="pkce"),ot()&&this.detectSessionInUrl&&r!=="none"){const{data:i,error:s}=await this._getSessionFromURL(n,r);if(s){if(this._debug("#_initialize()","error detecting session from URL",s),Fm(s)){const l=(t=s.details)===null||t===void 0?void 0:t.code;if(l==="identity_already_exists"||l==="identity_not_found"||l==="single_identity_not_deletable")return{error:s}}return await this._removeSession(),{error:s}}const{session:o,redirectType:a}=i;return this._debug("#_initialize()","detected session in URL",o,"redirect type",a),await this._saveSession(o),setTimeout(async()=>{a==="recovery"?await this._notifyAllSubscribers("PASSWORD_RECOVERY",o):await this._notifyAllSubscribers("SIGNED_IN",o)},0),{error:null}}return await this._recoverAndRefresh(),{error:null}}catch(n){return M(n)?{error:n}:{error:new Oc("Unexpected error during initialization",n)}}finally{await this._handleVisibilityChange(),this._debug("#_initialize()","end")}}async signInAnonymously(t){var n,r,i;try{const s=await V(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:(r=(n=t==null?void 0:t.options)===null||n===void 0?void 0:n.data)!==null&&r!==void 0?r:{},gotrue_meta_security:{captcha_token:(i=t==null?void 0:t.options)===null||i===void 0?void 0:i.captchaToken}},xform:Et}),{data:o,error:a}=s;if(a||!o)return{data:{user:null,session:null},error:a};const l=o.session,u=o.user;return o.session&&(await this._saveSession(o.session),await this._notifyAllSubscribers("SIGNED_IN",l)),{data:{user:u,session:l},error:null}}catch(s){if(M(s))return{data:{user:null,session:null},error:s};throw s}}async signUp(t){var n,r,i;try{let s;if("email"in t){const{email:c,password:d,options:h}=t;let f=null,g=null;this.flowType==="pkce"&&([f,g]=await sr(this.storage,this.storageKey)),s=await V(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:h==null?void 0:h.emailRedirectTo,body:{email:c,password:d,data:(n=h==null?void 0:h.data)!==null&&n!==void 0?n:{},gotrue_meta_security:{captcha_token:h==null?void 0:h.captchaToken},code_challenge:f,code_challenge_method:g},xform:Et})}else if("phone"in t){const{phone:c,password:d,options:h}=t;s=await V(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:c,password:d,data:(r=h==null?void 0:h.data)!==null&&r!==void 0?r:{},channel:(i=h==null?void 0:h.channel)!==null&&i!==void 0?i:"sms",gotrue_meta_security:{captcha_token:h==null?void 0:h.captchaToken}},xform:Et})}else throw new gi("You must provide either an email or phone number and a password");const{data:o,error:a}=s;if(a||!o)return{data:{user:null,session:null},error:a};const l=o.session,u=o.user;return o.session&&(await this._saveSession(o.session),await this._notifyAllSubscribers("SIGNED_IN",l)),{data:{user:u,session:l},error:null}}catch(s){if(M(s))return{data:{user:null,session:null},error:s};throw s}}async signInWithPassword(t){try{let n;if("email"in t){const{email:s,password:o,options:a}=t;n=await V(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:s,password:o,gotrue_meta_security:{captcha_token:a==null?void 0:a.captchaToken}},xform:Oh})}else if("phone"in t){const{phone:s,password:o,options:a}=t;n=await V(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:s,password:o,gotrue_meta_security:{captcha_token:a==null?void 0:a.captchaToken}},xform:Oh})}else throw new gi("You must provide either an email or phone number and a password");const{data:r,error:i}=n;return i?{data:{user:null,session:null},error:i}:!r||!r.session||!r.user?{data:{user:null,session:null},error:new mi}:(r.session&&(await this._saveSession(r.session),await this._notifyAllSubscribers("SIGNED_IN",r.session)),{data:Object.assign({user:r.user,session:r.session},r.weak_password?{weakPassword:r.weak_password}:null),error:i})}catch(n){if(M(n))return{data:{user:null,session:null},error:n};throw n}}async signInWithOAuth(t){var n,r,i,s;return await this._handleProviderSignIn(t.provider,{redirectTo:(n=t.options)===null||n===void 0?void 0:n.redirectTo,scopes:(r=t.options)===null||r===void 0?void 0:r.scopes,queryParams:(i=t.options)===null||i===void 0?void 0:i.queryParams,skipBrowserRedirect:(s=t.options)===null||s===void 0?void 0:s.skipBrowserRedirect})}async exchangeCodeForSession(t){return await this.initializePromise,this._acquireLock(-1,async()=>this._exchangeCodeForSession(t))}async signInWithWeb3(t){const{chain:n}=t;if(n==="solana")return await this.signInWithSolana(t);throw new Error(`@supabase/auth-js: Unsupported chain "${n}"`)}async signInWithSolana(t){var n,r,i,s,o,a,l,u,c,d,h,f;let g,w;if("message"in t)g=t.message,w=t.signature;else{const{chain:x,wallet:y,statement:p,options:m}=t;let S;if(ot())if(typeof y=="object")S=y;else{const C=window;if("solana"in C&&typeof C.solana=="object"&&("signIn"in C.solana&&typeof C.solana.signIn=="function"||"signMessage"in C.solana&&typeof C.solana.signMessage=="function"))S=C.solana;else throw new Error("@supabase/auth-js: No compatible Solana wallet interface on the window object (window.solana) detected. Make sure the user already has a wallet installed and connected for this app. Prefer passing the wallet interface object directly to signInWithWeb3({ chain: 'solana', wallet: resolvedUserWallet }) instead.")}else{if(typeof y!="object"||!(m!=null&&m.url))throw new Error("@supabase/auth-js: Both wallet and url must be specified in non-browser environments.");S=y}const k=new URL((n=m==null?void 0:m.url)!==null&&n!==void 0?n:window.location.href);if("signIn"in S&&S.signIn){const C=await S.signIn(Object.assign(Object.assign(Object.assign({issuedAt:new Date().toISOString()},m==null?void 0:m.signInWithSolana),{version:"1",domain:k.host,uri:k.href}),p?{statement:p}:null));let E;if(Array.isArray(C)&&C[0]&&typeof C[0]=="object")E=C[0];else if(C&&typeof C=="object"&&"signedMessage"in C&&"signature"in C)E=C;else throw new Error("@supabase/auth-js: Wallet method signIn() returned unrecognized value");if("signedMessage"in E&&"signature"in E&&(typeof E.signedMessage=="string"||E.signedMessage instanceof Uint8Array)&&E.signature instanceof Uint8Array)g=typeof E.signedMessage=="string"?E.signedMessage:new TextDecoder().decode(E.signedMessage),w=E.signature;else throw new Error("@supabase/auth-js: Wallet method signIn() API returned object without signedMessage and signature fields")}else{if(!("signMessage"in S)||typeof S.signMessage!="function"||!("publicKey"in S)||typeof S!="object"||!S.publicKey||!("toBase58"in S.publicKey)||typeof S.publicKey.toBase58!="function")throw new Error("@supabase/auth-js: Wallet does not have a compatible signMessage() and publicKey.toBase58() API");g=[`${k.host} wants you to sign in with your Solana account:`,S.publicKey.toBase58(),...p?["",p,""]:[""],"Version: 1",`URI: ${k.href}`,`Issued At: ${(i=(r=m==null?void 0:m.signInWithSolana)===null||r===void 0?void 0:r.issuedAt)!==null&&i!==void 0?i:new Date().toISOString()}`,...!((s=m==null?void 0:m.signInWithSolana)===null||s===void 0)&&s.notBefore?[`Not Before: ${m.signInWithSolana.notBefore}`]:[],...!((o=m==null?void 0:m.signInWithSolana)===null||o===void 0)&&o.expirationTime?[`Expiration Time: ${m.signInWithSolana.expirationTime}`]:[],...!((a=m==null?void 0:m.signInWithSolana)===null||a===void 0)&&a.chainId?[`Chain ID: ${m.signInWithSolana.chainId}`]:[],...!((l=m==null?void 0:m.signInWithSolana)===null||l===void 0)&&l.nonce?[`Nonce: ${m.signInWithSolana.nonce}`]:[],...!((u=m==null?void 0:m.signInWithSolana)===null||u===void 0)&&u.requestId?[`Request ID: ${m.signInWithSolana.requestId}`]:[],...!((d=(c=m==null?void 0:m.signInWithSolana)===null||c===void 0?void 0:c.resources)===null||d===void 0)&&d.length?["Resources",...m.signInWithSolana.resources.map(E=>`- ${E}`)]:[]].join(`
`);const C=await S.signMessage(new TextEncoder().encode(g),"utf8");if(!C||!(C instanceof Uint8Array))throw new Error("@supabase/auth-js: Wallet signMessage() API returned an recognized value");w=C}}try{const{data:x,error:y}=await V(this.fetch,"POST",`${this.url}/token?grant_type=web3`,{headers:this.headers,body:Object.assign({chain:"solana",message:g,signature:iS(w)},!((h=t.options)===null||h===void 0)&&h.captchaToken?{gotrue_meta_security:{captcha_token:(f=t.options)===null||f===void 0?void 0:f.captchaToken}}:null),xform:Et});if(y)throw y;return!x||!x.session||!x.user?{data:{user:null,session:null},error:new mi}:(x.session&&(await this._saveSession(x.session),await this._notifyAllSubscribers("SIGNED_IN",x.session)),{data:Object.assign({},x),error:y})}catch(x){if(M(x))return{data:{user:null,session:null},error:x};throw x}}async _exchangeCodeForSession(t){const n=await Rs(this.storage,`${this.storageKey}-code-verifier`),[r,i]=(n??"").split("/");try{const{data:s,error:o}=await V(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:t,code_verifier:r},xform:Et});if(await Ls(this.storage,`${this.storageKey}-code-verifier`),o)throw o;return!s||!s.session||!s.user?{data:{user:null,session:null,redirectType:null},error:new mi}:(s.session&&(await this._saveSession(s.session),await this._notifyAllSubscribers("SIGNED_IN",s.session)),{data:Object.assign(Object.assign({},s),{redirectType:i??null}),error:o})}catch(s){if(M(s))return{data:{user:null,session:null,redirectType:null},error:s};throw s}}async signInWithIdToken(t){try{const{options:n,provider:r,token:i,access_token:s,nonce:o}=t,a=await V(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:r,id_token:i,access_token:s,nonce:o,gotrue_meta_security:{captcha_token:n==null?void 0:n.captchaToken}},xform:Et}),{data:l,error:u}=a;return u?{data:{user:null,session:null},error:u}:!l||!l.session||!l.user?{data:{user:null,session:null},error:new mi}:(l.session&&(await this._saveSession(l.session),await this._notifyAllSubscribers("SIGNED_IN",l.session)),{data:l,error:u})}catch(n){if(M(n))return{data:{user:null,session:null},error:n};throw n}}async signInWithOtp(t){var n,r,i,s,o;try{if("email"in t){const{email:a,options:l}=t;let u=null,c=null;this.flowType==="pkce"&&([u,c]=await sr(this.storage,this.storageKey));const{error:d}=await V(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:a,data:(n=l==null?void 0:l.data)!==null&&n!==void 0?n:{},create_user:(r=l==null?void 0:l.shouldCreateUser)!==null&&r!==void 0?r:!0,gotrue_meta_security:{captcha_token:l==null?void 0:l.captchaToken},code_challenge:u,code_challenge_method:c},redirectTo:l==null?void 0:l.emailRedirectTo});return{data:{user:null,session:null},error:d}}if("phone"in t){const{phone:a,options:l}=t,{data:u,error:c}=await V(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:a,data:(i=l==null?void 0:l.data)!==null&&i!==void 0?i:{},create_user:(s=l==null?void 0:l.shouldCreateUser)!==null&&s!==void 0?s:!0,gotrue_meta_security:{captcha_token:l==null?void 0:l.captchaToken},channel:(o=l==null?void 0:l.channel)!==null&&o!==void 0?o:"sms"}});return{data:{user:null,session:null,messageId:u==null?void 0:u.message_id},error:c}}throw new gi("You must provide either an email or phone number.")}catch(a){if(M(a))return{data:{user:null,session:null},error:a};throw a}}async verifyOtp(t){var n,r;try{let i,s;"options"in t&&(i=(n=t.options)===null||n===void 0?void 0:n.redirectTo,s=(r=t.options)===null||r===void 0?void 0:r.captchaToken);const{data:o,error:a}=await V(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},t),{gotrue_meta_security:{captcha_token:s}}),redirectTo:i,xform:Et});if(a)throw a;if(!o)throw new Error("An error occurred on token verification.");const l=o.session,u=o.user;return l!=null&&l.access_token&&(await this._saveSession(l),await this._notifyAllSubscribers(t.type=="recovery"?"PASSWORD_RECOVERY":"SIGNED_IN",l)),{data:{user:u,session:l},error:null}}catch(i){if(M(i))return{data:{user:null,session:null},error:i};throw i}}async signInWithSSO(t){var n,r,i;try{let s=null,o=null;return this.flowType==="pkce"&&([s,o]=await sr(this.storage,this.storageKey)),await V(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in t?{provider_id:t.providerId}:null),"domain"in t?{domain:t.domain}:null),{redirect_to:(r=(n=t.options)===null||n===void 0?void 0:n.redirectTo)!==null&&r!==void 0?r:void 0}),!((i=t==null?void 0:t.options)===null||i===void 0)&&i.captchaToken?{gotrue_meta_security:{captcha_token:t.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:s,code_challenge_method:o}),headers:this.headers,xform:CS})}catch(s){if(M(s))return{data:null,error:s};throw s}}async reauthenticate(){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._reauthenticate())}async _reauthenticate(){try{return await this._useSession(async t=>{const{data:{session:n},error:r}=t;if(r)throw r;if(!n)throw new Pt;const{error:i}=await V(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:n.access_token});return{data:{user:null,session:null},error:i}})}catch(t){if(M(t))return{data:{user:null,session:null},error:t};throw t}}async resend(t){try{const n=`${this.url}/resend`;if("email"in t){const{email:r,type:i,options:s}=t,{error:o}=await V(this.fetch,"POST",n,{headers:this.headers,body:{email:r,type:i,gotrue_meta_security:{captcha_token:s==null?void 0:s.captchaToken}},redirectTo:s==null?void 0:s.emailRedirectTo});return{data:{user:null,session:null},error:o}}else if("phone"in t){const{phone:r,type:i,options:s}=t,{data:o,error:a}=await V(this.fetch,"POST",n,{headers:this.headers,body:{phone:r,type:i,gotrue_meta_security:{captcha_token:s==null?void 0:s.captchaToken}}});return{data:{user:null,session:null,messageId:o==null?void 0:o.message_id},error:a}}throw new gi("You must provide either an email or phone number and a type")}catch(n){if(M(n))return{data:{user:null,session:null},error:n};throw n}}async getSession(){return await this.initializePromise,await this._acquireLock(-1,async()=>this._useSession(async n=>n))}async _acquireLock(t,n){this._debug("#_acquireLock","begin",t);try{if(this.lockAcquired){const r=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),i=(async()=>(await r,await n()))();return this.pendingInLock.push((async()=>{try{await i}catch{}})()),i}return await this.lock(`lock:${this.storageKey}`,t,async()=>{this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;const r=n();for(this.pendingInLock.push((async()=>{try{await r}catch{}})()),await r;this.pendingInLock.length;){const i=[...this.pendingInLock];await Promise.all(i),this.pendingInLock.splice(0,i.length)}return await r}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}})}finally{this._debug("#_acquireLock","end")}}async _useSession(t){this._debug("#_useSession","begin");try{const n=await this.__loadSession();return await t(n)}finally{this._debug("#_useSession","end")}}async __loadSession(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",new Error().stack);try{let t=null;const n=await Rs(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",n),n!==null&&(this._isValidSession(n)?t=n:(this._debug("#getSession()","session from storage is not valid"),await this._removeSession())),!t)return{data:{session:null},error:null};const r=t.expires_at?t.expires_at*1e3-Date.now()<Ya:!1;if(this._debug("#__loadSession()",`session has${r?"":" not"} expired`,"expires_at",t.expires_at),!r){if(this.storage.isServer){let o=this.suppressGetSessionWarning;t=new Proxy(t,{get:(l,u,c)=>(!o&&u==="user"&&(console.warn("Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server."),o=!0,this.suppressGetSessionWarning=!0),Reflect.get(l,u,c))})}return{data:{session:t},error:null}}const{session:i,error:s}=await this._callRefreshToken(t.refresh_token);return s?{data:{session:null},error:s}:{data:{session:i},error:null}}finally{this._debug("#__loadSession()","end")}}async getUser(t){return t?await this._getUser(t):(await this.initializePromise,await this._acquireLock(-1,async()=>await this._getUser()))}async _getUser(t){try{return t?await V(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:t,xform:Zt}):await this._useSession(async n=>{var r,i,s;const{data:o,error:a}=n;if(a)throw a;return!(!((r=o.session)===null||r===void 0)&&r.access_token)&&!this.hasCustomAuthorizationHeader?{data:{user:null},error:new Pt}:await V(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:(s=(i=o.session)===null||i===void 0?void 0:i.access_token)!==null&&s!==void 0?s:void 0,xform:Zt})})}catch(n){if(M(n))return Um(n)&&(await this._removeSession(),await Ls(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:n};throw n}}async updateUser(t,n={}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._updateUser(t,n))}async _updateUser(t,n={}){try{return await this._useSession(async r=>{const{data:i,error:s}=r;if(s)throw s;if(!i.session)throw new Pt;const o=i.session;let a=null,l=null;this.flowType==="pkce"&&t.email!=null&&([a,l]=await sr(this.storage,this.storageKey));const{data:u,error:c}=await V(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:n==null?void 0:n.emailRedirectTo,body:Object.assign(Object.assign({},t),{code_challenge:a,code_challenge_method:l}),jwt:o.access_token,xform:Zt});if(c)throw c;return o.user=u.user,await this._saveSession(o),await this._notifyAllSubscribers("USER_UPDATED",o),{data:{user:o.user},error:null}})}catch(r){if(M(r))return{data:{user:null},error:r};throw r}}async setSession(t){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._setSession(t))}async _setSession(t){try{if(!t.access_token||!t.refresh_token)throw new Pt;const n=Date.now()/1e3;let r=n,i=!0,s=null;const{payload:o}=Xa(t.access_token);if(o.exp&&(r=o.exp,i=r<=n),i){const{session:a,error:l}=await this._callRefreshToken(t.refresh_token);if(l)return{data:{user:null,session:null},error:l};if(!a)return{data:{user:null,session:null},error:null};s=a}else{const{data:a,error:l}=await this._getUser(t.access_token);if(l)throw l;s={access_token:t.access_token,refresh_token:t.refresh_token,user:a.user,token_type:"bearer",expires_in:r-n,expires_at:r},await this._saveSession(s),await this._notifyAllSubscribers("SIGNED_IN",s)}return{data:{user:s.user,session:s},error:null}}catch(n){if(M(n))return{data:{session:null,user:null},error:n};throw n}}async refreshSession(t){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._refreshSession(t))}async _refreshSession(t){try{return await this._useSession(async n=>{var r;if(!t){const{data:o,error:a}=n;if(a)throw a;t=(r=o.session)!==null&&r!==void 0?r:void 0}if(!(t!=null&&t.refresh_token))throw new Pt;const{session:i,error:s}=await this._callRefreshToken(t.refresh_token);return s?{data:{user:null,session:null},error:s}:i?{data:{user:i.user,session:i},error:null}:{data:{user:null,session:null},error:null}})}catch(n){if(M(n))return{data:{user:null,session:null},error:n};throw n}}async _getSessionFromURL(t,n){try{if(!ot())throw new vi("No browser detected.");if(t.error||t.error_description||t.error_code)throw new vi(t.error_description||"Error in URL with unspecified error_description",{error:t.error||"unspecified_error",code:t.error_code||"unspecified_code"});switch(n){case"implicit":if(this.flowType==="pkce")throw new yu("Not a valid PKCE flow url.");break;case"pkce":if(this.flowType==="implicit")throw new vi("Not a valid implicit grant flow url.");break;default:}if(n==="pkce"){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!t.code)throw new yu("No code detected.");const{data:p,error:m}=await this._exchangeCodeForSession(t.code);if(m)throw m;const S=new URL(window.location.href);return S.searchParams.delete("code"),window.history.replaceState(window.history.state,"",S.toString()),{data:{session:p.session,redirectType:null},error:null}}const{provider_token:r,provider_refresh_token:i,access_token:s,refresh_token:o,expires_in:a,expires_at:l,token_type:u}=t;if(!s||!a||!o||!u)throw new vi("No session defined in URL");const c=Math.round(Date.now()/1e3),d=parseInt(a);let h=c+d;l&&(h=parseInt(l));const f=h-c;f*1e3<=ar&&console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${f}s, should have been closer to ${d}s`);const g=h-d;c-g>=120?console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale",g,h,c):c-g<0&&console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew",g,h,c);const{data:w,error:x}=await this._getUser(s);if(x)throw x;const y={provider_token:r,provider_refresh_token:i,access_token:s,expires_in:d,expires_at:h,refresh_token:o,token_type:u,user:w.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:y,redirectType:t.type},error:null}}catch(r){if(M(r))return{data:{session:null,redirectType:null},error:r};throw r}}_isImplicitGrantCallback(t){return!!(t.access_token||t.error_description)}async _isPKCECallback(t){const n=await Rs(this.storage,`${this.storageKey}-code-verifier`);return!!(t.code&&n)}async signOut(t={scope:"global"}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._signOut(t))}async _signOut({scope:t}={scope:"global"}){return await this._useSession(async n=>{var r;const{data:i,error:s}=n;if(s)return{error:s};const o=(r=i.session)===null||r===void 0?void 0:r.access_token;if(o){const{error:a}=await this.admin.signOut(o,t);if(a&&!(Bm(a)&&(a.status===404||a.status===401||a.status===403)))return{error:a}}return t!=="others"&&(await this._removeSession(),await Ls(this.storage,`${this.storageKey}-code-verifier`)),{error:null}})}onAuthStateChange(t){const n=oS(),r={id:n,callback:t,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",n),this.stateChangeEmitters.delete(n)}};return this._debug("#onAuthStateChange()","registered callback with id",n),this.stateChangeEmitters.set(n,r),(async()=>(await this.initializePromise,await this._acquireLock(-1,async()=>{this._emitInitialSession(n)})))(),{data:{subscription:r}}}async _emitInitialSession(t){return await this._useSession(async n=>{var r,i;try{const{data:{session:s},error:o}=n;if(o)throw o;await((r=this.stateChangeEmitters.get(t))===null||r===void 0?void 0:r.callback("INITIAL_SESSION",s)),this._debug("INITIAL_SESSION","callback id",t,"session",s)}catch(s){await((i=this.stateChangeEmitters.get(t))===null||i===void 0?void 0:i.callback("INITIAL_SESSION",null)),this._debug("INITIAL_SESSION","callback id",t,"error",s),console.error(s)}})}async resetPasswordForEmail(t,n={}){let r=null,i=null;this.flowType==="pkce"&&([r,i]=await sr(this.storage,this.storageKey,!0));try{return await V(this.fetch,"POST",`${this.url}/recover`,{body:{email:t,code_challenge:r,code_challenge_method:i,gotrue_meta_security:{captcha_token:n.captchaToken}},headers:this.headers,redirectTo:n.redirectTo})}catch(s){if(M(s))return{data:null,error:s};throw s}}async getUserIdentities(){var t;try{const{data:n,error:r}=await this.getUser();if(r)throw r;return{data:{identities:(t=n.user.identities)!==null&&t!==void 0?t:[]},error:null}}catch(n){if(M(n))return{data:null,error:n};throw n}}async linkIdentity(t){var n;try{const{data:r,error:i}=await this._useSession(async s=>{var o,a,l,u,c;const{data:d,error:h}=s;if(h)throw h;const f=await this._getUrlForProvider(`${this.url}/user/identities/authorize`,t.provider,{redirectTo:(o=t.options)===null||o===void 0?void 0:o.redirectTo,scopes:(a=t.options)===null||a===void 0?void 0:a.scopes,queryParams:(l=t.options)===null||l===void 0?void 0:l.queryParams,skipBrowserRedirect:!0});return await V(this.fetch,"GET",f,{headers:this.headers,jwt:(c=(u=d.session)===null||u===void 0?void 0:u.access_token)!==null&&c!==void 0?c:void 0})});if(i)throw i;return ot()&&!(!((n=t.options)===null||n===void 0)&&n.skipBrowserRedirect)&&window.location.assign(r==null?void 0:r.url),{data:{provider:t.provider,url:r==null?void 0:r.url},error:null}}catch(r){if(M(r))return{data:{provider:t.provider,url:null},error:r};throw r}}async unlinkIdentity(t){try{return await this._useSession(async n=>{var r,i;const{data:s,error:o}=n;if(o)throw o;return await V(this.fetch,"DELETE",`${this.url}/user/identities/${t.identity_id}`,{headers:this.headers,jwt:(i=(r=s.session)===null||r===void 0?void 0:r.access_token)!==null&&i!==void 0?i:void 0})})}catch(n){if(M(n))return{data:null,error:n};throw n}}async _refreshAccessToken(t){const n=`#_refreshAccessToken(${t.substring(0,5)}...)`;this._debug(n,"begin");try{const r=Date.now();return await cS(async i=>(i>0&&await uS(200*Math.pow(2,i-1)),this._debug(n,"refreshing attempt",i),await V(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:t},headers:this.headers,xform:Et})),(i,s)=>{const o=200*Math.pow(2,i);return s&&Js(s)&&Date.now()+o-r<ar})}catch(r){if(this._debug(n,"error",r),M(r))return{data:{session:null,user:null},error:r};throw r}finally{this._debug(n,"end")}}_isValidSession(t){return typeof t=="object"&&t!==null&&"access_token"in t&&"refresh_token"in t&&"expires_at"in t}async _handleProviderSignIn(t,n){const r=await this._getUrlForProvider(`${this.url}/authorize`,t,{redirectTo:n.redirectTo,scopes:n.scopes,queryParams:n.queryParams});return this._debug("#_handleProviderSignIn()","provider",t,"options",n,"url",r),ot()&&!n.skipBrowserRedirect&&window.location.assign(r),{data:{provider:t,url:r},error:null}}async _recoverAndRefresh(){var t;const n="#_recoverAndRefresh()";this._debug(n,"begin");try{const r=await Rs(this.storage,this.storageKey);if(this._debug(n,"session from storage",r),!this._isValidSession(r)){this._debug(n,"session is not valid"),r!==null&&await this._removeSession();return}const i=((t=r.expires_at)!==null&&t!==void 0?t:1/0)*1e3-Date.now()<Ya;if(this._debug(n,`session has${i?"":" not"} expired with margin of ${Ya}s`),i){if(this.autoRefreshToken&&r.refresh_token){const{error:s}=await this._callRefreshToken(r.refresh_token);s&&(console.error(s),Js(s)||(this._debug(n,"refresh failed with a non-retryable error, removing the session",s),await this._removeSession()))}}else await this._notifyAllSubscribers("SIGNED_IN",r)}catch(r){this._debug(n,"error",r),console.error(r);return}finally{this._debug(n,"end")}}async _callRefreshToken(t){var n,r;if(!t)throw new Pt;if(this.refreshingDeferred)return this.refreshingDeferred.promise;const i=`#_callRefreshToken(${t.substring(0,5)}...)`;this._debug(i,"begin");try{this.refreshingDeferred=new da;const{data:s,error:o}=await this._refreshAccessToken(t);if(o)throw o;if(!s.session)throw new Pt;await this._saveSession(s.session),await this._notifyAllSubscribers("TOKEN_REFRESHED",s.session);const a={session:s.session,error:null};return this.refreshingDeferred.resolve(a),a}catch(s){if(this._debug(i,"error",s),M(s)){const o={session:null,error:s};return Js(s)||await this._removeSession(),(n=this.refreshingDeferred)===null||n===void 0||n.resolve(o),o}throw(r=this.refreshingDeferred)===null||r===void 0||r.reject(s),s}finally{this.refreshingDeferred=null,this._debug(i,"end")}}async _notifyAllSubscribers(t,n,r=!0){const i=`#_notifyAllSubscribers(${t})`;this._debug(i,"begin",n,`broadcast = ${r}`);try{this.broadcastChannel&&r&&this.broadcastChannel.postMessage({event:t,session:n});const s=[],o=Array.from(this.stateChangeEmitters.values()).map(async a=>{try{await a.callback(t,n)}catch(l){s.push(l)}});if(await Promise.all(o),s.length>0){for(let a=0;a<s.length;a+=1)console.error(s[a]);throw s[0]}}finally{this._debug(i,"end")}}async _saveSession(t){this._debug("#_saveSession()",t),this.suppressGetSessionWarning=!0,await Km(this.storage,this.storageKey,t)}async _removeSession(){this._debug("#_removeSession()"),await Ls(this.storage,this.storageKey),await this._notifyAllSubscribers("SIGNED_OUT",null)}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");const t=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{t&&ot()&&(window!=null&&window.removeEventListener)&&window.removeEventListener("visibilitychange",t)}catch(n){console.error("removing visibilitychange callback failed",n)}}async _startAutoRefresh(){await this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");const t=setInterval(()=>this._autoRefreshTokenTick(),ar);this.autoRefreshTicker=t,t&&typeof t=="object"&&typeof t.unref=="function"?t.unref():typeof Deno<"u"&&typeof Deno.unrefTimer=="function"&&Deno.unrefTimer(t),setTimeout(async()=>{await this.initializePromise,await this._autoRefreshTokenTick()},0)}async _stopAutoRefresh(){this._debug("#_stopAutoRefresh()");const t=this.autoRefreshTicker;this.autoRefreshTicker=null,t&&clearInterval(t)}async startAutoRefresh(){this._removeVisibilityChangedCallback(),await this._startAutoRefresh()}async stopAutoRefresh(){this._removeVisibilityChangedCallback(),await this._stopAutoRefresh()}async _autoRefreshTokenTick(){this._debug("#_autoRefreshTokenTick()","begin");try{await this._acquireLock(0,async()=>{try{const t=Date.now();try{return await this._useSession(async n=>{const{data:{session:r}}=n;if(!r||!r.refresh_token||!r.expires_at){this._debug("#_autoRefreshTokenTick()","no session");return}const i=Math.floor((r.expires_at*1e3-t)/ar);this._debug("#_autoRefreshTokenTick()",`access token expires in ${i} ticks, a tick lasts ${ar}ms, refresh threshold is ${gu} ticks`),i<=gu&&await this._callRefreshToken(r.refresh_token)})}catch(n){console.error("Auto refresh tick failed with error. This is likely a transient error.",n)}}finally{this._debug("#_autoRefreshTokenTick()","end")}})}catch(t){if(t.isAcquireTimeout||t instanceof Nc)this._debug("auto refresh token tick lock not available");else throw t}}async _handleVisibilityChange(){if(this._debug("#_handleVisibilityChange()"),!ot()||!(window!=null&&window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=async()=>await this._onVisibilityChanged(!1),window==null||window.addEventListener("visibilitychange",this.visibilityChangedCallback),await this._onVisibilityChanged(!0)}catch(t){console.error("_handleVisibilityChange",t)}}async _onVisibilityChanged(t){const n=`#_onVisibilityChanged(${t})`;this._debug(n,"visibilityState",document.visibilityState),document.visibilityState==="visible"?(this.autoRefreshToken&&this._startAutoRefresh(),t||(await this.initializePromise,await this._acquireLock(-1,async()=>{if(document.visibilityState!=="visible"){this._debug(n,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting");return}await this._recoverAndRefresh()}))):document.visibilityState==="hidden"&&this.autoRefreshToken&&this._stopAutoRefresh()}async _getUrlForProvider(t,n,r){const i=[`provider=${encodeURIComponent(n)}`];if(r!=null&&r.redirectTo&&i.push(`redirect_to=${encodeURIComponent(r.redirectTo)}`),r!=null&&r.scopes&&i.push(`scopes=${encodeURIComponent(r.scopes)}`),this.flowType==="pkce"){const[s,o]=await sr(this.storage,this.storageKey),a=new URLSearchParams({code_challenge:`${encodeURIComponent(s)}`,code_challenge_method:`${encodeURIComponent(o)}`});i.push(a.toString())}if(r!=null&&r.queryParams){const s=new URLSearchParams(r.queryParams);i.push(s.toString())}return r!=null&&r.skipBrowserRedirect&&i.push(`skip_http_redirect=${r.skipBrowserRedirect}`),`${t}?${i.join("&")}`}async _unenroll(t){try{return await this._useSession(async n=>{var r;const{data:i,error:s}=n;return s?{data:null,error:s}:await V(this.fetch,"DELETE",`${this.url}/factors/${t.factorId}`,{headers:this.headers,jwt:(r=i==null?void 0:i.session)===null||r===void 0?void 0:r.access_token})})}catch(n){if(M(n))return{data:null,error:n};throw n}}async _enroll(t){try{return await this._useSession(async n=>{var r,i;const{data:s,error:o}=n;if(o)return{data:null,error:o};const a=Object.assign({friendly_name:t.friendlyName,factor_type:t.factorType},t.factorType==="phone"?{phone:t.phone}:{issuer:t.issuer}),{data:l,error:u}=await V(this.fetch,"POST",`${this.url}/factors`,{body:a,headers:this.headers,jwt:(r=s==null?void 0:s.session)===null||r===void 0?void 0:r.access_token});return u?{data:null,error:u}:(t.factorType==="totp"&&(!((i=l==null?void 0:l.totp)===null||i===void 0)&&i.qr_code)&&(l.totp.qr_code=`data:image/svg+xml;utf-8,${l.totp.qr_code}`),{data:l,error:null})})}catch(n){if(M(n))return{data:null,error:n};throw n}}async _verify(t){return this._acquireLock(-1,async()=>{try{return await this._useSession(async n=>{var r;const{data:i,error:s}=n;if(s)return{data:null,error:s};const{data:o,error:a}=await V(this.fetch,"POST",`${this.url}/factors/${t.factorId}/verify`,{body:{code:t.code,challenge_id:t.challengeId},headers:this.headers,jwt:(r=i==null?void 0:i.session)===null||r===void 0?void 0:r.access_token});return a?{data:null,error:a}:(await this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+o.expires_in},o)),await this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",o),{data:o,error:a})})}catch(n){if(M(n))return{data:null,error:n};throw n}})}async _challenge(t){return this._acquireLock(-1,async()=>{try{return await this._useSession(async n=>{var r;const{data:i,error:s}=n;return s?{data:null,error:s}:await V(this.fetch,"POST",`${this.url}/factors/${t.factorId}/challenge`,{body:{channel:t.channel},headers:this.headers,jwt:(r=i==null?void 0:i.session)===null||r===void 0?void 0:r.access_token})})}catch(n){if(M(n))return{data:null,error:n};throw n}})}async _challengeAndVerify(t){const{data:n,error:r}=await this._challenge({factorId:t.factorId});return r?{data:null,error:r}:await this._verify({factorId:t.factorId,challengeId:n.id,code:t.code})}async _listFactors(){const{data:{user:t},error:n}=await this.getUser();if(n)return{data:null,error:n};const r=(t==null?void 0:t.factors)||[],i=r.filter(o=>o.factor_type==="totp"&&o.status==="verified"),s=r.filter(o=>o.factor_type==="phone"&&o.status==="verified");return{data:{all:r,totp:i,phone:s},error:null}}async _getAuthenticatorAssuranceLevel(){return this._acquireLock(-1,async()=>await this._useSession(async t=>{var n,r;const{data:{session:i},error:s}=t;if(s)return{data:null,error:s};if(!i)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};const{payload:o}=Xa(i.access_token);let a=null;o.aal&&(a=o.aal);let l=a;((r=(n=i.user.factors)===null||n===void 0?void 0:n.filter(d=>d.status==="verified"))!==null&&r!==void 0?r:[]).length>0&&(l="aal2");const c=o.amr||[];return{data:{currentLevel:a,nextLevel:l,currentAuthenticationMethods:c},error:null}}))}async fetchJwk(t,n={keys:[]}){let r=n.keys.find(o=>o.kid===t);if(r||(r=this.jwks.keys.find(o=>o.kid===t),r&&this.jwks_cached_at+Q_>Date.now()))return r;const{data:i,error:s}=await V(this.fetch,"GET",`${this.url}/.well-known/jwks.json`,{headers:this.headers});if(s)throw s;if(!i.keys||i.keys.length===0)throw new Lr("JWKS is empty");if(this.jwks=i,this.jwks_cached_at=Date.now(),r=i.keys.find(o=>o.kid===t),!r)throw new Lr("No matching signing key found in JWKS");return r}async getClaims(t,n={keys:[]}){try{let r=t;if(!r){const{data:f,error:g}=await this.getSession();if(g||!f.session)return{data:null,error:g};r=f.session.access_token}const{header:i,payload:s,signature:o,raw:{header:a,payload:l}}=Xa(r);if(vS(s.exp),!i.kid||i.alg==="HS256"||!("crypto"in globalThis&&"subtle"in globalThis.crypto)){const{error:f}=await this.getUser(r);if(f)throw f;return{data:{claims:s,header:i,signature:o},error:null}}const u=yS(i.alg),c=await this.fetchJwk(i.kid,n),d=await crypto.subtle.importKey("jwk",c,u,!0,["verify"]);if(!await crypto.subtle.verify(u,d,o,rS(`${a}.${l}`)))throw new Lr("Invalid JWT signature");return{data:{claims:s,header:i,signature:o},error:null}}catch(r){if(M(r))return{data:null,error:r};throw r}}}Ur.nextInstanceID=0;const IS=Ic,Jm=Ur;class NS extends Jm{constructor(t){super(t)}}var MS=globalThis&&globalThis.__awaiter||function(e,t,n,r){function i(s){return s instanceof n?s:new n(function(o){o(s)})}return new(n||(n=Promise))(function(s,o){function a(c){try{u(r.next(c))}catch(d){o(d)}}function l(c){try{u(r.throw(c))}catch(d){o(d)}}function u(c){c.done?s(c.value):i(c.value).then(a,l)}u((r=r.apply(e,t||[])).next())})};class Qm{constructor(t,n,r){var i,s,o;if(this.supabaseUrl=t,this.supabaseKey=n,!t)throw new Error("supabaseUrl is required.");if(!n)throw new Error("supabaseKey is required.");const a=H_(t),l=new URL(a);this.realtimeUrl=new URL("realtime/v1",l),this.realtimeUrl.protocol=this.realtimeUrl.protocol.replace("http","ws"),this.authUrl=new URL("auth/v1",l),this.storageUrl=new URL("storage/v1",l),this.functionsUrl=new URL("functions/v1",l);const u=`sb-${l.hostname.split(".")[0]}-auth-token`,c={db:M_,realtime:V_,auth:Object.assign(Object.assign({},D_),{storageKey:u}),global:N_},d=W_(r??{},c);this.storageKey=(i=d.auth.storageKey)!==null&&i!==void 0?i:"",this.headers=(s=d.global.headers)!==null&&s!==void 0?s:{},d.accessToken?(this.accessToken=d.accessToken,this.auth=new Proxy({},{get:(h,f)=>{throw new Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(f)} is not possible`)}})):this.auth=this._initSupabaseAuthClient((o=d.auth)!==null&&o!==void 0?o:{},this.headers,d.global.fetch),this.fetch=U_(n,this._getAccessToken.bind(this),d.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},d.realtime)),this.rest=new r_(new URL("rest/v1",l).href,{headers:this.headers,schema:d.db.schema,fetch:this.fetch}),d.accessToken||this._listenForAuthEvents()}get functions(){return new Ox(this.functionsUrl.href,{headers:this.headers,customFetch:this.fetch})}get storage(){return new L_(this.storageUrl.href,this.headers,this.fetch)}from(t){return this.rest.from(t)}schema(t){return this.rest.schema(t)}rpc(t,n={},r={}){return this.rest.rpc(t,n,r)}channel(t,n={config:{}}){return this.realtime.channel(t,n)}getChannels(){return this.realtime.getChannels()}removeChannel(t){return this.realtime.removeChannel(t)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var t,n;return MS(this,void 0,void 0,function*(){if(this.accessToken)return yield this.accessToken();const{data:r}=yield this.auth.getSession();return(n=(t=r.session)===null||t===void 0?void 0:t.access_token)!==null&&n!==void 0?n:null})}_initSupabaseAuthClient({autoRefreshToken:t,persistSession:n,detectSessionInUrl:r,storage:i,storageKey:s,flowType:o,lock:a,debug:l},u,c){const d={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new NS({url:this.authUrl.href,headers:Object.assign(Object.assign({},d),u),storageKey:s,autoRefreshToken:t,persistSession:n,detectSessionInUrl:r,storage:i,flowType:o,lock:a,debug:l,fetch:c,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(t){return new Nm(this.realtimeUrl.href,Object.assign(Object.assign({},t),{params:Object.assign({apikey:this.supabaseKey},t==null?void 0:t.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((n,r)=>{this._handleTokenChanged(n,"CLIENT",r==null?void 0:r.access_token)})}_handleTokenChanged(t,n,r){(t==="TOKEN_REFRESHED"||t==="SIGNED_IN")&&this.changedAccessToken!==r?this.changedAccessToken=r:t==="SIGNED_OUT"&&(this.realtime.setAuth(),n=="STORAGE"&&this.auth.signOut(),this.changedAccessToken=void 0)}}const Ym=(e,t,n)=>new Qm(e,t,n),Zb=Object.freeze(Object.defineProperty({__proto__:null,AuthAdminApi:IS,AuthApiError:zm,AuthClient:Jm,AuthError:ca,AuthImplicitGrantRedirectError:vi,AuthInvalidCredentialsError:gi,AuthInvalidJwtError:Lr,AuthInvalidTokenResponseError:mi,AuthPKCEGrantCodeExchangeError:yu,AuthRetryableFetchError:To,AuthSessionMissingError:Pt,AuthUnknownError:Oc,AuthWeakPasswordError:wu,CustomAuthError:Ft,get FunctionRegion(){return bo},FunctionsError:na,FunctionsFetchError:vm,FunctionsHttpError:wm,FunctionsRelayError:ym,GoTrueAdminApi:Ic,GoTrueClient:Ur,NavigatorLockAcquireTimeoutError:Gm,PostgrestError:i_,REALTIME_CHANNEL_STATES:y_,get REALTIME_LISTEN_TYPES(){return fu},get REALTIME_POSTGRES_CHANGES_LISTEN_EVENT(){return hu},get REALTIME_PRESENCE_LISTEN_EVENTS(){return du},get REALTIME_SUBSCRIBE_STATES(){return vt},RealtimeChannel:ua,RealtimeClient:Nm,RealtimePresence:Rr,SIGN_OUT_SCOPES:Qs,SupabaseClient:Qm,createClient:Ym,isAuthApiError:Bm,isAuthError:M,isAuthImplicitGrantRedirectError:Fm,isAuthRetryableFetchError:Js,isAuthSessionMissingError:Um,isAuthWeakPasswordError:Y_,lockInternals:Nn,navigatorLock:qm,processLock:LS},Symbol.toStringTag,{value:"Module"})),DS="https://jpvbtrzvbpyzgtpvltss.supabase.co",VS="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpwdmJ0cnp2YnB5emd0cHZsdHNzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA5MTM1MzAsImV4cCI6MjA2NjQ4OTUzMH0.TTEAu4XUOXRW-gBvs1qSlSx92fnW7apyMY_KTnQiUbI",Dh=Ym(DS,VS);class $S{setToken(t,n=!0){localStorage.removeItem("auth_token"),sessionStorage.removeItem("auth_token"),n?localStorage.setItem("auth_token",t):sessionStorage.setItem("auth_token",t)}getToken(){return localStorage.getItem("auth_token")||sessionStorage.getItem("auth_token")}clearToken(){localStorage.removeItem("auth_token"),sessionStorage.removeItem("auth_token")}async signUp(t,n,r){try{const s=await(await fetch("/api/auth/signup",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:t,password:n,name:r})})).json();return s.success&&s.token&&this.setToken(s.token,!0),s}catch{return{success:!1,error:"Network error during signup"}}}async signIn(t,n,r=!0){try{const s=await(await fetch("/api/auth/login",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:t,password:n})})).json();return s.success&&s.token&&this.setToken(s.token,r),s}catch{return{success:!1,error:"Network error during login"}}}async signOut(){try{const t=this.getToken();t&&await fetch("/api/auth/logout",{method:"POST",headers:{Authorization:`Bearer ${t}`}})}finally{this.clearToken()}}async getCurrentUser(){try{const t=this.getToken();if(!t)return null;const n=await fetch("/api/auth/user",{headers:{Authorization:`Bearer ${t}`,"Content-Type":"application/json"}});if(!n.ok)return n.status===401&&(console.log("Token expired or invalid, clearing..."),this.clearToken()),null;const r=await n.json();return r.success&&r.data?r.data:null}catch(t){return console.error("getCurrentUser error:",t),null}}isAuthenticated(){return!!this.getToken()}async signInWithGoogle(){try{const{error:t}=await Dh.auth.signInWithOAuth({provider:"google",options:{redirectTo:`${window.location.origin}/auth/callback`}});return t?{error:t.message}:{}}catch{return{error:"Failed to sign in with Google"}}}async handleOAuthCallback(){var t,n;try{const{data:r,error:i}=await Dh.auth.getSession();if(i)return{success:!1,error:i.message};if(r.session){this.setToken(r.session.access_token,!0);const s=r.session.user;if(s){const a=await(await fetch("/api/auth/oauth-user",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${r.session.access_token}`},body:JSON.stringify({id:s.id,email:s.email,name:((t=s.user_metadata)==null?void 0:t.full_name)||((n=s.user_metadata)==null?void 0:n.name),provider:"google"})})).json();return a.success?{success:!0,user:a.user}:{success:!1,error:a.error||"Failed to create user profile"}}}return{success:!1,error:"No session found"}}catch{return{success:!1,error:"Failed to handle OAuth callback"}}}}const St=new $S,Xr=Ax((e,t)=>({user:null,isLoading:!0,isAuthenticated:!1,login:async(n,r,i=!0)=>{e({isLoading:!0});try{const s=await St.signIn(n,r,i);return s.success&&s.user?(e({user:s.user,isAuthenticated:!0,isLoading:!1}),{success:!0}):(e({isLoading:!1}),{success:!1,error:s.error||"Login failed"})}catch{return e({isLoading:!1}),{success:!1,error:"Network error"}}},signup:async(n,r,i)=>{e({isLoading:!0});try{const s=await St.signUp(n,r,i);return s.success&&s.user?(e({user:s.user,isAuthenticated:!0,isLoading:!1}),{success:!0}):(e({isLoading:!1}),{success:!1,error:s.error||"Signup failed"})}catch{return e({isLoading:!1}),{success:!1,error:"Network error"}}},signInWithGoogle:async()=>{e({isLoading:!0});try{const n=await St.signInWithGoogle();return n.error?(e({isLoading:!1}),{success:!1,error:n.error}):{success:!0}}catch{return e({isLoading:!1}),{success:!1,error:"Failed to initiate Google sign in"}}},handleOAuthCallback:async()=>{e({isLoading:!0});try{const n=await St.handleOAuthCallback();return n.success&&n.user?(e({user:n.user,isAuthenticated:!0,isLoading:!1}),{success:!0,user:n.user}):(e({isLoading:!1}),{success:!1,error:n.error||"OAuth callback failed"})}catch{return e({isLoading:!1}),{success:!1,error:"Network error during OAuth callback"}}},logout:async()=>{e({isLoading:!0});try{await St.signOut()}finally{e({user:null,isAuthenticated:!1,isLoading:!1})}},checkAuth:async()=>{e({isLoading:!0});try{if(!St.isAuthenticated()){e({user:null,isAuthenticated:!1,isLoading:!1});return}const r=await St.getCurrentUser();r?e({user:r,isAuthenticated:!0,isLoading:!1}):(await St.signOut(),e({user:null,isAuthenticated:!1,isLoading:!1}))}catch(n){console.error("Auth check failed:",n),await St.signOut(),e({user:null,isAuthenticated:!1,isLoading:!1})}},updateUser:n=>{const{user:r}=t();r&&e({user:{...r,...n}})}})),Xm=_.createContext({transformPagePoint:e=>e,isStatic:!1,reducedMotion:"never"}),ha=_.createContext({}),fa=_.createContext(null),pa=typeof document<"u",Mc=pa?_.useLayoutEffect:_.useEffect,Zm=_.createContext({strict:!1}),Dc=e=>e.replace(/([a-z])([A-Z])/g,"$1-$2").toLowerCase(),zS="framerAppearId",eg="data-"+Dc(zS);function BS(e,t,n,r){const{visualElement:i}=_.useContext(ha),s=_.useContext(Zm),o=_.useContext(fa),a=_.useContext(Xm).reducedMotion,l=_.useRef();r=r||s.renderer,!l.current&&r&&(l.current=r(e,{visualState:t,parent:i,props:n,presenceContext:o,blockInitialAnimation:o?o.initial===!1:!1,reducedMotionConfig:a}));const u=l.current;_.useInsertionEffect(()=>{u&&u.update(n,o)});const c=_.useRef(!!(n[eg]&&!window.HandoffComplete));return Mc(()=>{u&&(u.render(),c.current&&u.animationState&&u.animationState.animateChanges())}),_.useEffect(()=>{u&&(u.updateFeatures(),!c.current&&u.animationState&&u.animationState.animateChanges(),c.current&&(c.current=!1,window.HandoffComplete=!0))}),u}function xr(e){return e&&typeof e=="object"&&Object.prototype.hasOwnProperty.call(e,"current")}function US(e,t,n){return _.useCallback(r=>{r&&e.mount&&e.mount(r),t&&(r?t.mount(r):t.unmount()),n&&(typeof n=="function"?n(r):xr(n)&&(n.current=r))},[t])}function Zi(e){return typeof e=="string"||Array.isArray(e)}function ma(e){return e!==null&&typeof e=="object"&&typeof e.start=="function"}const Vc=["animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"],$c=["initial",...Vc];function ga(e){return ma(e.animate)||$c.some(t=>Zi(e[t]))}function tg(e){return!!(ga(e)||e.variants)}function FS(e,t){if(ga(e)){const{initial:n,animate:r}=e;return{initial:n===!1||Zi(n)?n:void 0,animate:Zi(r)?r:void 0}}return e.inherit!==!1?t:{}}function HS(e){const{initial:t,animate:n}=FS(e,_.useContext(ha));return _.useMemo(()=>({initial:t,animate:n}),[Vh(t),Vh(n)])}function Vh(e){return Array.isArray(e)?e.join(" "):e}const $h={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},es={};for(const e in $h)es[e]={isEnabled:t=>$h[e].some(n=>!!t[n])};function WS(e){for(const t in e)es[t]={...es[t],...e[t]}}const zc=_.createContext({}),ng=_.createContext({}),KS=Symbol.for("motionComponentSymbol");function GS({preloadedFeatures:e,createVisualElement:t,useRender:n,useVisualState:r,Component:i}){e&&WS(e);function s(a,l){let u;const c={..._.useContext(Xm),...a,layoutId:qS(a)},{isStatic:d}=c,h=HS(a),f=r(a,d);if(!d&&pa){h.visualElement=BS(i,f,c,t);const g=_.useContext(ng),w=_.useContext(Zm).strict;h.visualElement&&(u=h.visualElement.loadFeatures(c,w,e,g))}return _.createElement(ha.Provider,{value:h},u&&h.visualElement?_.createElement(u,{visualElement:h.visualElement,...c}):null,n(i,a,US(f,h.visualElement,l),f,d,h.visualElement))}const o=_.forwardRef(s);return o[KS]=i,o}function qS({layoutId:e}){const t=_.useContext(zc).id;return t&&e!==void 0?t+"-"+e:e}function JS(e){function t(r,i={}){return GS(e(r,i))}if(typeof Proxy>"u")return t;const n=new Map;return new Proxy(t,{get:(r,i)=>(n.has(i)||n.set(i,t(i)),n.get(i))})}const QS=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Bc(e){return typeof e!="string"||e.includes("-")?!1:!!(QS.indexOf(e)>-1||/[A-Z]/.test(e))}const Ao={};function YS(e){Object.assign(Ao,e)}const us=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],er=new Set(us);function rg(e,{layout:t,layoutId:n}){return er.has(e)||e.startsWith("origin")||(t||n!==void 0)&&(!!Ao[e]||e==="opacity")}const Ve=e=>!!(e&&e.getVelocity),XS={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},ZS=us.length;function ek(e,{enableHardwareAcceleration:t=!0,allowTransformNone:n=!0},r,i){let s="";for(let o=0;o<ZS;o++){const a=us[o];if(e[a]!==void 0){const l=XS[a]||a;s+=`${l}(${e[a]}) `}}return t&&!e.z&&(s+="translateZ(0)"),s=s.trim(),i?s=i(e,r?"":s):n&&r&&(s="none"),s}const ig=e=>t=>typeof t=="string"&&t.startsWith(e),sg=ig("--"),xu=ig("var(--"),tk=/var\s*\(\s*--[\w-]+(\s*,\s*(?:(?:[^)(]|\((?:[^)(]+|\([^)(]*\))*\))*)+)?\s*\)/g,nk=(e,t)=>t&&typeof e=="number"?t.transform(e):e,yn=(e,t,n)=>Math.min(Math.max(n,e),t),tr={test:e=>typeof e=="number",parse:parseFloat,transform:e=>e},Ai={...tr,transform:e=>yn(0,1,e)},Os={...tr,default:1},Ri=e=>Math.round(e*1e5)/1e5,va=/(-)?([\d]*\.?[\d])+/g,og=/(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))/gi,rk=/^(#[0-9a-f]{3,8}|(rgb|hsl)a?\((-?[\d\.]+%?[,\s]+){2}(-?[\d\.]+%?)\s*[\,\/]?\s*[\d\.]*%?\))$/i;function cs(e){return typeof e=="string"}const ds=e=>({test:t=>cs(t)&&t.endsWith(e)&&t.split(" ").length===1,parse:parseFloat,transform:t=>`${t}${e}`}),Kt=ds("deg"),_t=ds("%"),R=ds("px"),ik=ds("vh"),sk=ds("vw"),zh={..._t,parse:e=>_t.parse(e)/100,transform:e=>_t.transform(e*100)},Bh={...tr,transform:Math.round},ag={borderWidth:R,borderTopWidth:R,borderRightWidth:R,borderBottomWidth:R,borderLeftWidth:R,borderRadius:R,radius:R,borderTopLeftRadius:R,borderTopRightRadius:R,borderBottomRightRadius:R,borderBottomLeftRadius:R,width:R,maxWidth:R,height:R,maxHeight:R,size:R,top:R,right:R,bottom:R,left:R,padding:R,paddingTop:R,paddingRight:R,paddingBottom:R,paddingLeft:R,margin:R,marginTop:R,marginRight:R,marginBottom:R,marginLeft:R,rotate:Kt,rotateX:Kt,rotateY:Kt,rotateZ:Kt,scale:Os,scaleX:Os,scaleY:Os,scaleZ:Os,skew:Kt,skewX:Kt,skewY:Kt,distance:R,translateX:R,translateY:R,translateZ:R,x:R,y:R,z:R,perspective:R,transformPerspective:R,opacity:Ai,originX:zh,originY:zh,originZ:R,zIndex:Bh,fillOpacity:Ai,strokeOpacity:Ai,numOctaves:Bh};function Uc(e,t,n,r){const{style:i,vars:s,transform:o,transformOrigin:a}=e;let l=!1,u=!1,c=!0;for(const d in t){const h=t[d];if(sg(d)){s[d]=h;continue}const f=ag[d],g=nk(h,f);if(er.has(d)){if(l=!0,o[d]=g,!c)continue;h!==(f.default||0)&&(c=!1)}else d.startsWith("origin")?(u=!0,a[d]=g):i[d]=g}if(t.transform||(l||r?i.transform=ek(e.transform,n,c,r):i.transform&&(i.transform="none")),u){const{originX:d="50%",originY:h="50%",originZ:f=0}=a;i.transformOrigin=`${d} ${h} ${f}`}}const Fc=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function lg(e,t,n){for(const r in t)!Ve(t[r])&&!rg(r,n)&&(e[r]=t[r])}function ok({transformTemplate:e},t,n){return _.useMemo(()=>{const r=Fc();return Uc(r,t,{enableHardwareAcceleration:!n},e),Object.assign({},r.vars,r.style)},[t])}function ak(e,t,n){const r=e.style||{},i={};return lg(i,r,e),Object.assign(i,ok(e,t,n)),e.transformValues?e.transformValues(i):i}function lk(e,t,n){const r={},i=ak(e,t,n);return e.drag&&e.dragListener!==!1&&(r.draggable=!1,i.userSelect=i.WebkitUserSelect=i.WebkitTouchCallout="none",i.touchAction=e.drag===!0?"none":`pan-${e.drag==="x"?"y":"x"}`),e.tabIndex===void 0&&(e.onTap||e.onTapStart||e.whileTap)&&(r.tabIndex=0),r.style=i,r}const uk=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","transformValues","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function Ro(e){return e.startsWith("while")||e.startsWith("drag")&&e!=="draggable"||e.startsWith("layout")||e.startsWith("onTap")||e.startsWith("onPan")||e.startsWith("onLayout")||uk.has(e)}let ug=e=>!Ro(e);function ck(e){e&&(ug=t=>t.startsWith("on")?!Ro(t):e(t))}try{ck(require("@emotion/is-prop-valid").default)}catch{}function dk(e,t,n){const r={};for(const i in e)i==="values"&&typeof e.values=="object"||(ug(i)||n===!0&&Ro(i)||!t&&!Ro(i)||e.draggable&&i.startsWith("onDrag"))&&(r[i]=e[i]);return r}function Uh(e,t,n){return typeof e=="string"?e:R.transform(t+n*e)}function hk(e,t,n){const r=Uh(t,e.x,e.width),i=Uh(n,e.y,e.height);return`${r} ${i}`}const fk={offset:"stroke-dashoffset",array:"stroke-dasharray"},pk={offset:"strokeDashoffset",array:"strokeDasharray"};function mk(e,t,n=1,r=0,i=!0){e.pathLength=1;const s=i?fk:pk;e[s.offset]=R.transform(-r);const o=R.transform(t),a=R.transform(n);e[s.array]=`${o} ${a}`}function Hc(e,{attrX:t,attrY:n,attrScale:r,originX:i,originY:s,pathLength:o,pathSpacing:a=1,pathOffset:l=0,...u},c,d,h){if(Uc(e,u,c,h),d){e.style.viewBox&&(e.attrs.viewBox=e.style.viewBox);return}e.attrs=e.style,e.style={};const{attrs:f,style:g,dimensions:w}=e;f.transform&&(w&&(g.transform=f.transform),delete f.transform),w&&(i!==void 0||s!==void 0||g.transform)&&(g.transformOrigin=hk(w,i!==void 0?i:.5,s!==void 0?s:.5)),t!==void 0&&(f.x=t),n!==void 0&&(f.y=n),r!==void 0&&(f.scale=r),o!==void 0&&mk(f,o,a,l,!1)}const cg=()=>({...Fc(),attrs:{}}),Wc=e=>typeof e=="string"&&e.toLowerCase()==="svg";function gk(e,t,n,r){const i=_.useMemo(()=>{const s=cg();return Hc(s,t,{enableHardwareAcceleration:!1},Wc(r),e.transformTemplate),{...s.attrs,style:{...s.style}}},[t]);if(e.style){const s={};lg(s,e.style,e),i.style={...s,...i.style}}return i}function vk(e=!1){return(n,r,i,{latestValues:s},o)=>{const l=(Bc(n)?gk:lk)(r,s,o,n),c={...dk(r,typeof n=="string",e),...l,ref:i},{children:d}=r,h=_.useMemo(()=>Ve(d)?d.get():d,[d]);return _.createElement(n,{...c,children:h})}}function dg(e,{style:t,vars:n},r,i){Object.assign(e.style,t,i&&i.getProjectionStyles(r));for(const s in n)e.style.setProperty(s,n[s])}const hg=new Set(["baseFrequency","diffuseConstant","kernelMatrix","kernelUnitLength","keySplines","keyTimes","limitingConeAngle","markerHeight","markerWidth","numOctaves","targetX","targetY","surfaceScale","specularConstant","specularExponent","stdDeviation","tableValues","viewBox","gradientTransform","pathLength","startOffset","textLength","lengthAdjust"]);function fg(e,t,n,r){dg(e,t,void 0,r);for(const i in t.attrs)e.setAttribute(hg.has(i)?i:Dc(i),t.attrs[i])}function Kc(e,t){const{style:n}=e,r={};for(const i in n)(Ve(n[i])||t.style&&Ve(t.style[i])||rg(i,e))&&(r[i]=n[i]);return r}function pg(e,t){const n=Kc(e,t);for(const r in e)if(Ve(e[r])||Ve(t[r])){const i=us.indexOf(r)!==-1?"attr"+r.charAt(0).toUpperCase()+r.substring(1):r;n[i]=e[r]}return n}function Gc(e,t,n,r={},i={}){return typeof t=="function"&&(t=t(n!==void 0?n:e.custom,r,i)),typeof t=="string"&&(t=e.variants&&e.variants[t]),typeof t=="function"&&(t=t(n!==void 0?n:e.custom,r,i)),t}function mg(e){const t=_.useRef(null);return t.current===null&&(t.current=e()),t.current}const Lo=e=>Array.isArray(e),yk=e=>!!(e&&typeof e=="object"&&e.mix&&e.toValue),wk=e=>Lo(e)?e[e.length-1]||0:e;function Ys(e){const t=Ve(e)?e.get():e;return yk(t)?t.toValue():t}function xk({scrapeMotionValuesFromProps:e,createRenderState:t,onMount:n},r,i,s){const o={latestValues:_k(r,i,s,e),renderState:t()};return n&&(o.mount=a=>n(r,a,o)),o}const gg=e=>(t,n)=>{const r=_.useContext(ha),i=_.useContext(fa),s=()=>xk(e,t,r,i);return n?s():mg(s)};function _k(e,t,n,r){const i={},s=r(e,{});for(const h in s)i[h]=Ys(s[h]);let{initial:o,animate:a}=e;const l=ga(e),u=tg(e);t&&u&&!l&&e.inherit!==!1&&(o===void 0&&(o=t.initial),a===void 0&&(a=t.animate));let c=n?n.initial===!1:!1;c=c||o===!1;const d=c?a:o;return d&&typeof d!="boolean"&&!ma(d)&&(Array.isArray(d)?d:[d]).forEach(f=>{const g=Gc(e,f);if(!g)return;const{transitionEnd:w,transition:x,...y}=g;for(const p in y){let m=y[p];if(Array.isArray(m)){const S=c?m.length-1:0;m=m[S]}m!==null&&(i[p]=m)}for(const p in w)i[p]=w[p]}),i}const te=e=>e;class Fh{constructor(){this.order=[],this.scheduled=new Set}add(t){if(!this.scheduled.has(t))return this.scheduled.add(t),this.order.push(t),!0}remove(t){const n=this.order.indexOf(t);n!==-1&&(this.order.splice(n,1),this.scheduled.delete(t))}clear(){this.order.length=0,this.scheduled.clear()}}function Sk(e){let t=new Fh,n=new Fh,r=0,i=!1,s=!1;const o=new WeakSet,a={schedule:(l,u=!1,c=!1)=>{const d=c&&i,h=d?t:n;return u&&o.add(l),h.add(l)&&d&&i&&(r=t.order.length),l},cancel:l=>{n.remove(l),o.delete(l)},process:l=>{if(i){s=!0;return}if(i=!0,[t,n]=[n,t],n.clear(),r=t.order.length,r)for(let u=0;u<r;u++){const c=t.order[u];c(l),o.has(c)&&(a.schedule(c),e())}i=!1,s&&(s=!1,a.process(l))}};return a}const Is=["prepare","read","update","preRender","render","postRender"],kk=40;function Ck(e,t){let n=!1,r=!0;const i={delta:0,timestamp:0,isProcessing:!1},s=Is.reduce((d,h)=>(d[h]=Sk(()=>n=!0),d),{}),o=d=>s[d].process(i),a=()=>{const d=performance.now();n=!1,i.delta=r?1e3/60:Math.max(Math.min(d-i.timestamp,kk),1),i.timestamp=d,i.isProcessing=!0,Is.forEach(o),i.isProcessing=!1,n&&t&&(r=!1,e(a))},l=()=>{n=!0,r=!0,i.isProcessing||e(a)};return{schedule:Is.reduce((d,h)=>{const f=s[h];return d[h]=(g,w=!1,x=!1)=>(n||l(),f.schedule(g,w,x)),d},{}),cancel:d=>Is.forEach(h=>s[h].cancel(d)),state:i,steps:s}}const{schedule:H,cancel:$t,state:_e,steps:Za}=Ck(typeof requestAnimationFrame<"u"?requestAnimationFrame:te,!0),Ek={useVisualState:gg({scrapeMotionValuesFromProps:pg,createRenderState:cg,onMount:(e,t,{renderState:n,latestValues:r})=>{H.read(()=>{try{n.dimensions=typeof t.getBBox=="function"?t.getBBox():t.getBoundingClientRect()}catch{n.dimensions={x:0,y:0,width:0,height:0}}}),H.render(()=>{Hc(n,r,{enableHardwareAcceleration:!1},Wc(t.tagName),e.transformTemplate),fg(t,n)})}})},bk={useVisualState:gg({scrapeMotionValuesFromProps:Kc,createRenderState:Fc})};function Pk(e,{forwardMotionProps:t=!1},n,r){return{...Bc(e)?Ek:bk,preloadedFeatures:n,useRender:vk(t),createVisualElement:r,Component:e}}function Rt(e,t,n,r={passive:!0}){return e.addEventListener(t,n,r),()=>e.removeEventListener(t,n)}const vg=e=>e.pointerType==="mouse"?typeof e.button!="number"||e.button<=0:e.isPrimary!==!1;function ya(e,t="page"){return{point:{x:e[t+"X"],y:e[t+"Y"]}}}const Tk=e=>t=>vg(t)&&e(t,ya(t));function Ot(e,t,n,r){return Rt(e,t,Tk(n),r)}const jk=(e,t)=>n=>t(e(n)),fn=(...e)=>e.reduce(jk);function yg(e){let t=null;return()=>{const n=()=>{t=null};return t===null?(t=e,n):!1}}const Hh=yg("dragHorizontal"),Wh=yg("dragVertical");function wg(e){let t=!1;if(e==="y")t=Wh();else if(e==="x")t=Hh();else{const n=Hh(),r=Wh();n&&r?t=()=>{n(),r()}:(n&&n(),r&&r())}return t}function xg(){const e=wg(!0);return e?(e(),!1):!0}class bn{constructor(t){this.isMounted=!1,this.node=t}update(){}}function Kh(e,t){const n="pointer"+(t?"enter":"leave"),r="onHover"+(t?"Start":"End"),i=(s,o)=>{if(s.pointerType==="touch"||xg())return;const a=e.getProps();e.animationState&&a.whileHover&&e.animationState.setActive("whileHover",t),a[r]&&H.update(()=>a[r](s,o))};return Ot(e.current,n,i,{passive:!e.getProps()[r]})}class Ak extends bn{mount(){this.unmount=fn(Kh(this.node,!0),Kh(this.node,!1))}unmount(){}}class Rk extends bn{constructor(){super(...arguments),this.isActive=!1}onFocus(){let t=!1;try{t=this.node.current.matches(":focus-visible")}catch{t=!0}!t||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!0),this.isActive=!0)}onBlur(){!this.isActive||!this.node.animationState||(this.node.animationState.setActive("whileFocus",!1),this.isActive=!1)}mount(){this.unmount=fn(Rt(this.node.current,"focus",()=>this.onFocus()),Rt(this.node.current,"blur",()=>this.onBlur()))}unmount(){}}const _g=(e,t)=>t?e===t?!0:_g(e,t.parentElement):!1;function el(e,t){if(!t)return;const n=new PointerEvent("pointer"+e);t(n,ya(n))}class Lk extends bn{constructor(){super(...arguments),this.removeStartListeners=te,this.removeEndListeners=te,this.removeAccessibleListeners=te,this.startPointerPress=(t,n)=>{if(this.isPressing)return;this.removeEndListeners();const r=this.node.getProps(),s=Ot(window,"pointerup",(a,l)=>{if(!this.checkPressEnd())return;const{onTap:u,onTapCancel:c,globalTapTarget:d}=this.node.getProps();H.update(()=>{!d&&!_g(this.node.current,a.target)?c&&c(a,l):u&&u(a,l)})},{passive:!(r.onTap||r.onPointerUp)}),o=Ot(window,"pointercancel",(a,l)=>this.cancelPress(a,l),{passive:!(r.onTapCancel||r.onPointerCancel)});this.removeEndListeners=fn(s,o),this.startPress(t,n)},this.startAccessiblePress=()=>{const t=s=>{if(s.key!=="Enter"||this.isPressing)return;const o=a=>{a.key!=="Enter"||!this.checkPressEnd()||el("up",(l,u)=>{const{onTap:c}=this.node.getProps();c&&H.update(()=>c(l,u))})};this.removeEndListeners(),this.removeEndListeners=Rt(this.node.current,"keyup",o),el("down",(a,l)=>{this.startPress(a,l)})},n=Rt(this.node.current,"keydown",t),r=()=>{this.isPressing&&el("cancel",(s,o)=>this.cancelPress(s,o))},i=Rt(this.node.current,"blur",r);this.removeAccessibleListeners=fn(n,i)}}startPress(t,n){this.isPressing=!0;const{onTapStart:r,whileTap:i}=this.node.getProps();i&&this.node.animationState&&this.node.animationState.setActive("whileTap",!0),r&&H.update(()=>r(t,n))}checkPressEnd(){return this.removeEndListeners(),this.isPressing=!1,this.node.getProps().whileTap&&this.node.animationState&&this.node.animationState.setActive("whileTap",!1),!xg()}cancelPress(t,n){if(!this.checkPressEnd())return;const{onTapCancel:r}=this.node.getProps();r&&H.update(()=>r(t,n))}mount(){const t=this.node.getProps(),n=Ot(t.globalTapTarget?window:this.node.current,"pointerdown",this.startPointerPress,{passive:!(t.onTapStart||t.onPointerStart)}),r=Rt(this.node.current,"focus",this.startAccessiblePress);this.removeStartListeners=fn(n,r)}unmount(){this.removeStartListeners(),this.removeEndListeners(),this.removeAccessibleListeners()}}const _u=new WeakMap,tl=new WeakMap,Ok=e=>{const t=_u.get(e.target);t&&t(e)},Ik=e=>{e.forEach(Ok)};function Nk({root:e,...t}){const n=e||document;tl.has(n)||tl.set(n,{});const r=tl.get(n),i=JSON.stringify(t);return r[i]||(r[i]=new IntersectionObserver(Ik,{root:e,...t})),r[i]}function Mk(e,t,n){const r=Nk(t);return _u.set(e,n),r.observe(e),()=>{_u.delete(e),r.unobserve(e)}}const Dk={some:0,all:1};class Vk extends bn{constructor(){super(...arguments),this.hasEnteredView=!1,this.isInView=!1}startObserver(){this.unmount();const{viewport:t={}}=this.node.getProps(),{root:n,margin:r,amount:i="some",once:s}=t,o={root:n?n.current:void 0,rootMargin:r,threshold:typeof i=="number"?i:Dk[i]},a=l=>{const{isIntersecting:u}=l;if(this.isInView===u||(this.isInView=u,s&&!u&&this.hasEnteredView))return;u&&(this.hasEnteredView=!0),this.node.animationState&&this.node.animationState.setActive("whileInView",u);const{onViewportEnter:c,onViewportLeave:d}=this.node.getProps(),h=u?c:d;h&&h(l)};return Mk(this.node.current,o,a)}mount(){this.startObserver()}update(){if(typeof IntersectionObserver>"u")return;const{props:t,prevProps:n}=this.node;["amount","margin","root"].some($k(t,n))&&this.startObserver()}unmount(){}}function $k({viewport:e={}},{viewport:t={}}={}){return n=>e[n]!==t[n]}const zk={inView:{Feature:Vk},tap:{Feature:Lk},focus:{Feature:Rk},hover:{Feature:Ak}};function Sg(e,t){if(!Array.isArray(t))return!1;const n=t.length;if(n!==e.length)return!1;for(let r=0;r<n;r++)if(t[r]!==e[r])return!1;return!0}function Bk(e){const t={};return e.values.forEach((n,r)=>t[r]=n.get()),t}function Uk(e){const t={};return e.values.forEach((n,r)=>t[r]=n.getVelocity()),t}function wa(e,t,n){const r=e.getProps();return Gc(r,t,n!==void 0?n:r.custom,Bk(e),Uk(e))}let Fk=te,qc=te;const pn=e=>e*1e3,It=e=>e/1e3,Hk={current:!1},kg=e=>Array.isArray(e)&&typeof e[0]=="number";function Cg(e){return!!(!e||typeof e=="string"&&Eg[e]||kg(e)||Array.isArray(e)&&e.every(Cg))}const yi=([e,t,n,r])=>`cubic-bezier(${e}, ${t}, ${n}, ${r})`,Eg={linear:"linear",ease:"ease",easeIn:"ease-in",easeOut:"ease-out",easeInOut:"ease-in-out",circIn:yi([0,.65,.55,1]),circOut:yi([.55,0,1,.45]),backIn:yi([.31,.01,.66,-.59]),backOut:yi([.33,1.53,.69,.99])};function bg(e){if(e)return kg(e)?yi(e):Array.isArray(e)?e.map(bg):Eg[e]}function Wk(e,t,n,{delay:r=0,duration:i,repeat:s=0,repeatType:o="loop",ease:a,times:l}={}){const u={[t]:n};l&&(u.offset=l);const c=bg(a);return Array.isArray(c)&&(u.easing=c),e.animate(u,{delay:r,duration:i,easing:Array.isArray(c)?"linear":c,fill:"both",iterations:s+1,direction:o==="reverse"?"alternate":"normal"})}function Kk(e,{repeat:t,repeatType:n="loop"}){const r=t&&n!=="loop"&&t%2===1?0:e.length-1;return e[r]}const Pg=(e,t,n)=>(((1-3*n+3*t)*e+(3*n-6*t))*e+3*t)*e,Gk=1e-7,qk=12;function Jk(e,t,n,r,i){let s,o,a=0;do o=t+(n-t)/2,s=Pg(o,r,i)-e,s>0?n=o:t=o;while(Math.abs(s)>Gk&&++a<qk);return o}function hs(e,t,n,r){if(e===t&&n===r)return te;const i=s=>Jk(s,0,1,e,n);return s=>s===0||s===1?s:Pg(i(s),t,r)}const Qk=hs(.42,0,1,1),Yk=hs(0,0,.58,1),Tg=hs(.42,0,.58,1),Xk=e=>Array.isArray(e)&&typeof e[0]!="number",jg=e=>t=>t<=.5?e(2*t)/2:(2-e(2*(1-t)))/2,Ag=e=>t=>1-e(1-t),Jc=e=>1-Math.sin(Math.acos(e)),Rg=Ag(Jc),Zk=jg(Jc),Lg=hs(.33,1.53,.69,.99),Qc=Ag(Lg),e2=jg(Qc),t2=e=>(e*=2)<1?.5*Qc(e):.5*(2-Math.pow(2,-10*(e-1))),n2={linear:te,easeIn:Qk,easeInOut:Tg,easeOut:Yk,circIn:Jc,circInOut:Zk,circOut:Rg,backIn:Qc,backInOut:e2,backOut:Lg,anticipate:t2},Gh=e=>{if(Array.isArray(e)){qc(e.length===4);const[t,n,r,i]=e;return hs(t,n,r,i)}else if(typeof e=="string")return n2[e];return e},Yc=(e,t)=>n=>!!(cs(n)&&rk.test(n)&&n.startsWith(e)||t&&Object.prototype.hasOwnProperty.call(n,t)),Og=(e,t,n)=>r=>{if(!cs(r))return r;const[i,s,o,a]=r.match(va);return{[e]:parseFloat(i),[t]:parseFloat(s),[n]:parseFloat(o),alpha:a!==void 0?parseFloat(a):1}},r2=e=>yn(0,255,e),nl={...tr,transform:e=>Math.round(r2(e))},Fn={test:Yc("rgb","red"),parse:Og("red","green","blue"),transform:({red:e,green:t,blue:n,alpha:r=1})=>"rgba("+nl.transform(e)+", "+nl.transform(t)+", "+nl.transform(n)+", "+Ri(Ai.transform(r))+")"};function i2(e){let t="",n="",r="",i="";return e.length>5?(t=e.substring(1,3),n=e.substring(3,5),r=e.substring(5,7),i=e.substring(7,9)):(t=e.substring(1,2),n=e.substring(2,3),r=e.substring(3,4),i=e.substring(4,5),t+=t,n+=n,r+=r,i+=i),{red:parseInt(t,16),green:parseInt(n,16),blue:parseInt(r,16),alpha:i?parseInt(i,16)/255:1}}const Su={test:Yc("#"),parse:i2,transform:Fn.transform},_r={test:Yc("hsl","hue"),parse:Og("hue","saturation","lightness"),transform:({hue:e,saturation:t,lightness:n,alpha:r=1})=>"hsla("+Math.round(e)+", "+_t.transform(Ri(t))+", "+_t.transform(Ri(n))+", "+Ri(Ai.transform(r))+")"},Ee={test:e=>Fn.test(e)||Su.test(e)||_r.test(e),parse:e=>Fn.test(e)?Fn.parse(e):_r.test(e)?_r.parse(e):Su.parse(e),transform:e=>cs(e)?e:e.hasOwnProperty("red")?Fn.transform(e):_r.transform(e)},Y=(e,t,n)=>-n*e+n*t+e;function rl(e,t,n){return n<0&&(n+=1),n>1&&(n-=1),n<1/6?e+(t-e)*6*n:n<1/2?t:n<2/3?e+(t-e)*(2/3-n)*6:e}function s2({hue:e,saturation:t,lightness:n,alpha:r}){e/=360,t/=100,n/=100;let i=0,s=0,o=0;if(!t)i=s=o=n;else{const a=n<.5?n*(1+t):n+t-n*t,l=2*n-a;i=rl(l,a,e+1/3),s=rl(l,a,e),o=rl(l,a,e-1/3)}return{red:Math.round(i*255),green:Math.round(s*255),blue:Math.round(o*255),alpha:r}}const il=(e,t,n)=>{const r=e*e;return Math.sqrt(Math.max(0,n*(t*t-r)+r))},o2=[Su,Fn,_r],a2=e=>o2.find(t=>t.test(e));function qh(e){const t=a2(e);let n=t.parse(e);return t===_r&&(n=s2(n)),n}const Ig=(e,t)=>{const n=qh(e),r=qh(t),i={...n};return s=>(i.red=il(n.red,r.red,s),i.green=il(n.green,r.green,s),i.blue=il(n.blue,r.blue,s),i.alpha=Y(n.alpha,r.alpha,s),Fn.transform(i))};function l2(e){var t,n;return isNaN(e)&&cs(e)&&(((t=e.match(va))===null||t===void 0?void 0:t.length)||0)+(((n=e.match(og))===null||n===void 0?void 0:n.length)||0)>0}const Ng={regex:tk,countKey:"Vars",token:"${v}",parse:te},Mg={regex:og,countKey:"Colors",token:"${c}",parse:Ee.parse},Dg={regex:va,countKey:"Numbers",token:"${n}",parse:tr.parse};function sl(e,{regex:t,countKey:n,token:r,parse:i}){const s=e.tokenised.match(t);s&&(e["num"+n]=s.length,e.tokenised=e.tokenised.replace(t,r),e.values.push(...s.map(i)))}function Oo(e){const t=e.toString(),n={value:t,tokenised:t,values:[],numVars:0,numColors:0,numNumbers:0};return n.value.includes("var(--")&&sl(n,Ng),sl(n,Mg),sl(n,Dg),n}function Vg(e){return Oo(e).values}function $g(e){const{values:t,numColors:n,numVars:r,tokenised:i}=Oo(e),s=t.length;return o=>{let a=i;for(let l=0;l<s;l++)l<r?a=a.replace(Ng.token,o[l]):l<r+n?a=a.replace(Mg.token,Ee.transform(o[l])):a=a.replace(Dg.token,Ri(o[l]));return a}}const u2=e=>typeof e=="number"?0:e;function c2(e){const t=Vg(e);return $g(e)(t.map(u2))}const wn={test:l2,parse:Vg,createTransformer:$g,getAnimatableNone:c2},zg=(e,t)=>n=>`${n>0?t:e}`;function Bg(e,t){return typeof e=="number"?n=>Y(e,t,n):Ee.test(e)?Ig(e,t):e.startsWith("var(")?zg(e,t):Fg(e,t)}const Ug=(e,t)=>{const n=[...e],r=n.length,i=e.map((s,o)=>Bg(s,t[o]));return s=>{for(let o=0;o<r;o++)n[o]=i[o](s);return n}},d2=(e,t)=>{const n={...e,...t},r={};for(const i in n)e[i]!==void 0&&t[i]!==void 0&&(r[i]=Bg(e[i],t[i]));return i=>{for(const s in r)n[s]=r[s](i);return n}},Fg=(e,t)=>{const n=wn.createTransformer(t),r=Oo(e),i=Oo(t);return r.numVars===i.numVars&&r.numColors===i.numColors&&r.numNumbers>=i.numNumbers?fn(Ug(r.values,i.values),n):zg(e,t)},ts=(e,t,n)=>{const r=t-e;return r===0?1:(n-e)/r},Jh=(e,t)=>n=>Y(e,t,n);function h2(e){return typeof e=="number"?Jh:typeof e=="string"?Ee.test(e)?Ig:Fg:Array.isArray(e)?Ug:typeof e=="object"?d2:Jh}function f2(e,t,n){const r=[],i=n||h2(e[0]),s=e.length-1;for(let o=0;o<s;o++){let a=i(e[o],e[o+1]);if(t){const l=Array.isArray(t)?t[o]||te:t;a=fn(l,a)}r.push(a)}return r}function Hg(e,t,{clamp:n=!0,ease:r,mixer:i}={}){const s=e.length;if(qc(s===t.length),s===1)return()=>t[0];e[0]>e[s-1]&&(e=[...e].reverse(),t=[...t].reverse());const o=f2(t,r,i),a=o.length,l=u=>{let c=0;if(a>1)for(;c<e.length-2&&!(u<e[c+1]);c++);const d=ts(e[c],e[c+1],u);return o[c](d)};return n?u=>l(yn(e[0],e[s-1],u)):l}function p2(e,t){const n=e[e.length-1];for(let r=1;r<=t;r++){const i=ts(0,t,r);e.push(Y(n,1,i))}}function m2(e){const t=[0];return p2(t,e.length-1),t}function g2(e,t){return e.map(n=>n*t)}function v2(e,t){return e.map(()=>t||Tg).splice(0,e.length-1)}function Io({duration:e=300,keyframes:t,times:n,ease:r="easeInOut"}){const i=Xk(r)?r.map(Gh):Gh(r),s={done:!1,value:t[0]},o=g2(n&&n.length===t.length?n:m2(t),e),a=Hg(o,t,{ease:Array.isArray(i)?i:v2(t,i)});return{calculatedDuration:e,next:l=>(s.value=a(l),s.done=l>=e,s)}}function Wg(e,t){return t?e*(1e3/t):0}const y2=5;function Kg(e,t,n){const r=Math.max(t-y2,0);return Wg(n-e(r),t-r)}const ol=.001,w2=.01,Qh=10,x2=.05,_2=1;function S2({duration:e=800,bounce:t=.25,velocity:n=0,mass:r=1}){let i,s;Fk(e<=pn(Qh));let o=1-t;o=yn(x2,_2,o),e=yn(w2,Qh,It(e)),o<1?(i=u=>{const c=u*o,d=c*e,h=c-n,f=ku(u,o),g=Math.exp(-d);return ol-h/f*g},s=u=>{const d=u*o*e,h=d*n+n,f=Math.pow(o,2)*Math.pow(u,2)*e,g=Math.exp(-d),w=ku(Math.pow(u,2),o);return(-i(u)+ol>0?-1:1)*((h-f)*g)/w}):(i=u=>{const c=Math.exp(-u*e),d=(u-n)*e+1;return-ol+c*d},s=u=>{const c=Math.exp(-u*e),d=(n-u)*(e*e);return c*d});const a=5/e,l=C2(i,s,a);if(e=pn(e),isNaN(l))return{stiffness:100,damping:10,duration:e};{const u=Math.pow(l,2)*r;return{stiffness:u,damping:o*2*Math.sqrt(r*u),duration:e}}}const k2=12;function C2(e,t,n){let r=n;for(let i=1;i<k2;i++)r=r-e(r)/t(r);return r}function ku(e,t){return e*Math.sqrt(1-t*t)}const E2=["duration","bounce"],b2=["stiffness","damping","mass"];function Yh(e,t){return t.some(n=>e[n]!==void 0)}function P2(e){let t={velocity:0,stiffness:100,damping:10,mass:1,isResolvedFromDuration:!1,...e};if(!Yh(e,b2)&&Yh(e,E2)){const n=S2(e);t={...t,...n,mass:1},t.isResolvedFromDuration=!0}return t}function Gg({keyframes:e,restDelta:t,restSpeed:n,...r}){const i=e[0],s=e[e.length-1],o={done:!1,value:i},{stiffness:a,damping:l,mass:u,duration:c,velocity:d,isResolvedFromDuration:h}=P2({...r,velocity:-It(r.velocity||0)}),f=d||0,g=l/(2*Math.sqrt(a*u)),w=s-i,x=It(Math.sqrt(a/u)),y=Math.abs(w)<5;n||(n=y?.01:2),t||(t=y?.005:.5);let p;if(g<1){const m=ku(x,g);p=S=>{const k=Math.exp(-g*x*S);return s-k*((f+g*x*w)/m*Math.sin(m*S)+w*Math.cos(m*S))}}else if(g===1)p=m=>s-Math.exp(-x*m)*(w+(f+x*w)*m);else{const m=x*Math.sqrt(g*g-1);p=S=>{const k=Math.exp(-g*x*S),C=Math.min(m*S,300);return s-k*((f+g*x*w)*Math.sinh(C)+m*w*Math.cosh(C))/m}}return{calculatedDuration:h&&c||null,next:m=>{const S=p(m);if(h)o.done=m>=c;else{let k=f;m!==0&&(g<1?k=Kg(p,m,S):k=0);const C=Math.abs(k)<=n,E=Math.abs(s-S)<=t;o.done=C&&E}return o.value=o.done?s:S,o}}}function Xh({keyframes:e,velocity:t=0,power:n=.8,timeConstant:r=325,bounceDamping:i=10,bounceStiffness:s=500,modifyTarget:o,min:a,max:l,restDelta:u=.5,restSpeed:c}){const d=e[0],h={done:!1,value:d},f=b=>a!==void 0&&b<a||l!==void 0&&b>l,g=b=>a===void 0?l:l===void 0||Math.abs(a-b)<Math.abs(l-b)?a:l;let w=n*t;const x=d+w,y=o===void 0?x:o(x);y!==x&&(w=y-d);const p=b=>-w*Math.exp(-b/r),m=b=>y+p(b),S=b=>{const A=p(b),L=m(b);h.done=Math.abs(A)<=u,h.value=h.done?y:L};let k,C;const E=b=>{f(h.value)&&(k=b,C=Gg({keyframes:[h.value,g(h.value)],velocity:Kg(m,b,h.value),damping:i,stiffness:s,restDelta:u,restSpeed:c}))};return E(0),{calculatedDuration:null,next:b=>{let A=!1;return!C&&k===void 0&&(A=!0,S(b),E(b)),k!==void 0&&b>k?C.next(b-k):(!A&&S(b),h)}}}const T2=e=>{const t=({timestamp:n})=>e(n);return{start:()=>H.update(t,!0),stop:()=>$t(t),now:()=>_e.isProcessing?_e.timestamp:performance.now()}},Zh=2e4;function ef(e){let t=0;const n=50;let r=e.next(t);for(;!r.done&&t<Zh;)t+=n,r=e.next(t);return t>=Zh?1/0:t}const j2={decay:Xh,inertia:Xh,tween:Io,keyframes:Io,spring:Gg};function No({autoplay:e=!0,delay:t=0,driver:n=T2,keyframes:r,type:i="keyframes",repeat:s=0,repeatDelay:o=0,repeatType:a="loop",onPlay:l,onStop:u,onComplete:c,onUpdate:d,...h}){let f=1,g=!1,w,x;const y=()=>{x=new Promise(I=>{w=I})};y();let p;const m=j2[i]||Io;let S;m!==Io&&typeof r[0]!="number"&&(S=Hg([0,100],r,{clamp:!1}),r=[0,100]);const k=m({...h,keyframes:r});let C;a==="mirror"&&(C=m({...h,keyframes:[...r].reverse(),velocity:-(h.velocity||0)}));let E="idle",b=null,A=null,L=null;k.calculatedDuration===null&&s&&(k.calculatedDuration=ef(k));const{calculatedDuration:ue}=k;let pe=1/0,Ce=1/0;ue!==null&&(pe=ue+o,Ce=pe*(s+1)-o);let ce=0;const Ht=I=>{if(A===null)return;f>0&&(A=Math.min(A,I)),f<0&&(A=Math.min(I-Ce/f,A)),b!==null?ce=b:ce=Math.round(I-A)*f;const G=ce-t*(f>=0?1:-1),Pn=f>=0?G<0:G>Ce;ce=Math.max(G,0),E==="finished"&&b===null&&(ce=Ce);let pt=ce,nr=k;if(s){const xa=Math.min(ce,Ce)/pe;let fs=Math.floor(xa),jn=xa%1;!jn&&xa>=1&&(jn=1),jn===1&&fs--,fs=Math.min(fs,s+1),!!(fs%2)&&(a==="reverse"?(jn=1-jn,o&&(jn-=o/pe)):a==="mirror"&&(nr=C)),pt=yn(0,1,jn)*pe}const ze=Pn?{done:!1,value:r[0]}:nr.next(pt);S&&(ze.value=S(ze.value));let{done:Tn}=ze;!Pn&&ue!==null&&(Tn=f>=0?ce>=Ce:ce<=0);const Pv=b===null&&(E==="finished"||E==="running"&&Tn);return d&&d(ze.value),Pv&&T(),ze},ne=()=>{p&&p.stop(),p=void 0},Je=()=>{E="idle",ne(),w(),y(),A=L=null},T=()=>{E="finished",c&&c(),ne(),w()},O=()=>{if(g)return;p||(p=n(Ht));const I=p.now();l&&l(),b!==null?A=I-b:(!A||E==="finished")&&(A=I),E==="finished"&&y(),L=A,b=null,E="running",p.start()};e&&O();const D={then(I,G){return x.then(I,G)},get time(){return It(ce)},set time(I){I=pn(I),ce=I,b!==null||!p||f===0?b=I:A=p.now()-I/f},get duration(){const I=k.calculatedDuration===null?ef(k):k.calculatedDuration;return It(I)},get speed(){return f},set speed(I){I===f||!p||(f=I,D.time=It(ce))},get state(){return E},play:O,pause:()=>{E="paused",b=ce},stop:()=>{g=!0,E!=="idle"&&(E="idle",u&&u(),Je())},cancel:()=>{L!==null&&Ht(L),Je()},complete:()=>{E="finished"},sample:I=>(A=0,Ht(I))};return D}function A2(e){let t;return()=>(t===void 0&&(t=e()),t)}const R2=A2(()=>Object.hasOwnProperty.call(Element.prototype,"animate")),L2=new Set(["opacity","clipPath","filter","transform","backgroundColor"]),Ns=10,O2=2e4,I2=(e,t)=>t.type==="spring"||e==="backgroundColor"||!Cg(t.ease);function N2(e,t,{onUpdate:n,onComplete:r,...i}){if(!(R2()&&L2.has(t)&&!i.repeatDelay&&i.repeatType!=="mirror"&&i.damping!==0&&i.type!=="inertia"))return!1;let o=!1,a,l,u=!1;const c=()=>{l=new Promise(m=>{a=m})};c();let{keyframes:d,duration:h=300,ease:f,times:g}=i;if(I2(t,i)){const m=No({...i,repeat:0,delay:0});let S={done:!1,value:d[0]};const k=[];let C=0;for(;!S.done&&C<O2;)S=m.sample(C),k.push(S.value),C+=Ns;g=void 0,d=k,h=C-Ns,f="linear"}const w=Wk(e.owner.current,t,d,{...i,duration:h,ease:f,times:g}),x=()=>{u=!1,w.cancel()},y=()=>{u=!0,H.update(x),a(),c()};return w.onfinish=()=>{u||(e.set(Kk(d,i)),r&&r(),y())},{then(m,S){return l.then(m,S)},attachTimeline(m){return w.timeline=m,w.onfinish=null,te},get time(){return It(w.currentTime||0)},set time(m){w.currentTime=pn(m)},get speed(){return w.playbackRate},set speed(m){w.playbackRate=m},get duration(){return It(h)},play:()=>{o||(w.play(),$t(x))},pause:()=>w.pause(),stop:()=>{if(o=!0,w.playState==="idle")return;const{currentTime:m}=w;if(m){const S=No({...i,autoplay:!1});e.setWithVelocity(S.sample(m-Ns).value,S.sample(m).value,Ns)}y()},complete:()=>{u||w.finish()},cancel:y}}function M2({keyframes:e,delay:t,onUpdate:n,onComplete:r}){const i=()=>(n&&n(e[e.length-1]),r&&r(),{time:0,speed:1,duration:0,play:te,pause:te,stop:te,then:s=>(s(),Promise.resolve()),cancel:te,complete:te});return t?No({keyframes:[0,1],duration:0,delay:t,onComplete:i}):i()}const D2={type:"spring",stiffness:500,damping:25,restSpeed:10},V2=e=>({type:"spring",stiffness:550,damping:e===0?2*Math.sqrt(550):30,restSpeed:10}),$2={type:"keyframes",duration:.8},z2={type:"keyframes",ease:[.25,.1,.35,1],duration:.3},B2=(e,{keyframes:t})=>t.length>2?$2:er.has(e)?e.startsWith("scale")?V2(t[1]):D2:z2,Cu=(e,t)=>e==="zIndex"?!1:!!(typeof t=="number"||Array.isArray(t)||typeof t=="string"&&(wn.test(t)||t==="0")&&!t.startsWith("url(")),U2=new Set(["brightness","contrast","saturate","opacity"]);function F2(e){const[t,n]=e.slice(0,-1).split("(");if(t==="drop-shadow")return e;const[r]=n.match(va)||[];if(!r)return e;const i=n.replace(r,"");let s=U2.has(t)?1:0;return r!==n&&(s*=100),t+"("+s+i+")"}const H2=/([a-z-]*)\(.*?\)/g,Eu={...wn,getAnimatableNone:e=>{const t=e.match(H2);return t?t.map(F2).join(" "):e}},W2={...ag,color:Ee,backgroundColor:Ee,outlineColor:Ee,fill:Ee,stroke:Ee,borderColor:Ee,borderTopColor:Ee,borderRightColor:Ee,borderBottomColor:Ee,borderLeftColor:Ee,filter:Eu,WebkitFilter:Eu},Xc=e=>W2[e];function qg(e,t){let n=Xc(e);return n!==Eu&&(n=wn),n.getAnimatableNone?n.getAnimatableNone(t):void 0}const Jg=e=>/^0[^.\s]+$/.test(e);function K2(e){if(typeof e=="number")return e===0;if(e!==null)return e==="none"||e==="0"||Jg(e)}function G2(e,t,n,r){const i=Cu(t,n);let s;Array.isArray(n)?s=[...n]:s=[null,n];const o=r.from!==void 0?r.from:e.get();let a;const l=[];for(let u=0;u<s.length;u++)s[u]===null&&(s[u]=u===0?o:s[u-1]),K2(s[u])&&l.push(u),typeof s[u]=="string"&&s[u]!=="none"&&s[u]!=="0"&&(a=s[u]);if(i&&l.length&&a)for(let u=0;u<l.length;u++){const c=l[u];s[c]=qg(t,a)}return s}function q2({when:e,delay:t,delayChildren:n,staggerChildren:r,staggerDirection:i,repeat:s,repeatType:o,repeatDelay:a,from:l,elapsed:u,...c}){return!!Object.keys(c).length}function Zc(e,t){return e[t]||e.default||e}const J2={skipAnimations:!1},ed=(e,t,n,r={})=>i=>{const s=Zc(r,e)||{},o=s.delay||r.delay||0;let{elapsed:a=0}=r;a=a-pn(o);const l=G2(t,e,n,s),u=l[0],c=l[l.length-1],d=Cu(e,u),h=Cu(e,c);let f={keyframes:l,velocity:t.getVelocity(),ease:"easeOut",...s,delay:-a,onUpdate:g=>{t.set(g),s.onUpdate&&s.onUpdate(g)},onComplete:()=>{i(),s.onComplete&&s.onComplete()}};if(q2(s)||(f={...f,...B2(e,f)}),f.duration&&(f.duration=pn(f.duration)),f.repeatDelay&&(f.repeatDelay=pn(f.repeatDelay)),!d||!h||Hk.current||s.type===!1||J2.skipAnimations)return M2(f);if(!r.isHandoff&&t.owner&&t.owner.current instanceof HTMLElement&&!t.owner.getProps().onUpdate){const g=N2(t,e,f);if(g)return g}return No(f)};function Mo(e){return!!(Ve(e)&&e.add)}const Qg=e=>/^\-?\d*\.?\d+$/.test(e);function td(e,t){e.indexOf(t)===-1&&e.push(t)}function nd(e,t){const n=e.indexOf(t);n>-1&&e.splice(n,1)}class rd{constructor(){this.subscriptions=[]}add(t){return td(this.subscriptions,t),()=>nd(this.subscriptions,t)}notify(t,n,r){const i=this.subscriptions.length;if(i)if(i===1)this.subscriptions[0](t,n,r);else for(let s=0;s<i;s++){const o=this.subscriptions[s];o&&o(t,n,r)}}getSize(){return this.subscriptions.length}clear(){this.subscriptions.length=0}}const Q2=e=>!isNaN(parseFloat(e));class Y2{constructor(t,n={}){this.version="10.18.0",this.timeDelta=0,this.lastUpdated=0,this.canTrackVelocity=!1,this.events={},this.updateAndNotify=(r,i=!0)=>{this.prev=this.current,this.current=r;const{delta:s,timestamp:o}=_e;this.lastUpdated!==o&&(this.timeDelta=s,this.lastUpdated=o,H.postRender(this.scheduleVelocityCheck)),this.prev!==this.current&&this.events.change&&this.events.change.notify(this.current),this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()),i&&this.events.renderRequest&&this.events.renderRequest.notify(this.current)},this.scheduleVelocityCheck=()=>H.postRender(this.velocityCheck),this.velocityCheck=({timestamp:r})=>{r!==this.lastUpdated&&(this.prev=this.current,this.events.velocityChange&&this.events.velocityChange.notify(this.getVelocity()))},this.hasAnimated=!1,this.prev=this.current=t,this.canTrackVelocity=Q2(this.current),this.owner=n.owner}onChange(t){return this.on("change",t)}on(t,n){this.events[t]||(this.events[t]=new rd);const r=this.events[t].add(n);return t==="change"?()=>{r(),H.read(()=>{this.events.change.getSize()||this.stop()})}:r}clearListeners(){for(const t in this.events)this.events[t].clear()}attach(t,n){this.passiveEffect=t,this.stopPassiveEffect=n}set(t,n=!0){!n||!this.passiveEffect?this.updateAndNotify(t,n):this.passiveEffect(t,this.updateAndNotify)}setWithVelocity(t,n,r){this.set(n),this.prev=t,this.timeDelta=r}jump(t){this.updateAndNotify(t),this.prev=t,this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}get(){return this.current}getPrevious(){return this.prev}getVelocity(){return this.canTrackVelocity?Wg(parseFloat(this.current)-parseFloat(this.prev),this.timeDelta):0}start(t){return this.stop(),new Promise(n=>{this.hasAnimated=!0,this.animation=t(n),this.events.animationStart&&this.events.animationStart.notify()}).then(()=>{this.events.animationComplete&&this.events.animationComplete.notify(),this.clearAnimation()})}stop(){this.animation&&(this.animation.stop(),this.events.animationCancel&&this.events.animationCancel.notify()),this.clearAnimation()}isAnimating(){return!!this.animation}clearAnimation(){delete this.animation}destroy(){this.clearListeners(),this.stop(),this.stopPassiveEffect&&this.stopPassiveEffect()}}function Fr(e,t){return new Y2(e,t)}const Yg=e=>t=>t.test(e),X2={test:e=>e==="auto",parse:e=>e},Xg=[tr,R,_t,Kt,sk,ik,X2],li=e=>Xg.find(Yg(e)),Z2=[...Xg,Ee,wn],eC=e=>Z2.find(Yg(e));function tC(e,t,n){e.hasValue(t)?e.getValue(t).set(n):e.addValue(t,Fr(n))}function nC(e,t){const n=wa(e,t);let{transitionEnd:r={},transition:i={},...s}=n?e.makeTargetAnimatable(n,!1):{};s={...s,...r};for(const o in s){const a=wk(s[o]);tC(e,o,a)}}function rC(e,t,n){var r,i;const s=Object.keys(t).filter(a=>!e.hasValue(a)),o=s.length;if(o)for(let a=0;a<o;a++){const l=s[a],u=t[l];let c=null;Array.isArray(u)&&(c=u[0]),c===null&&(c=(i=(r=n[l])!==null&&r!==void 0?r:e.readValue(l))!==null&&i!==void 0?i:t[l]),c!=null&&(typeof c=="string"&&(Qg(c)||Jg(c))?c=parseFloat(c):!eC(c)&&wn.test(u)&&(c=qg(l,u)),e.addValue(l,Fr(c,{owner:e})),n[l]===void 0&&(n[l]=c),c!==null&&e.setBaseTarget(l,c))}}function iC(e,t){return t?(t[e]||t.default||t).from:void 0}function sC(e,t,n){const r={};for(const i in e){const s=iC(i,t);if(s!==void 0)r[i]=s;else{const o=n.getValue(i);o&&(r[i]=o.get())}}return r}function oC({protectedKeys:e,needsAnimating:t},n){const r=e.hasOwnProperty(n)&&t[n]!==!0;return t[n]=!1,r}function aC(e,t){const n=e.get();if(Array.isArray(t)){for(let r=0;r<t.length;r++)if(t[r]!==n)return!0}else return n!==t}function Zg(e,t,{delay:n=0,transitionOverride:r,type:i}={}){let{transition:s=e.getDefaultTransition(),transitionEnd:o,...a}=e.makeTargetAnimatable(t);const l=e.getValue("willChange");r&&(s=r);const u=[],c=i&&e.animationState&&e.animationState.getState()[i];for(const d in a){const h=e.getValue(d),f=a[d];if(!h||f===void 0||c&&oC(c,d))continue;const g={delay:n,elapsed:0,...Zc(s||{},d)};if(window.HandoffAppearAnimations){const y=e.getProps()[eg];if(y){const p=window.HandoffAppearAnimations(y,d,h,H);p!==null&&(g.elapsed=p,g.isHandoff=!0)}}let w=!g.isHandoff&&!aC(h,f);if(g.type==="spring"&&(h.getVelocity()||g.velocity)&&(w=!1),h.animation&&(w=!1),w)continue;h.start(ed(d,h,f,e.shouldReduceMotion&&er.has(d)?{type:!1}:g));const x=h.animation;Mo(l)&&(l.add(d),x.then(()=>l.remove(d))),u.push(x)}return o&&Promise.all(u).then(()=>{o&&nC(e,o)}),u}function bu(e,t,n={}){const r=wa(e,t,n.custom);let{transition:i=e.getDefaultTransition()||{}}=r||{};n.transitionOverride&&(i=n.transitionOverride);const s=r?()=>Promise.all(Zg(e,r,n)):()=>Promise.resolve(),o=e.variantChildren&&e.variantChildren.size?(l=0)=>{const{delayChildren:u=0,staggerChildren:c,staggerDirection:d}=i;return lC(e,t,u+l,c,d,n)}:()=>Promise.resolve(),{when:a}=i;if(a){const[l,u]=a==="beforeChildren"?[s,o]:[o,s];return l().then(()=>u())}else return Promise.all([s(),o(n.delay)])}function lC(e,t,n=0,r=0,i=1,s){const o=[],a=(e.variantChildren.size-1)*r,l=i===1?(u=0)=>u*r:(u=0)=>a-u*r;return Array.from(e.variantChildren).sort(uC).forEach((u,c)=>{u.notify("AnimationStart",t),o.push(bu(u,t,{...s,delay:n+l(c)}).then(()=>u.notify("AnimationComplete",t)))}),Promise.all(o)}function uC(e,t){return e.sortNodePosition(t)}function cC(e,t,n={}){e.notify("AnimationStart",t);let r;if(Array.isArray(t)){const i=t.map(s=>bu(e,s,n));r=Promise.all(i)}else if(typeof t=="string")r=bu(e,t,n);else{const i=typeof t=="function"?wa(e,t,n.custom):t;r=Promise.all(Zg(e,i,n))}return r.then(()=>e.notify("AnimationComplete",t))}const dC=[...Vc].reverse(),hC=Vc.length;function fC(e){return t=>Promise.all(t.map(({animation:n,options:r})=>cC(e,n,r)))}function pC(e){let t=fC(e);const n=gC();let r=!0;const i=(l,u)=>{const c=wa(e,u);if(c){const{transition:d,transitionEnd:h,...f}=c;l={...l,...f,...h}}return l};function s(l){t=l(e)}function o(l,u){const c=e.getProps(),d=e.getVariantContext(!0)||{},h=[],f=new Set;let g={},w=1/0;for(let y=0;y<hC;y++){const p=dC[y],m=n[p],S=c[p]!==void 0?c[p]:d[p],k=Zi(S),C=p===u?m.isActive:null;C===!1&&(w=y);let E=S===d[p]&&S!==c[p]&&k;if(E&&r&&e.manuallyAnimateOnMount&&(E=!1),m.protectedKeys={...g},!m.isActive&&C===null||!S&&!m.prevProp||ma(S)||typeof S=="boolean")continue;let A=mC(m.prevProp,S)||p===u&&m.isActive&&!E&&k||y>w&&k,L=!1;const ue=Array.isArray(S)?S:[S];let pe=ue.reduce(i,{});C===!1&&(pe={});const{prevResolvedValues:Ce={}}=m,ce={...Ce,...pe},Ht=ne=>{A=!0,f.has(ne)&&(L=!0,f.delete(ne)),m.needsAnimating[ne]=!0};for(const ne in ce){const Je=pe[ne],T=Ce[ne];if(g.hasOwnProperty(ne))continue;let O=!1;Lo(Je)&&Lo(T)?O=!Sg(Je,T):O=Je!==T,O?Je!==void 0?Ht(ne):f.add(ne):Je!==void 0&&f.has(ne)?Ht(ne):m.protectedKeys[ne]=!0}m.prevProp=S,m.prevResolvedValues=pe,m.isActive&&(g={...g,...pe}),r&&e.blockInitialAnimation&&(A=!1),A&&(!E||L)&&h.push(...ue.map(ne=>({animation:ne,options:{type:p,...l}})))}if(f.size){const y={};f.forEach(p=>{const m=e.getBaseTarget(p);m!==void 0&&(y[p]=m)}),h.push({animation:y})}let x=!!h.length;return r&&(c.initial===!1||c.initial===c.animate)&&!e.manuallyAnimateOnMount&&(x=!1),r=!1,x?t(h):Promise.resolve()}function a(l,u,c){var d;if(n[l].isActive===u)return Promise.resolve();(d=e.variantChildren)===null||d===void 0||d.forEach(f=>{var g;return(g=f.animationState)===null||g===void 0?void 0:g.setActive(l,u)}),n[l].isActive=u;const h=o(c,l);for(const f in n)n[f].protectedKeys={};return h}return{animateChanges:o,setActive:a,setAnimateFunction:s,getState:()=>n}}function mC(e,t){return typeof t=="string"?t!==e:Array.isArray(t)?!Sg(t,e):!1}function Rn(e=!1){return{isActive:e,protectedKeys:{},needsAnimating:{},prevResolvedValues:{}}}function gC(){return{animate:Rn(!0),whileInView:Rn(),whileHover:Rn(),whileTap:Rn(),whileDrag:Rn(),whileFocus:Rn(),exit:Rn()}}class vC extends bn{constructor(t){super(t),t.animationState||(t.animationState=pC(t))}updateAnimationControlsSubscription(){const{animate:t}=this.node.getProps();this.unmount(),ma(t)&&(this.unmount=t.subscribe(this.node))}mount(){this.updateAnimationControlsSubscription()}update(){const{animate:t}=this.node.getProps(),{animate:n}=this.node.prevProps||{};t!==n&&this.updateAnimationControlsSubscription()}unmount(){}}let yC=0;class wC extends bn{constructor(){super(...arguments),this.id=yC++}update(){if(!this.node.presenceContext)return;const{isPresent:t,onExitComplete:n,custom:r}=this.node.presenceContext,{isPresent:i}=this.node.prevPresenceContext||{};if(!this.node.animationState||t===i)return;const s=this.node.animationState.setActive("exit",!t,{custom:r??this.node.getProps().custom});n&&!t&&s.then(()=>n(this.id))}mount(){const{register:t}=this.node.presenceContext||{};t&&(this.unmount=t(this.id))}unmount(){}}const xC={animation:{Feature:vC},exit:{Feature:wC}},tf=(e,t)=>Math.abs(e-t);function _C(e,t){const n=tf(e.x,t.x),r=tf(e.y,t.y);return Math.sqrt(n**2+r**2)}class ev{constructor(t,n,{transformPagePoint:r,contextWindow:i,dragSnapToOrigin:s=!1}={}){if(this.startEvent=null,this.lastMoveEvent=null,this.lastMoveEventInfo=null,this.handlers={},this.contextWindow=window,this.updatePoint=()=>{if(!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const d=ll(this.lastMoveEventInfo,this.history),h=this.startEvent!==null,f=_C(d.offset,{x:0,y:0})>=3;if(!h&&!f)return;const{point:g}=d,{timestamp:w}=_e;this.history.push({...g,timestamp:w});const{onStart:x,onMove:y}=this.handlers;h||(x&&x(this.lastMoveEvent,d),this.startEvent=this.lastMoveEvent),y&&y(this.lastMoveEvent,d)},this.handlePointerMove=(d,h)=>{this.lastMoveEvent=d,this.lastMoveEventInfo=al(h,this.transformPagePoint),H.update(this.updatePoint,!0)},this.handlePointerUp=(d,h)=>{this.end();const{onEnd:f,onSessionEnd:g,resumeAnimation:w}=this.handlers;if(this.dragSnapToOrigin&&w&&w(),!(this.lastMoveEvent&&this.lastMoveEventInfo))return;const x=ll(d.type==="pointercancel"?this.lastMoveEventInfo:al(h,this.transformPagePoint),this.history);this.startEvent&&f&&f(d,x),g&&g(d,x)},!vg(t))return;this.dragSnapToOrigin=s,this.handlers=n,this.transformPagePoint=r,this.contextWindow=i||window;const o=ya(t),a=al(o,this.transformPagePoint),{point:l}=a,{timestamp:u}=_e;this.history=[{...l,timestamp:u}];const{onSessionStart:c}=n;c&&c(t,ll(a,this.history)),this.removeListeners=fn(Ot(this.contextWindow,"pointermove",this.handlePointerMove),Ot(this.contextWindow,"pointerup",this.handlePointerUp),Ot(this.contextWindow,"pointercancel",this.handlePointerUp))}updateHandlers(t){this.handlers=t}end(){this.removeListeners&&this.removeListeners(),$t(this.updatePoint)}}function al(e,t){return t?{point:t(e.point)}:e}function nf(e,t){return{x:e.x-t.x,y:e.y-t.y}}function ll({point:e},t){return{point:e,delta:nf(e,tv(t)),offset:nf(e,SC(t)),velocity:kC(t,.1)}}function SC(e){return e[0]}function tv(e){return e[e.length-1]}function kC(e,t){if(e.length<2)return{x:0,y:0};let n=e.length-1,r=null;const i=tv(e);for(;n>=0&&(r=e[n],!(i.timestamp-r.timestamp>pn(t)));)n--;if(!r)return{x:0,y:0};const s=It(i.timestamp-r.timestamp);if(s===0)return{x:0,y:0};const o={x:(i.x-r.x)/s,y:(i.y-r.y)/s};return o.x===1/0&&(o.x=0),o.y===1/0&&(o.y=0),o}function Ke(e){return e.max-e.min}function Pu(e,t=0,n=.01){return Math.abs(e-t)<=n}function rf(e,t,n,r=.5){e.origin=r,e.originPoint=Y(t.min,t.max,e.origin),e.scale=Ke(n)/Ke(t),(Pu(e.scale,1,1e-4)||isNaN(e.scale))&&(e.scale=1),e.translate=Y(n.min,n.max,e.origin)-e.originPoint,(Pu(e.translate)||isNaN(e.translate))&&(e.translate=0)}function Li(e,t,n,r){rf(e.x,t.x,n.x,r?r.originX:void 0),rf(e.y,t.y,n.y,r?r.originY:void 0)}function sf(e,t,n){e.min=n.min+t.min,e.max=e.min+Ke(t)}function CC(e,t,n){sf(e.x,t.x,n.x),sf(e.y,t.y,n.y)}function of(e,t,n){e.min=t.min-n.min,e.max=e.min+Ke(t)}function Oi(e,t,n){of(e.x,t.x,n.x),of(e.y,t.y,n.y)}function EC(e,{min:t,max:n},r){return t!==void 0&&e<t?e=r?Y(t,e,r.min):Math.max(e,t):n!==void 0&&e>n&&(e=r?Y(n,e,r.max):Math.min(e,n)),e}function af(e,t,n){return{min:t!==void 0?e.min+t:void 0,max:n!==void 0?e.max+n-(e.max-e.min):void 0}}function bC(e,{top:t,left:n,bottom:r,right:i}){return{x:af(e.x,n,i),y:af(e.y,t,r)}}function lf(e,t){let n=t.min-e.min,r=t.max-e.max;return t.max-t.min<e.max-e.min&&([n,r]=[r,n]),{min:n,max:r}}function PC(e,t){return{x:lf(e.x,t.x),y:lf(e.y,t.y)}}function TC(e,t){let n=.5;const r=Ke(e),i=Ke(t);return i>r?n=ts(t.min,t.max-r,e.min):r>i&&(n=ts(e.min,e.max-i,t.min)),yn(0,1,n)}function jC(e,t){const n={};return t.min!==void 0&&(n.min=t.min-e.min),t.max!==void 0&&(n.max=t.max-e.min),n}const Tu=.35;function AC(e=Tu){return e===!1?e=0:e===!0&&(e=Tu),{x:uf(e,"left","right"),y:uf(e,"top","bottom")}}function uf(e,t,n){return{min:cf(e,t),max:cf(e,n)}}function cf(e,t){return typeof e=="number"?e:e[t]||0}const df=()=>({translate:0,scale:1,origin:0,originPoint:0}),Sr=()=>({x:df(),y:df()}),hf=()=>({min:0,max:0}),se=()=>({x:hf(),y:hf()});function Ye(e){return[e("x"),e("y")]}function nv({top:e,left:t,right:n,bottom:r}){return{x:{min:t,max:n},y:{min:e,max:r}}}function RC({x:e,y:t}){return{top:t.min,right:e.max,bottom:t.max,left:e.min}}function LC(e,t){if(!t)return e;const n=t({x:e.left,y:e.top}),r=t({x:e.right,y:e.bottom});return{top:n.y,left:n.x,bottom:r.y,right:r.x}}function ul(e){return e===void 0||e===1}function ju({scale:e,scaleX:t,scaleY:n}){return!ul(e)||!ul(t)||!ul(n)}function Mn(e){return ju(e)||rv(e)||e.z||e.rotate||e.rotateX||e.rotateY}function rv(e){return ff(e.x)||ff(e.y)}function ff(e){return e&&e!=="0%"}function Do(e,t,n){const r=e-n,i=t*r;return n+i}function pf(e,t,n,r,i){return i!==void 0&&(e=Do(e,i,r)),Do(e,n,r)+t}function Au(e,t=0,n=1,r,i){e.min=pf(e.min,t,n,r,i),e.max=pf(e.max,t,n,r,i)}function iv(e,{x:t,y:n}){Au(e.x,t.translate,t.scale,t.originPoint),Au(e.y,n.translate,n.scale,n.originPoint)}function OC(e,t,n,r=!1){const i=n.length;if(!i)return;t.x=t.y=1;let s,o;for(let a=0;a<i;a++){s=n[a],o=s.projectionDelta;const l=s.instance;l&&l.style&&l.style.display==="contents"||(r&&s.options.layoutScroll&&s.scroll&&s!==s.root&&kr(e,{x:-s.scroll.offset.x,y:-s.scroll.offset.y}),o&&(t.x*=o.x.scale,t.y*=o.y.scale,iv(e,o)),r&&Mn(s.latestValues)&&kr(e,s.latestValues))}t.x=mf(t.x),t.y=mf(t.y)}function mf(e){return Number.isInteger(e)||e>1.0000000000001||e<.999999999999?e:1}function Qt(e,t){e.min=e.min+t,e.max=e.max+t}function gf(e,t,[n,r,i]){const s=t[i]!==void 0?t[i]:.5,o=Y(e.min,e.max,s);Au(e,t[n],t[r],o,t.scale)}const IC=["x","scaleX","originX"],NC=["y","scaleY","originY"];function kr(e,t){gf(e.x,t,IC),gf(e.y,t,NC)}function sv(e,t){return nv(LC(e.getBoundingClientRect(),t))}function MC(e,t,n){const r=sv(e,n),{scroll:i}=t;return i&&(Qt(r.x,i.offset.x),Qt(r.y,i.offset.y)),r}const ov=({current:e})=>e?e.ownerDocument.defaultView:null,DC=new WeakMap;class VC{constructor(t){this.openGlobalLock=null,this.isDragging=!1,this.currentDirection=null,this.originPoint={x:0,y:0},this.constraints=!1,this.hasMutatedConstraints=!1,this.elastic=se(),this.visualElement=t}start(t,{snapToCursor:n=!1}={}){const{presenceContext:r}=this.visualElement;if(r&&r.isPresent===!1)return;const i=c=>{const{dragSnapToOrigin:d}=this.getProps();d?this.pauseAnimation():this.stopAnimation(),n&&this.snapToCursor(ya(c,"page").point)},s=(c,d)=>{const{drag:h,dragPropagation:f,onDragStart:g}=this.getProps();if(h&&!f&&(this.openGlobalLock&&this.openGlobalLock(),this.openGlobalLock=wg(h),!this.openGlobalLock))return;this.isDragging=!0,this.currentDirection=null,this.resolveConstraints(),this.visualElement.projection&&(this.visualElement.projection.isAnimationBlocked=!0,this.visualElement.projection.target=void 0),Ye(x=>{let y=this.getAxisMotionValue(x).get()||0;if(_t.test(y)){const{projection:p}=this.visualElement;if(p&&p.layout){const m=p.layout.layoutBox[x];m&&(y=Ke(m)*(parseFloat(y)/100))}}this.originPoint[x]=y}),g&&H.update(()=>g(c,d),!1,!0);const{animationState:w}=this.visualElement;w&&w.setActive("whileDrag",!0)},o=(c,d)=>{const{dragPropagation:h,dragDirectionLock:f,onDirectionLock:g,onDrag:w}=this.getProps();if(!h&&!this.openGlobalLock)return;const{offset:x}=d;if(f&&this.currentDirection===null){this.currentDirection=$C(x),this.currentDirection!==null&&g&&g(this.currentDirection);return}this.updateAxis("x",d.point,x),this.updateAxis("y",d.point,x),this.visualElement.render(),w&&w(c,d)},a=(c,d)=>this.stop(c,d),l=()=>Ye(c=>{var d;return this.getAnimationState(c)==="paused"&&((d=this.getAxisMotionValue(c).animation)===null||d===void 0?void 0:d.play())}),{dragSnapToOrigin:u}=this.getProps();this.panSession=new ev(t,{onSessionStart:i,onStart:s,onMove:o,onSessionEnd:a,resumeAnimation:l},{transformPagePoint:this.visualElement.getTransformPagePoint(),dragSnapToOrigin:u,contextWindow:ov(this.visualElement)})}stop(t,n){const r=this.isDragging;if(this.cancel(),!r)return;const{velocity:i}=n;this.startAnimation(i);const{onDragEnd:s}=this.getProps();s&&H.update(()=>s(t,n))}cancel(){this.isDragging=!1;const{projection:t,animationState:n}=this.visualElement;t&&(t.isAnimationBlocked=!1),this.panSession&&this.panSession.end(),this.panSession=void 0;const{dragPropagation:r}=this.getProps();!r&&this.openGlobalLock&&(this.openGlobalLock(),this.openGlobalLock=null),n&&n.setActive("whileDrag",!1)}updateAxis(t,n,r){const{drag:i}=this.getProps();if(!r||!Ms(t,i,this.currentDirection))return;const s=this.getAxisMotionValue(t);let o=this.originPoint[t]+r[t];this.constraints&&this.constraints[t]&&(o=EC(o,this.constraints[t],this.elastic[t])),s.set(o)}resolveConstraints(){var t;const{dragConstraints:n,dragElastic:r}=this.getProps(),i=this.visualElement.projection&&!this.visualElement.projection.layout?this.visualElement.projection.measure(!1):(t=this.visualElement.projection)===null||t===void 0?void 0:t.layout,s=this.constraints;n&&xr(n)?this.constraints||(this.constraints=this.resolveRefConstraints()):n&&i?this.constraints=bC(i.layoutBox,n):this.constraints=!1,this.elastic=AC(r),s!==this.constraints&&i&&this.constraints&&!this.hasMutatedConstraints&&Ye(o=>{this.getAxisMotionValue(o)&&(this.constraints[o]=jC(i.layoutBox[o],this.constraints[o]))})}resolveRefConstraints(){const{dragConstraints:t,onMeasureDragConstraints:n}=this.getProps();if(!t||!xr(t))return!1;const r=t.current,{projection:i}=this.visualElement;if(!i||!i.layout)return!1;const s=MC(r,i.root,this.visualElement.getTransformPagePoint());let o=PC(i.layout.layoutBox,s);if(n){const a=n(RC(o));this.hasMutatedConstraints=!!a,a&&(o=nv(a))}return o}startAnimation(t){const{drag:n,dragMomentum:r,dragElastic:i,dragTransition:s,dragSnapToOrigin:o,onDragTransitionEnd:a}=this.getProps(),l=this.constraints||{},u=Ye(c=>{if(!Ms(c,n,this.currentDirection))return;let d=l&&l[c]||{};o&&(d={min:0,max:0});const h=i?200:1e6,f=i?40:1e7,g={type:"inertia",velocity:r?t[c]:0,bounceStiffness:h,bounceDamping:f,timeConstant:750,restDelta:1,restSpeed:10,...s,...d};return this.startAxisValueAnimation(c,g)});return Promise.all(u).then(a)}startAxisValueAnimation(t,n){const r=this.getAxisMotionValue(t);return r.start(ed(t,r,0,n))}stopAnimation(){Ye(t=>this.getAxisMotionValue(t).stop())}pauseAnimation(){Ye(t=>{var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.pause()})}getAnimationState(t){var n;return(n=this.getAxisMotionValue(t).animation)===null||n===void 0?void 0:n.state}getAxisMotionValue(t){const n="_drag"+t.toUpperCase(),r=this.visualElement.getProps(),i=r[n];return i||this.visualElement.getValue(t,(r.initial?r.initial[t]:void 0)||0)}snapToCursor(t){Ye(n=>{const{drag:r}=this.getProps();if(!Ms(n,r,this.currentDirection))return;const{projection:i}=this.visualElement,s=this.getAxisMotionValue(n);if(i&&i.layout){const{min:o,max:a}=i.layout.layoutBox[n];s.set(t[n]-Y(o,a,.5))}})}scalePositionWithinConstraints(){if(!this.visualElement.current)return;const{drag:t,dragConstraints:n}=this.getProps(),{projection:r}=this.visualElement;if(!xr(n)||!r||!this.constraints)return;this.stopAnimation();const i={x:0,y:0};Ye(o=>{const a=this.getAxisMotionValue(o);if(a){const l=a.get();i[o]=TC({min:l,max:l},this.constraints[o])}});const{transformTemplate:s}=this.visualElement.getProps();this.visualElement.current.style.transform=s?s({},""):"none",r.root&&r.root.updateScroll(),r.updateLayout(),this.resolveConstraints(),Ye(o=>{if(!Ms(o,t,null))return;const a=this.getAxisMotionValue(o),{min:l,max:u}=this.constraints[o];a.set(Y(l,u,i[o]))})}addListeners(){if(!this.visualElement.current)return;DC.set(this.visualElement,this);const t=this.visualElement.current,n=Ot(t,"pointerdown",l=>{const{drag:u,dragListener:c=!0}=this.getProps();u&&c&&this.start(l)}),r=()=>{const{dragConstraints:l}=this.getProps();xr(l)&&(this.constraints=this.resolveRefConstraints())},{projection:i}=this.visualElement,s=i.addEventListener("measure",r);i&&!i.layout&&(i.root&&i.root.updateScroll(),i.updateLayout()),r();const o=Rt(window,"resize",()=>this.scalePositionWithinConstraints()),a=i.addEventListener("didUpdate",({delta:l,hasLayoutChanged:u})=>{this.isDragging&&u&&(Ye(c=>{const d=this.getAxisMotionValue(c);d&&(this.originPoint[c]+=l[c].translate,d.set(d.get()+l[c].translate))}),this.visualElement.render())});return()=>{o(),n(),s(),a&&a()}}getProps(){const t=this.visualElement.getProps(),{drag:n=!1,dragDirectionLock:r=!1,dragPropagation:i=!1,dragConstraints:s=!1,dragElastic:o=Tu,dragMomentum:a=!0}=t;return{...t,drag:n,dragDirectionLock:r,dragPropagation:i,dragConstraints:s,dragElastic:o,dragMomentum:a}}}function Ms(e,t,n){return(t===!0||t===e)&&(n===null||n===e)}function $C(e,t=10){let n=null;return Math.abs(e.y)>t?n="y":Math.abs(e.x)>t&&(n="x"),n}class zC extends bn{constructor(t){super(t),this.removeGroupControls=te,this.removeListeners=te,this.controls=new VC(t)}mount(){const{dragControls:t}=this.node.getProps();t&&(this.removeGroupControls=t.subscribe(this.controls)),this.removeListeners=this.controls.addListeners()||te}unmount(){this.removeGroupControls(),this.removeListeners()}}const vf=e=>(t,n)=>{e&&H.update(()=>e(t,n))};class BC extends bn{constructor(){super(...arguments),this.removePointerDownListener=te}onPointerDown(t){this.session=new ev(t,this.createPanHandlers(),{transformPagePoint:this.node.getTransformPagePoint(),contextWindow:ov(this.node)})}createPanHandlers(){const{onPanSessionStart:t,onPanStart:n,onPan:r,onPanEnd:i}=this.node.getProps();return{onSessionStart:vf(t),onStart:vf(n),onMove:r,onEnd:(s,o)=>{delete this.session,i&&H.update(()=>i(s,o))}}}mount(){this.removePointerDownListener=Ot(this.node.current,"pointerdown",t=>this.onPointerDown(t))}update(){this.session&&this.session.updateHandlers(this.createPanHandlers())}unmount(){this.removePointerDownListener(),this.session&&this.session.end()}}function UC(){const e=_.useContext(fa);if(e===null)return[!0,null];const{isPresent:t,onExitComplete:n,register:r}=e,i=_.useId();return _.useEffect(()=>r(i),[]),!t&&n?[!1,()=>n&&n(i)]:[!0]}const Xs={hasAnimatedSinceResize:!0,hasEverUpdated:!1};function yf(e,t){return t.max===t.min?0:e/(t.max-t.min)*100}const ui={correct:(e,t)=>{if(!t.target)return e;if(typeof e=="string")if(R.test(e))e=parseFloat(e);else return e;const n=yf(e,t.target.x),r=yf(e,t.target.y);return`${n}% ${r}%`}},FC={correct:(e,{treeScale:t,projectionDelta:n})=>{const r=e,i=wn.parse(e);if(i.length>5)return r;const s=wn.createTransformer(e),o=typeof i[0]!="number"?1:0,a=n.x.scale*t.x,l=n.y.scale*t.y;i[0+o]/=a,i[1+o]/=l;const u=Y(a,l,.5);return typeof i[2+o]=="number"&&(i[2+o]/=u),typeof i[3+o]=="number"&&(i[3+o]/=u),s(i)}};class HC extends Fe.Component{componentDidMount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r,layoutId:i}=this.props,{projection:s}=t;YS(WC),s&&(n.group&&n.group.add(s),r&&r.register&&i&&r.register(s),s.root.didUpdate(),s.addEventListener("animationComplete",()=>{this.safeToRemove()}),s.setOptions({...s.options,onExitComplete:()=>this.safeToRemove()})),Xs.hasEverUpdated=!0}getSnapshotBeforeUpdate(t){const{layoutDependency:n,visualElement:r,drag:i,isPresent:s}=this.props,o=r.projection;return o&&(o.isPresent=s,i||t.layoutDependency!==n||n===void 0?o.willUpdate():this.safeToRemove(),t.isPresent!==s&&(s?o.promote():o.relegate()||H.postRender(()=>{const a=o.getStack();(!a||!a.members.length)&&this.safeToRemove()}))),null}componentDidUpdate(){const{projection:t}=this.props.visualElement;t&&(t.root.didUpdate(),queueMicrotask(()=>{!t.currentAnimation&&t.isLead()&&this.safeToRemove()}))}componentWillUnmount(){const{visualElement:t,layoutGroup:n,switchLayoutGroup:r}=this.props,{projection:i}=t;i&&(i.scheduleCheckAfterUnmount(),n&&n.group&&n.group.remove(i),r&&r.deregister&&r.deregister(i))}safeToRemove(){const{safeToRemove:t}=this.props;t&&t()}render(){return null}}function av(e){const[t,n]=UC(),r=_.useContext(zc);return Fe.createElement(HC,{...e,layoutGroup:r,switchLayoutGroup:_.useContext(ng),isPresent:t,safeToRemove:n})}const WC={borderRadius:{...ui,applyTo:["borderTopLeftRadius","borderTopRightRadius","borderBottomLeftRadius","borderBottomRightRadius"]},borderTopLeftRadius:ui,borderTopRightRadius:ui,borderBottomLeftRadius:ui,borderBottomRightRadius:ui,boxShadow:FC},lv=["TopLeft","TopRight","BottomLeft","BottomRight"],KC=lv.length,wf=e=>typeof e=="string"?parseFloat(e):e,xf=e=>typeof e=="number"||R.test(e);function GC(e,t,n,r,i,s){i?(e.opacity=Y(0,n.opacity!==void 0?n.opacity:1,qC(r)),e.opacityExit=Y(t.opacity!==void 0?t.opacity:1,0,JC(r))):s&&(e.opacity=Y(t.opacity!==void 0?t.opacity:1,n.opacity!==void 0?n.opacity:1,r));for(let o=0;o<KC;o++){const a=`border${lv[o]}Radius`;let l=_f(t,a),u=_f(n,a);if(l===void 0&&u===void 0)continue;l||(l=0),u||(u=0),l===0||u===0||xf(l)===xf(u)?(e[a]=Math.max(Y(wf(l),wf(u),r),0),(_t.test(u)||_t.test(l))&&(e[a]+="%")):e[a]=u}(t.rotate||n.rotate)&&(e.rotate=Y(t.rotate||0,n.rotate||0,r))}function _f(e,t){return e[t]!==void 0?e[t]:e.borderRadius}const qC=uv(0,.5,Rg),JC=uv(.5,.95,te);function uv(e,t,n){return r=>r<e?0:r>t?1:n(ts(e,t,r))}function Sf(e,t){e.min=t.min,e.max=t.max}function Qe(e,t){Sf(e.x,t.x),Sf(e.y,t.y)}function kf(e,t,n,r,i){return e-=t,e=Do(e,1/n,r),i!==void 0&&(e=Do(e,1/i,r)),e}function QC(e,t=0,n=1,r=.5,i,s=e,o=e){if(_t.test(t)&&(t=parseFloat(t),t=Y(o.min,o.max,t/100)-o.min),typeof t!="number")return;let a=Y(s.min,s.max,r);e===s&&(a-=t),e.min=kf(e.min,t,n,a,i),e.max=kf(e.max,t,n,a,i)}function Cf(e,t,[n,r,i],s,o){QC(e,t[n],t[r],t[i],t.scale,s,o)}const YC=["x","scaleX","originX"],XC=["y","scaleY","originY"];function Ef(e,t,n,r){Cf(e.x,t,YC,n?n.x:void 0,r?r.x:void 0),Cf(e.y,t,XC,n?n.y:void 0,r?r.y:void 0)}function bf(e){return e.translate===0&&e.scale===1}function cv(e){return bf(e.x)&&bf(e.y)}function ZC(e,t){return e.x.min===t.x.min&&e.x.max===t.x.max&&e.y.min===t.y.min&&e.y.max===t.y.max}function dv(e,t){return Math.round(e.x.min)===Math.round(t.x.min)&&Math.round(e.x.max)===Math.round(t.x.max)&&Math.round(e.y.min)===Math.round(t.y.min)&&Math.round(e.y.max)===Math.round(t.y.max)}function Pf(e){return Ke(e.x)/Ke(e.y)}class eE{constructor(){this.members=[]}add(t){td(this.members,t),t.scheduleRender()}remove(t){if(nd(this.members,t),t===this.prevLead&&(this.prevLead=void 0),t===this.lead){const n=this.members[this.members.length-1];n&&this.promote(n)}}relegate(t){const n=this.members.findIndex(i=>t===i);if(n===0)return!1;let r;for(let i=n;i>=0;i--){const s=this.members[i];if(s.isPresent!==!1){r=s;break}}return r?(this.promote(r),!0):!1}promote(t,n){const r=this.lead;if(t!==r&&(this.prevLead=r,this.lead=t,t.show(),r)){r.instance&&r.scheduleRender(),t.scheduleRender(),t.resumeFrom=r,n&&(t.resumeFrom.preserveOpacity=!0),r.snapshot&&(t.snapshot=r.snapshot,t.snapshot.latestValues=r.animationValues||r.latestValues),t.root&&t.root.isUpdating&&(t.isLayoutDirty=!0);const{crossfade:i}=t.options;i===!1&&r.hide()}}exitAnimationComplete(){this.members.forEach(t=>{const{options:n,resumingFrom:r}=t;n.onExitComplete&&n.onExitComplete(),r&&r.options.onExitComplete&&r.options.onExitComplete()})}scheduleRender(){this.members.forEach(t=>{t.instance&&t.scheduleRender(!1)})}removeLeadSnapshot(){this.lead&&this.lead.snapshot&&(this.lead.snapshot=void 0)}}function Tf(e,t,n){let r="";const i=e.x.translate/t.x,s=e.y.translate/t.y;if((i||s)&&(r=`translate3d(${i}px, ${s}px, 0) `),(t.x!==1||t.y!==1)&&(r+=`scale(${1/t.x}, ${1/t.y}) `),n){const{rotate:l,rotateX:u,rotateY:c}=n;l&&(r+=`rotate(${l}deg) `),u&&(r+=`rotateX(${u}deg) `),c&&(r+=`rotateY(${c}deg) `)}const o=e.x.scale*t.x,a=e.y.scale*t.y;return(o!==1||a!==1)&&(r+=`scale(${o}, ${a})`),r||"none"}const tE=(e,t)=>e.depth-t.depth;class nE{constructor(){this.children=[],this.isDirty=!1}add(t){td(this.children,t),this.isDirty=!0}remove(t){nd(this.children,t),this.isDirty=!0}forEach(t){this.isDirty&&this.children.sort(tE),this.isDirty=!1,this.children.forEach(t)}}function rE(e,t){const n=performance.now(),r=({timestamp:i})=>{const s=i-n;s>=t&&($t(r),e(s-t))};return H.read(r,!0),()=>$t(r)}function iE(e){window.MotionDebug&&window.MotionDebug.record(e)}function sE(e){return e instanceof SVGElement&&e.tagName!=="svg"}function oE(e,t,n){const r=Ve(e)?e:Fr(e);return r.start(ed("",r,t,n)),r.animation}const jf=["","X","Y","Z"],aE={visibility:"hidden"},Af=1e3;let lE=0;const Dn={type:"projectionFrame",totalNodes:0,resolvedTargetDeltas:0,recalculatedProjection:0};function hv({attachResizeListener:e,defaultParent:t,measureScroll:n,checkIsScrollRoot:r,resetTransform:i}){return class{constructor(o={},a=t==null?void 0:t()){this.id=lE++,this.animationId=0,this.children=new Set,this.options={},this.isTreeAnimating=!1,this.isAnimationBlocked=!1,this.isLayoutDirty=!1,this.isProjectionDirty=!1,this.isSharedProjectionDirty=!1,this.isTransformDirty=!1,this.updateManuallyBlocked=!1,this.updateBlockedByResize=!1,this.isUpdating=!1,this.isSVG=!1,this.needsReset=!1,this.shouldResetTransform=!1,this.treeScale={x:1,y:1},this.eventHandlers=new Map,this.hasTreeAnimated=!1,this.updateScheduled=!1,this.projectionUpdateScheduled=!1,this.checkUpdateFailed=()=>{this.isUpdating&&(this.isUpdating=!1,this.clearAllSnapshots())},this.updateProjection=()=>{this.projectionUpdateScheduled=!1,Dn.totalNodes=Dn.resolvedTargetDeltas=Dn.recalculatedProjection=0,this.nodes.forEach(dE),this.nodes.forEach(gE),this.nodes.forEach(vE),this.nodes.forEach(hE),iE(Dn)},this.hasProjected=!1,this.isVisible=!0,this.animationProgress=0,this.sharedNodes=new Map,this.latestValues=o,this.root=a?a.root||a:this,this.path=a?[...a.path,a]:[],this.parent=a,this.depth=a?a.depth+1:0;for(let l=0;l<this.path.length;l++)this.path[l].shouldResetTransform=!0;this.root===this&&(this.nodes=new nE)}addEventListener(o,a){return this.eventHandlers.has(o)||this.eventHandlers.set(o,new rd),this.eventHandlers.get(o).add(a)}notifyListeners(o,...a){const l=this.eventHandlers.get(o);l&&l.notify(...a)}hasListeners(o){return this.eventHandlers.has(o)}mount(o,a=this.root.hasTreeAnimated){if(this.instance)return;this.isSVG=sE(o),this.instance=o;const{layoutId:l,layout:u,visualElement:c}=this.options;if(c&&!c.current&&c.mount(o),this.root.nodes.add(this),this.parent&&this.parent.children.add(this),a&&(u||l)&&(this.isLayoutDirty=!0),e){let d;const h=()=>this.root.updateBlockedByResize=!1;e(o,()=>{this.root.updateBlockedByResize=!0,d&&d(),d=rE(h,250),Xs.hasAnimatedSinceResize&&(Xs.hasAnimatedSinceResize=!1,this.nodes.forEach(Lf))})}l&&this.root.registerSharedNode(l,this),this.options.animate!==!1&&c&&(l||u)&&this.addEventListener("didUpdate",({delta:d,hasLayoutChanged:h,hasRelativeTargetChanged:f,layout:g})=>{if(this.isTreeAnimationBlocked()){this.target=void 0,this.relativeTarget=void 0;return}const w=this.options.transition||c.getDefaultTransition()||SE,{onLayoutAnimationStart:x,onLayoutAnimationComplete:y}=c.getProps(),p=!this.targetLayout||!dv(this.targetLayout,g)||f,m=!h&&f;if(this.options.layoutRoot||this.resumeFrom&&this.resumeFrom.instance||m||h&&(p||!this.currentAnimation)){this.resumeFrom&&(this.resumingFrom=this.resumeFrom,this.resumingFrom.resumingFrom=void 0),this.setAnimationOrigin(d,m);const S={...Zc(w,"layout"),onPlay:x,onComplete:y};(c.shouldReduceMotion||this.options.layoutRoot)&&(S.delay=0,S.type=!1),this.startAnimation(S)}else h||Lf(this),this.isLead()&&this.options.onExitComplete&&this.options.onExitComplete();this.targetLayout=g})}unmount(){this.options.layoutId&&this.willUpdate(),this.root.nodes.remove(this);const o=this.getStack();o&&o.remove(this),this.parent&&this.parent.children.delete(this),this.instance=void 0,$t(this.updateProjection)}blockUpdate(){this.updateManuallyBlocked=!0}unblockUpdate(){this.updateManuallyBlocked=!1}isUpdateBlocked(){return this.updateManuallyBlocked||this.updateBlockedByResize}isTreeAnimationBlocked(){return this.isAnimationBlocked||this.parent&&this.parent.isTreeAnimationBlocked()||!1}startUpdate(){this.isUpdateBlocked()||(this.isUpdating=!0,this.nodes&&this.nodes.forEach(yE),this.animationId++)}getTransformTemplate(){const{visualElement:o}=this.options;return o&&o.getProps().transformTemplate}willUpdate(o=!0){if(this.root.hasTreeAnimated=!0,this.root.isUpdateBlocked()){this.options.onExitComplete&&this.options.onExitComplete();return}if(!this.root.isUpdating&&this.root.startUpdate(),this.isLayoutDirty)return;this.isLayoutDirty=!0;for(let c=0;c<this.path.length;c++){const d=this.path[c];d.shouldResetTransform=!0,d.updateScroll("snapshot"),d.options.layoutRoot&&d.willUpdate(!1)}const{layoutId:a,layout:l}=this.options;if(a===void 0&&!l)return;const u=this.getTransformTemplate();this.prevTransformTemplateValue=u?u(this.latestValues,""):void 0,this.updateSnapshot(),o&&this.notifyListeners("willUpdate")}update(){if(this.updateScheduled=!1,this.isUpdateBlocked()){this.unblockUpdate(),this.clearAllSnapshots(),this.nodes.forEach(Rf);return}this.isUpdating||this.nodes.forEach(pE),this.isUpdating=!1,this.nodes.forEach(mE),this.nodes.forEach(uE),this.nodes.forEach(cE),this.clearAllSnapshots();const a=performance.now();_e.delta=yn(0,1e3/60,a-_e.timestamp),_e.timestamp=a,_e.isProcessing=!0,Za.update.process(_e),Za.preRender.process(_e),Za.render.process(_e),_e.isProcessing=!1}didUpdate(){this.updateScheduled||(this.updateScheduled=!0,queueMicrotask(()=>this.update()))}clearAllSnapshots(){this.nodes.forEach(fE),this.sharedNodes.forEach(wE)}scheduleUpdateProjection(){this.projectionUpdateScheduled||(this.projectionUpdateScheduled=!0,H.preRender(this.updateProjection,!1,!0))}scheduleCheckAfterUnmount(){H.postRender(()=>{this.isLayoutDirty?this.root.didUpdate():this.root.checkUpdateFailed()})}updateSnapshot(){this.snapshot||!this.instance||(this.snapshot=this.measure())}updateLayout(){if(!this.instance||(this.updateScroll(),!(this.options.alwaysMeasureLayout&&this.isLead())&&!this.isLayoutDirty))return;if(this.resumeFrom&&!this.resumeFrom.instance)for(let l=0;l<this.path.length;l++)this.path[l].updateScroll();const o=this.layout;this.layout=this.measure(!1),this.layoutCorrected=se(),this.isLayoutDirty=!1,this.projectionDelta=void 0,this.notifyListeners("measure",this.layout.layoutBox);const{visualElement:a}=this.options;a&&a.notify("LayoutMeasure",this.layout.layoutBox,o?o.layoutBox:void 0)}updateScroll(o="measure"){let a=!!(this.options.layoutScroll&&this.instance);this.scroll&&this.scroll.animationId===this.root.animationId&&this.scroll.phase===o&&(a=!1),a&&(this.scroll={animationId:this.root.animationId,phase:o,isRoot:r(this.instance),offset:n(this.instance)})}resetTransform(){if(!i)return;const o=this.isLayoutDirty||this.shouldResetTransform,a=this.projectionDelta&&!cv(this.projectionDelta),l=this.getTransformTemplate(),u=l?l(this.latestValues,""):void 0,c=u!==this.prevTransformTemplateValue;o&&(a||Mn(this.latestValues)||c)&&(i(this.instance,u),this.shouldResetTransform=!1,this.scheduleRender())}measure(o=!0){const a=this.measurePageBox();let l=this.removeElementScroll(a);return o&&(l=this.removeTransform(l)),kE(l),{animationId:this.root.animationId,measuredBox:a,layoutBox:l,latestValues:{},source:this.id}}measurePageBox(){const{visualElement:o}=this.options;if(!o)return se();const a=o.measureViewportBox(),{scroll:l}=this.root;return l&&(Qt(a.x,l.offset.x),Qt(a.y,l.offset.y)),a}removeElementScroll(o){const a=se();Qe(a,o);for(let l=0;l<this.path.length;l++){const u=this.path[l],{scroll:c,options:d}=u;if(u!==this.root&&c&&d.layoutScroll){if(c.isRoot){Qe(a,o);const{scroll:h}=this.root;h&&(Qt(a.x,-h.offset.x),Qt(a.y,-h.offset.y))}Qt(a.x,c.offset.x),Qt(a.y,c.offset.y)}}return a}applyTransform(o,a=!1){const l=se();Qe(l,o);for(let u=0;u<this.path.length;u++){const c=this.path[u];!a&&c.options.layoutScroll&&c.scroll&&c!==c.root&&kr(l,{x:-c.scroll.offset.x,y:-c.scroll.offset.y}),Mn(c.latestValues)&&kr(l,c.latestValues)}return Mn(this.latestValues)&&kr(l,this.latestValues),l}removeTransform(o){const a=se();Qe(a,o);for(let l=0;l<this.path.length;l++){const u=this.path[l];if(!u.instance||!Mn(u.latestValues))continue;ju(u.latestValues)&&u.updateSnapshot();const c=se(),d=u.measurePageBox();Qe(c,d),Ef(a,u.latestValues,u.snapshot?u.snapshot.layoutBox:void 0,c)}return Mn(this.latestValues)&&Ef(a,this.latestValues),a}setTargetDelta(o){this.targetDelta=o,this.root.scheduleUpdateProjection(),this.isProjectionDirty=!0}setOptions(o){this.options={...this.options,...o,crossfade:o.crossfade!==void 0?o.crossfade:!0}}clearMeasurements(){this.scroll=void 0,this.layout=void 0,this.snapshot=void 0,this.prevTransformTemplateValue=void 0,this.targetDelta=void 0,this.target=void 0,this.isLayoutDirty=!1}forceRelativeParentToResolveTarget(){this.relativeParent&&this.relativeParent.resolvedRelativeTargetAt!==_e.timestamp&&this.relativeParent.resolveTargetDelta(!0)}resolveTargetDelta(o=!1){var a;const l=this.getLead();this.isProjectionDirty||(this.isProjectionDirty=l.isProjectionDirty),this.isTransformDirty||(this.isTransformDirty=l.isTransformDirty),this.isSharedProjectionDirty||(this.isSharedProjectionDirty=l.isSharedProjectionDirty);const u=!!this.resumingFrom||this!==l;if(!(o||u&&this.isSharedProjectionDirty||this.isProjectionDirty||!((a=this.parent)===null||a===void 0)&&a.isProjectionDirty||this.attemptToResolveRelativeTarget))return;const{layout:d,layoutId:h}=this.options;if(!(!this.layout||!(d||h))){if(this.resolvedRelativeTargetAt=_e.timestamp,!this.targetDelta&&!this.relativeTarget){const f=this.getClosestProjectingParent();f&&f.layout&&this.animationProgress!==1?(this.relativeParent=f,this.forceRelativeParentToResolveTarget(),this.relativeTarget=se(),this.relativeTargetOrigin=se(),Oi(this.relativeTargetOrigin,this.layout.layoutBox,f.layout.layoutBox),Qe(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}if(!(!this.relativeTarget&&!this.targetDelta)){if(this.target||(this.target=se(),this.targetWithTransforms=se()),this.relativeTarget&&this.relativeTargetOrigin&&this.relativeParent&&this.relativeParent.target?(this.forceRelativeParentToResolveTarget(),CC(this.target,this.relativeTarget,this.relativeParent.target)):this.targetDelta?(this.resumingFrom?this.target=this.applyTransform(this.layout.layoutBox):Qe(this.target,this.layout.layoutBox),iv(this.target,this.targetDelta)):Qe(this.target,this.layout.layoutBox),this.attemptToResolveRelativeTarget){this.attemptToResolveRelativeTarget=!1;const f=this.getClosestProjectingParent();f&&!!f.resumingFrom==!!this.resumingFrom&&!f.options.layoutScroll&&f.target&&this.animationProgress!==1?(this.relativeParent=f,this.forceRelativeParentToResolveTarget(),this.relativeTarget=se(),this.relativeTargetOrigin=se(),Oi(this.relativeTargetOrigin,this.target,f.target),Qe(this.relativeTarget,this.relativeTargetOrigin)):this.relativeParent=this.relativeTarget=void 0}Dn.resolvedTargetDeltas++}}}getClosestProjectingParent(){if(!(!this.parent||ju(this.parent.latestValues)||rv(this.parent.latestValues)))return this.parent.isProjecting()?this.parent:this.parent.getClosestProjectingParent()}isProjecting(){return!!((this.relativeTarget||this.targetDelta||this.options.layoutRoot)&&this.layout)}calcProjection(){var o;const a=this.getLead(),l=!!this.resumingFrom||this!==a;let u=!0;if((this.isProjectionDirty||!((o=this.parent)===null||o===void 0)&&o.isProjectionDirty)&&(u=!1),l&&(this.isSharedProjectionDirty||this.isTransformDirty)&&(u=!1),this.resolvedRelativeTargetAt===_e.timestamp&&(u=!1),u)return;const{layout:c,layoutId:d}=this.options;if(this.isTreeAnimating=!!(this.parent&&this.parent.isTreeAnimating||this.currentAnimation||this.pendingAnimation),this.isTreeAnimating||(this.targetDelta=this.relativeTarget=void 0),!this.layout||!(c||d))return;Qe(this.layoutCorrected,this.layout.layoutBox);const h=this.treeScale.x,f=this.treeScale.y;OC(this.layoutCorrected,this.treeScale,this.path,l),a.layout&&!a.target&&(this.treeScale.x!==1||this.treeScale.y!==1)&&(a.target=a.layout.layoutBox);const{target:g}=a;if(!g){this.projectionTransform&&(this.projectionDelta=Sr(),this.projectionTransform="none",this.scheduleRender());return}this.projectionDelta||(this.projectionDelta=Sr(),this.projectionDeltaWithTransform=Sr());const w=this.projectionTransform;Li(this.projectionDelta,this.layoutCorrected,g,this.latestValues),this.projectionTransform=Tf(this.projectionDelta,this.treeScale),(this.projectionTransform!==w||this.treeScale.x!==h||this.treeScale.y!==f)&&(this.hasProjected=!0,this.scheduleRender(),this.notifyListeners("projectionUpdate",g)),Dn.recalculatedProjection++}hide(){this.isVisible=!1}show(){this.isVisible=!0}scheduleRender(o=!0){if(this.options.scheduleRender&&this.options.scheduleRender(),o){const a=this.getStack();a&&a.scheduleRender()}this.resumingFrom&&!this.resumingFrom.instance&&(this.resumingFrom=void 0)}setAnimationOrigin(o,a=!1){const l=this.snapshot,u=l?l.latestValues:{},c={...this.latestValues},d=Sr();(!this.relativeParent||!this.relativeParent.options.layoutRoot)&&(this.relativeTarget=this.relativeTargetOrigin=void 0),this.attemptToResolveRelativeTarget=!a;const h=se(),f=l?l.source:void 0,g=this.layout?this.layout.source:void 0,w=f!==g,x=this.getStack(),y=!x||x.members.length<=1,p=!!(w&&!y&&this.options.crossfade===!0&&!this.path.some(_E));this.animationProgress=0;let m;this.mixTargetDelta=S=>{const k=S/1e3;Of(d.x,o.x,k),Of(d.y,o.y,k),this.setTargetDelta(d),this.relativeTarget&&this.relativeTargetOrigin&&this.layout&&this.relativeParent&&this.relativeParent.layout&&(Oi(h,this.layout.layoutBox,this.relativeParent.layout.layoutBox),xE(this.relativeTarget,this.relativeTargetOrigin,h,k),m&&ZC(this.relativeTarget,m)&&(this.isProjectionDirty=!1),m||(m=se()),Qe(m,this.relativeTarget)),w&&(this.animationValues=c,GC(c,u,this.latestValues,k,p,y)),this.root.scheduleUpdateProjection(),this.scheduleRender(),this.animationProgress=k},this.mixTargetDelta(this.options.layoutRoot?1e3:0)}startAnimation(o){this.notifyListeners("animationStart"),this.currentAnimation&&this.currentAnimation.stop(),this.resumingFrom&&this.resumingFrom.currentAnimation&&this.resumingFrom.currentAnimation.stop(),this.pendingAnimation&&($t(this.pendingAnimation),this.pendingAnimation=void 0),this.pendingAnimation=H.update(()=>{Xs.hasAnimatedSinceResize=!0,this.currentAnimation=oE(0,Af,{...o,onUpdate:a=>{this.mixTargetDelta(a),o.onUpdate&&o.onUpdate(a)},onComplete:()=>{o.onComplete&&o.onComplete(),this.completeAnimation()}}),this.resumingFrom&&(this.resumingFrom.currentAnimation=this.currentAnimation),this.pendingAnimation=void 0})}completeAnimation(){this.resumingFrom&&(this.resumingFrom.currentAnimation=void 0,this.resumingFrom.preserveOpacity=void 0);const o=this.getStack();o&&o.exitAnimationComplete(),this.resumingFrom=this.currentAnimation=this.animationValues=void 0,this.notifyListeners("animationComplete")}finishAnimation(){this.currentAnimation&&(this.mixTargetDelta&&this.mixTargetDelta(Af),this.currentAnimation.stop()),this.completeAnimation()}applyTransformsToTarget(){const o=this.getLead();let{targetWithTransforms:a,target:l,layout:u,latestValues:c}=o;if(!(!a||!l||!u)){if(this!==o&&this.layout&&u&&fv(this.options.animationType,this.layout.layoutBox,u.layoutBox)){l=this.target||se();const d=Ke(this.layout.layoutBox.x);l.x.min=o.target.x.min,l.x.max=l.x.min+d;const h=Ke(this.layout.layoutBox.y);l.y.min=o.target.y.min,l.y.max=l.y.min+h}Qe(a,l),kr(a,c),Li(this.projectionDeltaWithTransform,this.layoutCorrected,a,c)}}registerSharedNode(o,a){this.sharedNodes.has(o)||this.sharedNodes.set(o,new eE),this.sharedNodes.get(o).add(a);const u=a.options.initialPromotionConfig;a.promote({transition:u?u.transition:void 0,preserveFollowOpacity:u&&u.shouldPreserveFollowOpacity?u.shouldPreserveFollowOpacity(a):void 0})}isLead(){const o=this.getStack();return o?o.lead===this:!0}getLead(){var o;const{layoutId:a}=this.options;return a?((o=this.getStack())===null||o===void 0?void 0:o.lead)||this:this}getPrevLead(){var o;const{layoutId:a}=this.options;return a?(o=this.getStack())===null||o===void 0?void 0:o.prevLead:void 0}getStack(){const{layoutId:o}=this.options;if(o)return this.root.sharedNodes.get(o)}promote({needsReset:o,transition:a,preserveFollowOpacity:l}={}){const u=this.getStack();u&&u.promote(this,l),o&&(this.projectionDelta=void 0,this.needsReset=!0),a&&this.setOptions({transition:a})}relegate(){const o=this.getStack();return o?o.relegate(this):!1}resetRotation(){const{visualElement:o}=this.options;if(!o)return;let a=!1;const{latestValues:l}=o;if((l.rotate||l.rotateX||l.rotateY||l.rotateZ)&&(a=!0),!a)return;const u={};for(let c=0;c<jf.length;c++){const d="rotate"+jf[c];l[d]&&(u[d]=l[d],o.setStaticValue(d,0))}o.render();for(const c in u)o.setStaticValue(c,u[c]);o.scheduleRender()}getProjectionStyles(o){var a,l;if(!this.instance||this.isSVG)return;if(!this.isVisible)return aE;const u={visibility:""},c=this.getTransformTemplate();if(this.needsReset)return this.needsReset=!1,u.opacity="",u.pointerEvents=Ys(o==null?void 0:o.pointerEvents)||"",u.transform=c?c(this.latestValues,""):"none",u;const d=this.getLead();if(!this.projectionDelta||!this.layout||!d.target){const w={};return this.options.layoutId&&(w.opacity=this.latestValues.opacity!==void 0?this.latestValues.opacity:1,w.pointerEvents=Ys(o==null?void 0:o.pointerEvents)||""),this.hasProjected&&!Mn(this.latestValues)&&(w.transform=c?c({},""):"none",this.hasProjected=!1),w}const h=d.animationValues||d.latestValues;this.applyTransformsToTarget(),u.transform=Tf(this.projectionDeltaWithTransform,this.treeScale,h),c&&(u.transform=c(h,u.transform));const{x:f,y:g}=this.projectionDelta;u.transformOrigin=`${f.origin*100}% ${g.origin*100}% 0`,d.animationValues?u.opacity=d===this?(l=(a=h.opacity)!==null&&a!==void 0?a:this.latestValues.opacity)!==null&&l!==void 0?l:1:this.preserveOpacity?this.latestValues.opacity:h.opacityExit:u.opacity=d===this?h.opacity!==void 0?h.opacity:"":h.opacityExit!==void 0?h.opacityExit:0;for(const w in Ao){if(h[w]===void 0)continue;const{correct:x,applyTo:y}=Ao[w],p=u.transform==="none"?h[w]:x(h[w],d);if(y){const m=y.length;for(let S=0;S<m;S++)u[y[S]]=p}else u[w]=p}return this.options.layoutId&&(u.pointerEvents=d===this?Ys(o==null?void 0:o.pointerEvents)||"":"none"),u}clearSnapshot(){this.resumeFrom=this.snapshot=void 0}resetTree(){this.root.nodes.forEach(o=>{var a;return(a=o.currentAnimation)===null||a===void 0?void 0:a.stop()}),this.root.nodes.forEach(Rf),this.root.sharedNodes.clear()}}}function uE(e){e.updateLayout()}function cE(e){var t;const n=((t=e.resumeFrom)===null||t===void 0?void 0:t.snapshot)||e.snapshot;if(e.isLead()&&e.layout&&n&&e.hasListeners("didUpdate")){const{layoutBox:r,measuredBox:i}=e.layout,{animationType:s}=e.options,o=n.source!==e.layout.source;s==="size"?Ye(d=>{const h=o?n.measuredBox[d]:n.layoutBox[d],f=Ke(h);h.min=r[d].min,h.max=h.min+f}):fv(s,n.layoutBox,r)&&Ye(d=>{const h=o?n.measuredBox[d]:n.layoutBox[d],f=Ke(r[d]);h.max=h.min+f,e.relativeTarget&&!e.currentAnimation&&(e.isProjectionDirty=!0,e.relativeTarget[d].max=e.relativeTarget[d].min+f)});const a=Sr();Li(a,r,n.layoutBox);const l=Sr();o?Li(l,e.applyTransform(i,!0),n.measuredBox):Li(l,r,n.layoutBox);const u=!cv(a);let c=!1;if(!e.resumeFrom){const d=e.getClosestProjectingParent();if(d&&!d.resumeFrom){const{snapshot:h,layout:f}=d;if(h&&f){const g=se();Oi(g,n.layoutBox,h.layoutBox);const w=se();Oi(w,r,f.layoutBox),dv(g,w)||(c=!0),d.options.layoutRoot&&(e.relativeTarget=w,e.relativeTargetOrigin=g,e.relativeParent=d)}}}e.notifyListeners("didUpdate",{layout:r,snapshot:n,delta:l,layoutDelta:a,hasLayoutChanged:u,hasRelativeTargetChanged:c})}else if(e.isLead()){const{onExitComplete:r}=e.options;r&&r()}e.options.transition=void 0}function dE(e){Dn.totalNodes++,e.parent&&(e.isProjecting()||(e.isProjectionDirty=e.parent.isProjectionDirty),e.isSharedProjectionDirty||(e.isSharedProjectionDirty=!!(e.isProjectionDirty||e.parent.isProjectionDirty||e.parent.isSharedProjectionDirty)),e.isTransformDirty||(e.isTransformDirty=e.parent.isTransformDirty))}function hE(e){e.isProjectionDirty=e.isSharedProjectionDirty=e.isTransformDirty=!1}function fE(e){e.clearSnapshot()}function Rf(e){e.clearMeasurements()}function pE(e){e.isLayoutDirty=!1}function mE(e){const{visualElement:t}=e.options;t&&t.getProps().onBeforeLayoutMeasure&&t.notify("BeforeLayoutMeasure"),e.resetTransform()}function Lf(e){e.finishAnimation(),e.targetDelta=e.relativeTarget=e.target=void 0,e.isProjectionDirty=!0}function gE(e){e.resolveTargetDelta()}function vE(e){e.calcProjection()}function yE(e){e.resetRotation()}function wE(e){e.removeLeadSnapshot()}function Of(e,t,n){e.translate=Y(t.translate,0,n),e.scale=Y(t.scale,1,n),e.origin=t.origin,e.originPoint=t.originPoint}function If(e,t,n,r){e.min=Y(t.min,n.min,r),e.max=Y(t.max,n.max,r)}function xE(e,t,n,r){If(e.x,t.x,n.x,r),If(e.y,t.y,n.y,r)}function _E(e){return e.animationValues&&e.animationValues.opacityExit!==void 0}const SE={duration:.45,ease:[.4,0,.1,1]},Nf=e=>typeof navigator<"u"&&navigator.userAgent.toLowerCase().includes(e),Mf=Nf("applewebkit/")&&!Nf("chrome/")?Math.round:te;function Df(e){e.min=Mf(e.min),e.max=Mf(e.max)}function kE(e){Df(e.x),Df(e.y)}function fv(e,t,n){return e==="position"||e==="preserve-aspect"&&!Pu(Pf(t),Pf(n),.2)}const CE=hv({attachResizeListener:(e,t)=>Rt(e,"resize",t),measureScroll:()=>({x:document.documentElement.scrollLeft||document.body.scrollLeft,y:document.documentElement.scrollTop||document.body.scrollTop}),checkIsScrollRoot:()=>!0}),cl={current:void 0},pv=hv({measureScroll:e=>({x:e.scrollLeft,y:e.scrollTop}),defaultParent:()=>{if(!cl.current){const e=new CE({});e.mount(window),e.setOptions({layoutScroll:!0}),cl.current=e}return cl.current},resetTransform:(e,t)=>{e.style.transform=t!==void 0?t:"none"},checkIsScrollRoot:e=>window.getComputedStyle(e).position==="fixed"}),EE={pan:{Feature:BC},drag:{Feature:zC,ProjectionNode:pv,MeasureLayout:av}},bE=/var\((--[a-zA-Z0-9-_]+),? ?([a-zA-Z0-9 ()%#.,-]+)?\)/;function PE(e){const t=bE.exec(e);if(!t)return[,];const[,n,r]=t;return[n,r]}function Ru(e,t,n=1){const[r,i]=PE(e);if(!r)return;const s=window.getComputedStyle(t).getPropertyValue(r);if(s){const o=s.trim();return Qg(o)?parseFloat(o):o}else return xu(i)?Ru(i,t,n+1):i}function TE(e,{...t},n){const r=e.current;if(!(r instanceof Element))return{target:t,transitionEnd:n};n&&(n={...n}),e.values.forEach(i=>{const s=i.get();if(!xu(s))return;const o=Ru(s,r);o&&i.set(o)});for(const i in t){const s=t[i];if(!xu(s))continue;const o=Ru(s,r);o&&(t[i]=o,n||(n={}),n[i]===void 0&&(n[i]=s))}return{target:t,transitionEnd:n}}const jE=new Set(["width","height","top","left","right","bottom","x","y","translateX","translateY"]),mv=e=>jE.has(e),AE=e=>Object.keys(e).some(mv),Vf=e=>e===tr||e===R,$f=(e,t)=>parseFloat(e.split(", ")[t]),zf=(e,t)=>(n,{transform:r})=>{if(r==="none"||!r)return 0;const i=r.match(/^matrix3d\((.+)\)$/);if(i)return $f(i[1],t);{const s=r.match(/^matrix\((.+)\)$/);return s?$f(s[1],e):0}},RE=new Set(["x","y","z"]),LE=us.filter(e=>!RE.has(e));function OE(e){const t=[];return LE.forEach(n=>{const r=e.getValue(n);r!==void 0&&(t.push([n,r.get()]),r.set(n.startsWith("scale")?1:0))}),t.length&&e.render(),t}const Hr={width:({x:e},{paddingLeft:t="0",paddingRight:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),height:({y:e},{paddingTop:t="0",paddingBottom:n="0"})=>e.max-e.min-parseFloat(t)-parseFloat(n),top:(e,{top:t})=>parseFloat(t),left:(e,{left:t})=>parseFloat(t),bottom:({y:e},{top:t})=>parseFloat(t)+(e.max-e.min),right:({x:e},{left:t})=>parseFloat(t)+(e.max-e.min),x:zf(4,13),y:zf(5,14)};Hr.translateX=Hr.x;Hr.translateY=Hr.y;const IE=(e,t,n)=>{const r=t.measureViewportBox(),i=t.current,s=getComputedStyle(i),{display:o}=s,a={};o==="none"&&t.setStaticValue("display",e.display||"block"),n.forEach(u=>{a[u]=Hr[u](r,s)}),t.render();const l=t.measureViewportBox();return n.forEach(u=>{const c=t.getValue(u);c&&c.jump(a[u]),e[u]=Hr[u](l,s)}),e},NE=(e,t,n={},r={})=>{t={...t},r={...r};const i=Object.keys(t).filter(mv);let s=[],o=!1;const a=[];if(i.forEach(l=>{const u=e.getValue(l);if(!e.hasValue(l))return;let c=n[l],d=li(c);const h=t[l];let f;if(Lo(h)){const g=h.length,w=h[0]===null?1:0;c=h[w],d=li(c);for(let x=w;x<g&&h[x]!==null;x++)f?qc(li(h[x])===f):f=li(h[x])}else f=li(h);if(d!==f)if(Vf(d)&&Vf(f)){const g=u.get();typeof g=="string"&&u.set(parseFloat(g)),typeof h=="string"?t[l]=parseFloat(h):Array.isArray(h)&&f===R&&(t[l]=h.map(parseFloat))}else d!=null&&d.transform&&(f!=null&&f.transform)&&(c===0||h===0)?c===0?u.set(f.transform(c)):t[l]=d.transform(h):(o||(s=OE(e),o=!0),a.push(l),r[l]=r[l]!==void 0?r[l]:t[l],u.jump(h))}),a.length){const l=a.indexOf("height")>=0?window.pageYOffset:null,u=IE(t,e,a);return s.length&&s.forEach(([c,d])=>{e.getValue(c).set(d)}),e.render(),pa&&l!==null&&window.scrollTo({top:l}),{target:u,transitionEnd:r}}else return{target:t,transitionEnd:r}};function ME(e,t,n,r){return AE(t)?NE(e,t,n,r):{target:t,transitionEnd:r}}const DE=(e,t,n,r)=>{const i=TE(e,t,r);return t=i.target,r=i.transitionEnd,ME(e,t,n,r)},Lu={current:null},gv={current:!1};function VE(){if(gv.current=!0,!!pa)if(window.matchMedia){const e=window.matchMedia("(prefers-reduced-motion)"),t=()=>Lu.current=e.matches;e.addListener(t),t()}else Lu.current=!1}function $E(e,t,n){const{willChange:r}=t;for(const i in t){const s=t[i],o=n[i];if(Ve(s))e.addValue(i,s),Mo(r)&&r.add(i);else if(Ve(o))e.addValue(i,Fr(s,{owner:e})),Mo(r)&&r.remove(i);else if(o!==s)if(e.hasValue(i)){const a=e.getValue(i);!a.hasAnimated&&a.set(s)}else{const a=e.getStaticValue(i);e.addValue(i,Fr(a!==void 0?a:s,{owner:e}))}}for(const i in n)t[i]===void 0&&e.removeValue(i);return t}const Bf=new WeakMap,vv=Object.keys(es),zE=vv.length,Uf=["AnimationStart","AnimationComplete","Update","BeforeLayoutMeasure","LayoutMeasure","LayoutAnimationStart","LayoutAnimationComplete"],BE=$c.length;class UE{constructor({parent:t,props:n,presenceContext:r,reducedMotionConfig:i,visualState:s},o={}){this.current=null,this.children=new Set,this.isVariantNode=!1,this.isControllingVariants=!1,this.shouldReduceMotion=null,this.values=new Map,this.features={},this.valueSubscriptions=new Map,this.prevMotionValues={},this.events={},this.propEventSubscriptions={},this.notifyUpdate=()=>this.notify("Update",this.latestValues),this.render=()=>{this.current&&(this.triggerBuild(),this.renderInstance(this.current,this.renderState,this.props.style,this.projection))},this.scheduleRender=()=>H.render(this.render,!1,!0);const{latestValues:a,renderState:l}=s;this.latestValues=a,this.baseTarget={...a},this.initialValues=n.initial?{...a}:{},this.renderState=l,this.parent=t,this.props=n,this.presenceContext=r,this.depth=t?t.depth+1:0,this.reducedMotionConfig=i,this.options=o,this.isControllingVariants=ga(n),this.isVariantNode=tg(n),this.isVariantNode&&(this.variantChildren=new Set),this.manuallyAnimateOnMount=!!(t&&t.current);const{willChange:u,...c}=this.scrapeMotionValuesFromProps(n,{});for(const d in c){const h=c[d];a[d]!==void 0&&Ve(h)&&(h.set(a[d],!1),Mo(u)&&u.add(d))}}scrapeMotionValuesFromProps(t,n){return{}}mount(t){this.current=t,Bf.set(t,this),this.projection&&!this.projection.instance&&this.projection.mount(t),this.parent&&this.isVariantNode&&!this.isControllingVariants&&(this.removeFromVariantTree=this.parent.addVariantChild(this)),this.values.forEach((n,r)=>this.bindToMotionValue(r,n)),gv.current||VE(),this.shouldReduceMotion=this.reducedMotionConfig==="never"?!1:this.reducedMotionConfig==="always"?!0:Lu.current,this.parent&&this.parent.children.add(this),this.update(this.props,this.presenceContext)}unmount(){Bf.delete(this.current),this.projection&&this.projection.unmount(),$t(this.notifyUpdate),$t(this.render),this.valueSubscriptions.forEach(t=>t()),this.removeFromVariantTree&&this.removeFromVariantTree(),this.parent&&this.parent.children.delete(this);for(const t in this.events)this.events[t].clear();for(const t in this.features)this.features[t].unmount();this.current=null}bindToMotionValue(t,n){const r=er.has(t),i=n.on("change",o=>{this.latestValues[t]=o,this.props.onUpdate&&H.update(this.notifyUpdate,!1,!0),r&&this.projection&&(this.projection.isTransformDirty=!0)}),s=n.on("renderRequest",this.scheduleRender);this.valueSubscriptions.set(t,()=>{i(),s()})}sortNodePosition(t){return!this.current||!this.sortInstanceNodePosition||this.type!==t.type?0:this.sortInstanceNodePosition(this.current,t.current)}loadFeatures({children:t,...n},r,i,s){let o,a;for(let l=0;l<zE;l++){const u=vv[l],{isEnabled:c,Feature:d,ProjectionNode:h,MeasureLayout:f}=es[u];h&&(o=h),c(n)&&(!this.features[u]&&d&&(this.features[u]=new d(this)),f&&(a=f))}if((this.type==="html"||this.type==="svg")&&!this.projection&&o){this.projection=new o(this.latestValues,this.parent&&this.parent.projection);const{layoutId:l,layout:u,drag:c,dragConstraints:d,layoutScroll:h,layoutRoot:f}=n;this.projection.setOptions({layoutId:l,layout:u,alwaysMeasureLayout:!!c||d&&xr(d),visualElement:this,scheduleRender:()=>this.scheduleRender(),animationType:typeof u=="string"?u:"both",initialPromotionConfig:s,layoutScroll:h,layoutRoot:f})}return a}updateFeatures(){for(const t in this.features){const n=this.features[t];n.isMounted?n.update():(n.mount(),n.isMounted=!0)}}triggerBuild(){this.build(this.renderState,this.latestValues,this.options,this.props)}measureViewportBox(){return this.current?this.measureInstanceViewportBox(this.current,this.props):se()}getStaticValue(t){return this.latestValues[t]}setStaticValue(t,n){this.latestValues[t]=n}makeTargetAnimatable(t,n=!0){return this.makeTargetAnimatableFromInstance(t,this.props,n)}update(t,n){(t.transformTemplate||this.props.transformTemplate)&&this.scheduleRender(),this.prevProps=this.props,this.props=t,this.prevPresenceContext=this.presenceContext,this.presenceContext=n;for(let r=0;r<Uf.length;r++){const i=Uf[r];this.propEventSubscriptions[i]&&(this.propEventSubscriptions[i](),delete this.propEventSubscriptions[i]);const s=t["on"+i];s&&(this.propEventSubscriptions[i]=this.on(i,s))}this.prevMotionValues=$E(this,this.scrapeMotionValuesFromProps(t,this.prevProps),this.prevMotionValues),this.handleChildMotionValue&&this.handleChildMotionValue()}getProps(){return this.props}getVariant(t){return this.props.variants?this.props.variants[t]:void 0}getDefaultTransition(){return this.props.transition}getTransformPagePoint(){return this.props.transformPagePoint}getClosestVariantNode(){return this.isVariantNode?this:this.parent?this.parent.getClosestVariantNode():void 0}getVariantContext(t=!1){if(t)return this.parent?this.parent.getVariantContext():void 0;if(!this.isControllingVariants){const r=this.parent?this.parent.getVariantContext()||{}:{};return this.props.initial!==void 0&&(r.initial=this.props.initial),r}const n={};for(let r=0;r<BE;r++){const i=$c[r],s=this.props[i];(Zi(s)||s===!1)&&(n[i]=s)}return n}addVariantChild(t){const n=this.getClosestVariantNode();if(n)return n.variantChildren&&n.variantChildren.add(t),()=>n.variantChildren.delete(t)}addValue(t,n){n!==this.values.get(t)&&(this.removeValue(t),this.bindToMotionValue(t,n)),this.values.set(t,n),this.latestValues[t]=n.get()}removeValue(t){this.values.delete(t);const n=this.valueSubscriptions.get(t);n&&(n(),this.valueSubscriptions.delete(t)),delete this.latestValues[t],this.removeValueFromRenderState(t,this.renderState)}hasValue(t){return this.values.has(t)}getValue(t,n){if(this.props.values&&this.props.values[t])return this.props.values[t];let r=this.values.get(t);return r===void 0&&n!==void 0&&(r=Fr(n,{owner:this}),this.addValue(t,r)),r}readValue(t){var n;return this.latestValues[t]!==void 0||!this.current?this.latestValues[t]:(n=this.getBaseTargetFromProps(this.props,t))!==null&&n!==void 0?n:this.readValueFromInstance(this.current,t,this.options)}setBaseTarget(t,n){this.baseTarget[t]=n}getBaseTarget(t){var n;const{initial:r}=this.props,i=typeof r=="string"||typeof r=="object"?(n=Gc(this.props,r))===null||n===void 0?void 0:n[t]:void 0;if(r&&i!==void 0)return i;const s=this.getBaseTargetFromProps(this.props,t);return s!==void 0&&!Ve(s)?s:this.initialValues[t]!==void 0&&i===void 0?void 0:this.baseTarget[t]}on(t,n){return this.events[t]||(this.events[t]=new rd),this.events[t].add(n)}notify(t,...n){this.events[t]&&this.events[t].notify(...n)}}class yv extends UE{sortInstanceNodePosition(t,n){return t.compareDocumentPosition(n)&2?1:-1}getBaseTargetFromProps(t,n){return t.style?t.style[n]:void 0}removeValueFromRenderState(t,{vars:n,style:r}){delete n[t],delete r[t]}makeTargetAnimatableFromInstance({transition:t,transitionEnd:n,...r},{transformValues:i},s){let o=sC(r,t||{},this);if(i&&(n&&(n=i(n)),r&&(r=i(r)),o&&(o=i(o))),s){rC(this,r,o);const a=DE(this,r,o,n);n=a.transitionEnd,r=a.target}return{transition:t,transitionEnd:n,...r}}}function FE(e){return window.getComputedStyle(e)}class HE extends yv{constructor(){super(...arguments),this.type="html"}readValueFromInstance(t,n){if(er.has(n)){const r=Xc(n);return r&&r.default||0}else{const r=FE(t),i=(sg(n)?r.getPropertyValue(n):r[n])||0;return typeof i=="string"?i.trim():i}}measureInstanceViewportBox(t,{transformPagePoint:n}){return sv(t,n)}build(t,n,r,i){Uc(t,n,r,i.transformTemplate)}scrapeMotionValuesFromProps(t,n){return Kc(t,n)}handleChildMotionValue(){this.childSubscription&&(this.childSubscription(),delete this.childSubscription);const{children:t}=this.props;Ve(t)&&(this.childSubscription=t.on("change",n=>{this.current&&(this.current.textContent=`${n}`)}))}renderInstance(t,n,r,i){dg(t,n,r,i)}}class WE extends yv{constructor(){super(...arguments),this.type="svg",this.isSVGTag=!1}getBaseTargetFromProps(t,n){return t[n]}readValueFromInstance(t,n){if(er.has(n)){const r=Xc(n);return r&&r.default||0}return n=hg.has(n)?n:Dc(n),t.getAttribute(n)}measureInstanceViewportBox(){return se()}scrapeMotionValuesFromProps(t,n){return pg(t,n)}build(t,n,r,i){Hc(t,n,r,this.isSVGTag,i.transformTemplate)}renderInstance(t,n,r,i){fg(t,n,r,i)}mount(t){this.isSVGTag=Wc(t.tagName),super.mount(t)}}const KE=(e,t)=>Bc(e)?new WE(t,{enableHardwareAcceleration:!1}):new HE(t,{enableHardwareAcceleration:!0}),GE={layout:{ProjectionNode:pv,MeasureLayout:av}},qE={...xC,...zk,...EE,...GE},Q=JS((e,t)=>Pk(e,t,qE,KE));function wv(){const e=_.useRef(!1);return Mc(()=>(e.current=!0,()=>{e.current=!1}),[]),e}function JE(){const e=wv(),[t,n]=_.useState(0),r=_.useCallback(()=>{e.current&&n(t+1)},[t]);return[_.useCallback(()=>H.postRender(r),[r]),t]}class QE extends _.Component{getSnapshotBeforeUpdate(t){const n=this.props.childRef.current;if(n&&t.isPresent&&!this.props.isPresent){const r=this.props.sizeRef.current;r.height=n.offsetHeight||0,r.width=n.offsetWidth||0,r.top=n.offsetTop,r.left=n.offsetLeft}return null}componentDidUpdate(){}render(){return this.props.children}}function YE({children:e,isPresent:t}){const n=_.useId(),r=_.useRef(null),i=_.useRef({width:0,height:0,top:0,left:0});return _.useInsertionEffect(()=>{const{width:s,height:o,top:a,left:l}=i.current;if(t||!r.current||!s||!o)return;r.current.dataset.motionPopId=n;const u=document.createElement("style");return document.head.appendChild(u),u.sheet&&u.sheet.insertRule(`
          [data-motion-pop-id="${n}"] {
            position: absolute !important;
            width: ${s}px !important;
            height: ${o}px !important;
            top: ${a}px !important;
            left: ${l}px !important;
          }
        `),()=>{document.head.removeChild(u)}},[t]),_.createElement(QE,{isPresent:t,childRef:r,sizeRef:i},_.cloneElement(e,{ref:r}))}const dl=({children:e,initial:t,isPresent:n,onExitComplete:r,custom:i,presenceAffectsLayout:s,mode:o})=>{const a=mg(XE),l=_.useId(),u=_.useMemo(()=>({id:l,initial:t,isPresent:n,custom:i,onExitComplete:c=>{a.set(c,!0);for(const d of a.values())if(!d)return;r&&r()},register:c=>(a.set(c,!1),()=>a.delete(c))}),s?void 0:[n]);return _.useMemo(()=>{a.forEach((c,d)=>a.set(d,!1))},[n]),_.useEffect(()=>{!n&&!a.size&&r&&r()},[n]),o==="popLayout"&&(e=_.createElement(YE,{isPresent:n},e)),_.createElement(fa.Provider,{value:u},e)};function XE(){return new Map}function ZE(e){return _.useEffect(()=>()=>e(),[])}const Vn=e=>e.key||"";function eb(e,t){e.forEach(n=>{const r=Vn(n);t.set(r,n)})}function tb(e){const t=[];return _.Children.forEach(e,n=>{_.isValidElement(n)&&t.push(n)}),t}const xv=({children:e,custom:t,initial:n=!0,onExitComplete:r,exitBeforeEnter:i,presenceAffectsLayout:s=!0,mode:o="sync"})=>{const a=_.useContext(zc).forceRender||JE()[0],l=wv(),u=tb(e);let c=u;const d=_.useRef(new Map).current,h=_.useRef(c),f=_.useRef(new Map).current,g=_.useRef(!0);if(Mc(()=>{g.current=!1,eb(u,f),h.current=c}),ZE(()=>{g.current=!0,f.clear(),d.clear()}),g.current)return _.createElement(_.Fragment,null,c.map(p=>_.createElement(dl,{key:Vn(p),isPresent:!0,initial:n?void 0:!1,presenceAffectsLayout:s,mode:o},p)));c=[...c];const w=h.current.map(Vn),x=u.map(Vn),y=w.length;for(let p=0;p<y;p++){const m=w[p];x.indexOf(m)===-1&&!d.has(m)&&d.set(m,void 0)}return o==="wait"&&d.size&&(c=[]),d.forEach((p,m)=>{if(x.indexOf(m)!==-1)return;const S=f.get(m);if(!S)return;const k=w.indexOf(m);let C=p;if(!C){const E=()=>{d.delete(m);const b=Array.from(f.keys()).filter(A=>!x.includes(A));if(b.forEach(A=>f.delete(A)),h.current=u.filter(A=>{const L=Vn(A);return L===m||b.includes(L)}),!d.size){if(l.current===!1)return;a(),r&&r()}};C=_.createElement(dl,{key:Vn(S),isPresent:!1,onExitComplete:E,custom:t,presenceAffectsLayout:s,mode:o},S),d.set(m,C)}c.splice(k,0,C)}),c=c.map(p=>{const m=p.key;return d.has(m)?p:_.createElement(dl,{key:Vn(p),isPresent:!0,presenceAffectsLayout:s,mode:o},p)}),_.createElement(_.Fragment,null,d.size?c:c.map(p=>_.cloneElement(p)))},zt=({children:e,onClick:t,variant:n="primary",size:r="md",isLoading:i=!1,disabled:s=!1,type:o="button",className:a="","aria-label":l,"aria-describedby":u,"aria-expanded":c,"aria-controls":d,"aria-pressed":h,autoFocus:f=!1,...g})=>{const w="font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transform-gpu",x={primary:"bg-primary-500 hover:bg-primary-600 text-white focus:ring-primary-500 hover:shadow-lg",secondary:"border-2 border-primary-500 text-primary-500 hover:bg-primary-50 focus:ring-primary-500 hover:shadow-md",danger:"bg-red-500 hover:bg-red-600 text-white focus:ring-red-500 hover:shadow-lg"},y={sm:"px-3 py-1.5 text-sm",md:"px-4 py-2 text-base",lg:"px-6 py-3 text-lg"},p=`${w} ${x[n]} ${y[r]} ${a}`,m={hover:{scale:1.02,transition:{duration:.15,ease:"easeOut"}},tap:{scale:.98,transition:{duration:.1,ease:"easeInOut"}}};return v.jsx(Q.button,{type:o,onClick:t,disabled:s||i,className:p,"aria-label":l,"aria-describedby":u,"aria-expanded":c,"aria-controls":d,"aria-pressed":h,"aria-busy":i,autoFocus:f,variants:m,whileHover:!s&&!i?"hover":void 0,whileTap:!s&&!i?"tap":void 0,...g,children:i?v.jsxs("div",{className:"flex items-center",children:[v.jsxs("svg",{className:"animate-spin -ml-1 mr-3 h-5 w-5 text-current",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[v.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),v.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Loading..."]}):e})},Cr=({label:e,placeholder:t,value:n,onChange:r,type:i="text",error:s,required:o=!1,disabled:a=!1,className:l="","aria-describedby":u,"aria-invalid":c,description:d,...h})=>{const f=_.useId(),g=`${f}-error`,w=`${f}-description`,x=`w-full px-3 py-2 border-2 rounded-md bg-background-secondary text-white focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent disabled:opacity-50 disabled:cursor-not-allowed ${s?"border-red-500":"border-gray-600 focus:border-primary-500"} ${l}`,y=[s?g:null,d?w:null,u].filter(Boolean).join(" ")||void 0;return v.jsxs("div",{className:"w-full",children:[e&&v.jsxs("label",{htmlFor:f,className:"block text-sm font-medium text-gray-300 mb-1",children:[e,o&&v.jsx("span",{className:"text-red-500 ml-1","aria-label":"required",children:"*"})]}),d&&v.jsx("p",{id:w,className:"text-sm text-gray-400 mb-1",children:d}),v.jsx("input",{id:f,type:i,value:n,onChange:p=>r(p.target.value),placeholder:t,disabled:a,required:o,"aria-invalid":c||!!s,"aria-describedby":y,className:x,...h}),s&&v.jsx("p",{id:g,className:"mt-1 text-sm text-red-500",role:"alert",children:s})]})};var _v={color:void 0,size:void 0,className:void 0,style:void 0,attr:void 0},Ff=Fe.createContext&&Fe.createContext(_v),mn=globalThis&&globalThis.__assign||function(){return mn=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++){t=arguments[n];for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i])}return e},mn.apply(this,arguments)},nb=globalThis&&globalThis.__rest||function(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(n[r]=e[r]);if(e!=null&&typeof Object.getOwnPropertySymbols=="function")for(var i=0,r=Object.getOwnPropertySymbols(e);i<r.length;i++)t.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(n[r[i]]=e[r[i]]);return n};function Sv(e){return e&&e.map(function(t,n){return Fe.createElement(t.tag,mn({key:n},t.attr),Sv(t.child))})}function N(e){return function(t){return Fe.createElement(rb,mn({attr:mn({},e.attr)},t),Sv(e.child))}}function rb(e){var t=function(n){var r=e.attr,i=e.size,s=e.title,o=nb(e,["attr","size","title"]),a=i||n.size||"1em",l;return n.className&&(l=n.className),e.className&&(l=(l?l+" ":"")+e.className),Fe.createElement("svg",mn({stroke:"currentColor",fill:"currentColor",strokeWidth:"0"},n.attr,r,o,{className:l,style:mn(mn({color:e.color||n.color},n.style),e.style),height:a,width:a,xmlns:"http://www.w3.org/2000/svg"}),s&&Fe.createElement("title",null,s),e.children)};return Ff!==void 0?Fe.createElement(Ff.Consumer,null,function(n){return t(n)}):t(_v)}function ib(e){return N({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z"}}]})(e)}function tP(e){return N({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M10 2a6 6 0 00-6 6v3.586l-.707.707A1 1 0 004 14h12a1 1 0 00.707-1.707L16 11.586V8a6 6 0 00-6-6zM10 18a3 3 0 01-3-3h6a3 3 0 01-3 3z"}}]})(e)}function nP(e){return N({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z",clipRule:"evenodd"}}]})(e)}function sb(e){return N({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M2 11a1 1 0 011-1h2a1 1 0 011 1v5a1 1 0 01-1 1H3a1 1 0 01-1-1v-5zM8 7a1 1 0 011-1h2a1 1 0 011 1v9a1 1 0 01-1 1H9a1 1 0 01-1-1V7zM14 4a1 1 0 011-1h2a1 1 0 011 1v12a1 1 0 01-1 1h-2a1 1 0 01-1-1V4z"}}]})(e)}function rP(e){return N({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z"}},{tag:"path",attr:{d:"M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z"}}]})(e)}function iP(e){return N({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"}}]})(e)}function ob(e){return N({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"}}]})(e)}function sP(e){return N({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z",clipRule:"evenodd"}}]})(e)}function oP(e){return N({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z",clipRule:"evenodd"}}]})(e)}function aP(e){return N({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z"}},{tag:"path",attr:{d:"M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z"}}]})(e)}function lP(e){return N({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z",clipRule:"evenodd"}}]})(e)}function ab(e){return N({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M11.49 3.17c-.38-1.56-2.6-1.56-2.98 0a1.532 1.532 0 01-2.286.948c-1.372-.836-2.942.734-2.106 2.106.54.886.061 2.042-.947 2.287-1.561.379-1.561 2.6 0 2.978a1.532 1.532 0 01.947 2.287c-.836 1.372.734 2.942 2.106 2.106a1.532 1.532 0 012.287.947c.379 1.561 2.6 1.561 2.978 0a1.533 1.533 0 012.287-.947c1.372.836 2.942-.734 2.106-2.106a1.533 1.533 0 01.947-2.287c1.561-.379 1.561-2.6 0-2.978a1.532 1.532 0 01-.947-2.287c.836-1.372-.734-2.942-2.106-2.106a1.532 1.532 0 01-2.287-.947zM10 13a3 3 0 100-6 3 3 0 000 6z",clipRule:"evenodd"}}]})(e)}function lb(e){return N({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z"}}]})(e)}function ub(e){return N({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M4 4a2 2 0 00-2 2v1h16V6a2 2 0 00-2-2H4z"}},{tag:"path",attr:{fillRule:"evenodd",d:"M18 9H2v5a2 2 0 002 2h12a2 2 0 002-2V9zM4 13a1 1 0 011-1h1a1 1 0 110 2H5a1 1 0 01-1-1zm5-1a1 1 0 100 2h1a1 1 0 100-2H9z",clipRule:"evenodd"}}]})(e)}function uP(e){return N({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M8.433 7.418c.155-.103.346-.196.567-.267v1.698a2.305 2.305 0 01-.567-.267C8.07 8.34 8 8.114 8 8c0-.114.07-.34.433-.582zM11 12.849v-1.698c.22.071.412.164.567.267.364.243.433.468.433.582 0 .114-.07.34-.433.582a2.305 2.305 0 01-.567.267z"}},{tag:"path",attr:{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-13a1 1 0 10-2 0v.092a4.535 4.535 0 00-1.676.662C6.602 6.234 6 7.009 6 8c0 .99.602 1.765 1.324 *********** 1.054.545 1.676.662v1.941c-.391-.127-.68-.317-.843-.504a1 1 0 10-1.51 1.31c.562.649 1.413 1.076 2.353 1.253V15a1 1 0 102 0v-.092a4.535 4.535 0 001.676-.662C13.398 13.766 14 12.991 14 12c0-.99-.602-1.765-1.324-2.246A4.535 4.535 0 0011 9.092V7.151c.391.127.68.317.843.504a1 1 0 101.511-1.31c-.563-.649-1.413-1.076-2.354-1.253V5z",clipRule:"evenodd"}}]})(e)}function cP(e){return N({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M3 12v3c0 1.657 3.134 3 7 3s7-1.343 7-3v-3c0 1.657-3.134 3-7 3s-7-1.343-7-3z"}},{tag:"path",attr:{d:"M3 7v3c0 1.657 3.134 3 7 3s7-1.343 7-3V7c0 1.657-3.134 3-7 3S3 8.657 3 7z"}},{tag:"path",attr:{d:"M17 5c0 1.657-3.134 3-7 3S3 6.657 3 5s3.134-3 7-3 7 1.343 7 3z"}}]})(e)}function cb(e){return N({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z",clipRule:"evenodd"}}]})(e)}function dP(e){return N({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z",clipRule:"evenodd"}}]})(e)}function hP(e){return N({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z",clipRule:"evenodd"}}]})(e)}function fP(e){return N({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M11 3a1 1 0 100 2h2.586l-6.293 6.293a1 1 0 101.414 1.414L15 6.414V9a1 1 0 102 0V4a1 1 0 00-1-1h-5z"}},{tag:"path",attr:{d:"M5 5a2 2 0 00-2 2v8a2 2 0 002 2h8a2 2 0 002-2v-3a1 1 0 10-2 0v3H5V7h3a1 1 0 000-2H5z"}}]})(e)}function pP(e){return N({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M3.707 2.293a1 1 0 00-1.414 1.414l14 14a1 1 0 001.414-1.414l-1.473-1.473A10.014 10.014 0 0019.542 10C18.268 5.943 14.478 3 10 3a9.958 9.958 0 00-4.512 1.074l-1.78-1.781zm4.261 4.26l1.514 1.515a2.003 2.003 0 012.45 2.45l1.514 1.514a4 4 0 00-5.478-5.478z",clipRule:"evenodd"}},{tag:"path",attr:{d:"M12.454 16.697L9.75 13.992a4 4 0 01-3.742-3.741L2.335 6.578A9.98 9.98 0 00.458 10c1.274 4.057 5.065 7 9.542 7 .847 0 1.669-.105 2.454-.303z"}}]})(e)}function mP(e){return N({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M10 12a2 2 0 100-4 2 2 0 000 4z"}},{tag:"path",attr:{fillRule:"evenodd",d:"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z",clipRule:"evenodd"}}]})(e)}function gP(e){return N({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z",clipRule:"evenodd"}}]})(e)}function vP(e){return N({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z",clipRule:"evenodd"}}]})(e)}function yP(e){return N({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"}}]})(e)}function wP(e){return N({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M18 8a6 6 0 01-7.743 5.743L10 14l-1 1-1 1H6v2H2v-4l4.257-4.257A6 6 0 1118 8zm-6-4a1 1 0 100 2 2 2 0 012 2 1 1 0 102 0 4 4 0 00-4-4z",clipRule:"evenodd"}}]})(e)}function xP(e){return N({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M11.3 1.046A1 1 0 0112 2v5h4a1 1 0 01.82 1.573l-7 10A1 1 0 018 18v-5H4a1 1 0 01-.82-1.573l7-10a1 1 0 011.12-.38z",clipRule:"evenodd"}}]})(e)}function _P(e){return N({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M3 3a1 1 0 00-1 1v12a1 1 0 102 0V4a1 1 0 00-1-1zm10.293 9.293a1 1 0 001.414 1.414l3-3a1 1 0 000-1.414l-3-3a1 1 0 10-1.414 1.414L14.586 9H7a1 1 0 100 2h7.586l-1.293 1.293z",clipRule:"evenodd"}}]})(e)}function SP(e){return N({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"}},{tag:"path",attr:{d:"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"}}]})(e)}function db(e){return N({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z",clipRule:"evenodd"}}]})(e)}function kP(e){return N({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z",clipRule:"evenodd"}}]})(e)}function CP(e){return N({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M17.293 13.293A8 8 0 016.707 2.707a8.001 8.001 0 1010.586 10.586z"}}]})(e)}function hb(e){return N({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z",clipRule:"evenodd"}}]})(e)}function EP(e){return N({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"}}]})(e)}function bP(e){return N({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z",clipRule:"evenodd"}}]})(e)}function PP(e){return N({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"}}]})(e)}function fb(e){return N({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M5 2a1 1 0 011 1v1h1a1 1 0 010 2H6v1a1 1 0 01-2 0V6H3a1 1 0 010-2h1V3a1 1 0 011-1zm0 10a1 1 0 011 1v1h1a1 1 0 110 2H6v1a1 1 0 11-2 0v-1H3a1 1 0 110-2h1v-1a1 1 0 011-1zM12 2a1 1 0 01.967.744L14.146 7.2 17.5 9.134a1 1 0 010 1.732l-3.354 1.935-1.18 4.455a1 1 0 01-1.933 0L9.854 12.8 6.5 10.866a1 1 0 010-1.732l3.354-1.935 1.18-4.455A1 1 0 0112 2z",clipRule:"evenodd"}}]})(e)}function TP(e){return N({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{d:"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"}}]})(e)}function jP(e){return N({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M10 2a1 1 0 011 1v1a1 1 0 11-2 0V3a1 1 0 011-1zm4 8a4 4 0 11-8 0 4 4 0 018 0zm-.464 4.95l.707.707a1 1 0 001.414-1.414l-.707-.707a1 1 0 00-1.414 1.414zm2.12-10.607a1 1 0 010 1.414l-.706.707a1 1 0 11-1.414-1.414l.707-.707a1 1 0 011.414 0zM17 11a1 1 0 100-2h-1a1 1 0 100 2h1zm-7 4a1 1 0 011 1v1a1 1 0 11-2 0v-1a1 1 0 011-1zM5.05 6.464A1 1 0 106.465 5.05l-.708-.707a1 1 0 00-1.414 1.414l.707.707zm1.414 8.486l-.707.707a1 1 0 01-1.414-1.414l.707-.707a1 1 0 011.414 1.414zM4 11a1 1 0 100-2H3a1 1 0 000 2h1z",clipRule:"evenodd"}}]})(e)}function AP(e){return N({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z",clipRule:"evenodd"}}]})(e)}function RP(e){return N({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M12 13a1 1 0 100 2h5a1 1 0 001-1V9a1 1 0 10-2 0v2.586l-4.293-4.293a1 1 0 00-1.414 0L8 9.586 3.707 5.293a1 1 0 00-1.414 1.414l5 5a1 1 0 001.414 0L11 9.414 14.586 13H12z",clipRule:"evenodd"}}]})(e)}function LP(e){return N({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M12 7a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0V8.414l-4.293 4.293a1 1 0 01-1.414 0L8 10.414l-4.293 4.293a1 1 0 01-1.414-1.414l5-5a1 1 0 011.414 0L11 10.586 14.586 7H12z",clipRule:"evenodd"}}]})(e)}function OP(e){return N({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM6.293 6.707a1 1 0 010-1.414l3-3a1 1 0 011.414 0l3 3a1 1 0 01-1.414 1.414L11 5.414V13a1 1 0 11-2 0V5.414L7.707 6.707a1 1 0 01-1.414 0z",clipRule:"evenodd"}}]})(e)}function IP(e){return N({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z",clipRule:"evenodd"}}]})(e)}function pb(e){return N({tag:"svg",attr:{viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},child:[{tag:"path",attr:{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"}}]})(e)}const mb=({id:e,label:t,checked:n,onChange:r,disabled:i=!1,error:s,description:o,size:a="md",className:l=""})=>{const u=f=>{r(f.target.checked)},d=(()=>{switch(a){case"sm":return{checkbox:"w-4 h-4",icon:"w-3 h-3",label:"text-sm",description:"text-xs"};case"lg":return{checkbox:"w-6 h-6",icon:"w-4 h-4",label:"text-lg",description:"text-sm"};case"md":default:return{checkbox:"w-5 h-5",icon:"w-3.5 h-3.5",label:"text-base",description:"text-sm"}}})(),h=e||`checkbox-${Math.random().toString(36).substr(2,9)}`;return v.jsxs("div",{className:`flex items-start space-x-3 ${l}`,children:[v.jsx("div",{className:"flex items-center h-5",children:v.jsxs("div",{className:"relative",children:[v.jsx("input",{id:h,type:"checkbox",checked:n,onChange:u,disabled:i,className:"sr-only","aria-describedby":o?`${h}-description`:void 0}),v.jsx("div",{className:`
              ${d.checkbox}
              border-2 rounded-md cursor-pointer transition-all duration-200
              ${n?"bg-primary-500 border-primary-500":"bg-background-secondary border-gray-600 hover:border-gray-500"}
              ${i?"opacity-50 cursor-not-allowed":"hover:shadow-sm"}
              ${s?"border-red-500":""}
              flex items-center justify-center
            `,onClick:()=>!i&&r(!n),children:n&&v.jsx(ob,{className:`${d.icon} text-white`,"aria-hidden":"true"})})]})}),(t||o)&&v.jsxs("div",{className:"flex-1 min-w-0",children:[t&&v.jsx("label",{htmlFor:h,className:`
                ${d.label}
                font-medium cursor-pointer
                ${i?"text-gray-500":"text-gray-300 hover:text-white"}
                ${s?"text-red-400":""}
                block
              `,children:t}),o&&v.jsx("p",{id:`${h}-description`,className:`
                ${d.description}
                ${i?"text-gray-600":"text-gray-400"}
                mt-1
              `,children:o}),s&&v.jsx("p",{className:"mt-1 text-sm text-red-500",children:s})]})]})},kv=({onGoogleAuth:e,isLoading:t=!1,disabled:n=!1,text:r="Continue with Google"})=>v.jsxs(zt,{type:"button",onClick:e,isLoading:t,disabled:n,className:"w-full flex items-center justify-center gap-2 bg-background-secondary hover:bg-background-tertiary text-text-primary border border-border-primary font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-background-primary disabled:opacity-50 disabled:cursor-not-allowed transform-gpu hover:shadow-md focus:ring-primary-500",children:[!t&&v.jsxs("svg",{className:"w-5 h-5",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:[v.jsx("path",{fill:"#4285F4",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),v.jsx("path",{fill:"#34A853",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),v.jsx("path",{fill:"#FBBC05",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),v.jsx("path",{fill:"#EA4335",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),r]}),gb=()=>{const[e,t]=_.useState(""),[n,r]=_.useState(""),[i,s]=_.useState(!0),[o,a]=_.useState(!1),[l,u]=_.useState({}),{login:c,signInWithGoogle:d,isLoading:h,isAuthenticated:f}=Xr(),g=En();_.useEffect(()=>{f&&g("/dashboard")},[f,g]);const w=()=>{const p={};return e?/\S+@\S+\.\S+/.test(e)||(p.email="Email is invalid"):p.email="Email is required",n||(p.password="Password is required"),u(p),Object.keys(p).length===0},x=async p=>{if(p.preventDefault(),!w())return;const m=await c(e,n,i);m.success?g("/dashboard"):u({general:m.error})},y=async()=>{a(!0),u({});try{const p=await d();p.success||(u({general:p.error||"Failed to sign in with Google"}),a(!1))}catch{u({general:"Failed to sign in with Google"}),a(!1)}};return v.jsx("div",{className:"min-h-screen flex items-center justify-center bg-background-primary",children:v.jsxs("div",{className:"max-w-md w-full space-y-8 p-8",children:[v.jsxs("div",{children:[v.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-white",children:"Sign in to ChewyAI"}),v.jsxs("p",{className:"mt-2 text-center text-sm text-gray-400",children:["Or"," ",v.jsx(hm,{to:"/signup",className:"font-medium text-primary-500 hover:text-primary-400",children:"create a new account"})]})]}),v.jsxs("div",{className:"space-y-4",children:[v.jsx(kv,{onGoogleAuth:y,isLoading:o,disabled:h,text:"Sign in with Google"}),v.jsxs("div",{className:"relative",children:[v.jsx("div",{className:"absolute inset-0 flex items-center",children:v.jsx("div",{className:"w-full border-t border-gray-600"})}),v.jsx("div",{className:"relative flex justify-center text-sm",children:v.jsx("span",{className:"px-2 bg-background-primary text-gray-400",children:"Or continue with email"})})]})]}),v.jsxs("form",{className:"mt-8 space-y-6",onSubmit:x,children:[l.general&&v.jsx("div",{className:"bg-red-900 border border-red-700 text-red-100 px-4 py-3 rounded",children:l.general}),v.jsxs("div",{className:"space-y-4",children:[v.jsx(Cr,{label:"Email address",type:"email",value:e,onChange:t,error:l.email,placeholder:"Enter your email",required:!0}),v.jsx(Cr,{label:"Password",type:"password",value:n,onChange:r,error:l.password,placeholder:"Enter your password",required:!0})]}),v.jsx("div",{className:"flex items-center justify-between",children:v.jsx(mb,{checked:i,onChange:s,label:"Stay signed in",description:"Keep me logged in across browser sessions",size:"sm"})}),v.jsx(zt,{type:"submit",isLoading:h,disabled:o,className:"w-full",size:"lg",children:"Sign in"})]})]})})},vb=()=>{const[e,t]=_.useState({name:"",email:"",password:"",confirmPassword:""}),[n,r]=_.useState(!1),[i,s]=_.useState({}),{signup:o,signInWithGoogle:a,isLoading:l}=Xr(),u=En(),c=()=>{const g={};return e.email?/\S+@\S+\.\S+/.test(e.email)||(g.email="Email is invalid"):g.email="Email is required",e.password?e.password.length<6&&(g.password="Password must be at least 6 characters"):g.password="Password is required",e.password!==e.confirmPassword&&(g.confirmPassword="Passwords do not match"),s(g),Object.keys(g).length===0},d=async g=>{if(g.preventDefault(),!c())return;const w=await o(e.email,e.password,e.name||void 0);w.success?u("/dashboard"):s({general:w.error||"Signup failed"})},h=async()=>{r(!0),s({});try{const g=await a();g.success||(s({general:g.error||"Failed to sign up with Google"}),r(!1))}catch{s({general:"Failed to sign up with Google"}),r(!1)}},f=(g,w)=>t(x=>({...x,[g]:w}));return v.jsx("div",{className:"min-h-screen flex items-center justify-center bg-background-primary",children:v.jsxs("div",{className:"max-w-md w-full space-y-8 p-8",children:[v.jsxs("div",{children:[v.jsx("h2",{className:"mt-6 text-center text-3xl font-extrabold text-white",children:"Create your account"}),v.jsxs("p",{className:"mt-2 text-center text-sm text-gray-400",children:["Or"," ",v.jsx(hm,{to:"/login",className:"font-medium text-primary-500 hover:text-primary-400",children:"sign in to your existing account"})]})]}),v.jsxs("div",{className:"space-y-4",children:[v.jsx(kv,{onGoogleAuth:h,isLoading:n,disabled:l,text:"Sign up with Google"}),v.jsxs("div",{className:"relative",children:[v.jsx("div",{className:"absolute inset-0 flex items-center",children:v.jsx("div",{className:"w-full border-t border-gray-600"})}),v.jsx("div",{className:"relative flex justify-center text-sm",children:v.jsx("span",{className:"px-2 bg-background-primary text-gray-400",children:"Or continue with email"})})]})]}),v.jsxs("form",{className:"mt-8 space-y-6",onSubmit:d,children:[i.general&&v.jsx("div",{className:"bg-red-900 border border-red-700 text-red-100 px-4 py-3 rounded",children:i.general}),v.jsxs("div",{className:"space-y-4",children:[v.jsx(Cr,{label:"Full Name (Optional)",value:e.name,onChange:g=>f("name",g),placeholder:"Enter your full name"}),v.jsx(Cr,{label:"Email address",type:"email",value:e.email,onChange:g=>f("email",g),error:i.email,placeholder:"Enter your email",required:!0}),v.jsx(Cr,{label:"Password",type:"password",value:e.password,onChange:g=>f("password",g),error:i.password,placeholder:"Create a password",required:!0}),v.jsx(Cr,{label:"Confirm Password",type:"password",value:e.confirmPassword,onChange:g=>f("confirmPassword",g),error:i.confirmPassword,placeholder:"Confirm your password",required:!0})]}),v.jsx(zt,{type:"submit",isLoading:l,disabled:n,className:"w-full",size:"lg",children:"Create Account"})]})]})})},yb=()=>{const[e,t]=_.useState(null),{handleOAuthCallback:n}=Xr(),r=En();return _.useEffect(()=>{(async()=>{try{const s=await n();s.success?r("/dashboard",{replace:!0}):(t(s.error||"Authentication failed"),setTimeout(()=>{r("/login",{replace:!0})},3e3))}catch{t("Failed to process authentication"),setTimeout(()=>{r("/login",{replace:!0})},3e3)}})()},[n,r]),e?v.jsx("div",{className:"min-h-screen flex items-center justify-center bg-background-primary",children:v.jsxs("div",{className:"max-w-md w-full text-center space-y-4 p-8",children:[v.jsxs("div",{className:"bg-red-900 border border-red-700 text-red-100 px-4 py-3 rounded",children:[v.jsx("h3",{className:"text-lg font-semibold",children:"Authentication Error"}),v.jsx("p",{className:"mt-2",children:e})]}),v.jsx("p",{className:"text-gray-400",children:"Redirecting to login page..."})]})}):v.jsx("div",{className:"min-h-screen flex items-center justify-center bg-background-primary",children:v.jsxs("div",{className:"max-w-md w-full text-center space-y-4 p-8",children:[v.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto"}),v.jsx("h3",{className:"text-lg font-semibold text-white",children:"Completing Authentication..."}),v.jsx("p",{className:"text-gray-400",children:"Please wait while we sign you in."})]})})},kt=({children:e})=>{const{isAuthenticated:t,isLoading:n,checkAuth:r}=Xr(),i=Cn();return _.useEffect(()=>{!t&&!n&&r()},[t,n,r]),n?v.jsx("div",{className:"min-h-screen flex items-center justify-center bg-background-primary",children:v.jsxs("div",{className:"text-center",children:[v.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"}),v.jsx("p",{className:"text-gray-400",children:"Checking authentication..."})]})}):t?v.jsx(v.Fragment,{children:e}):v.jsx(ou,{to:"/login",state:{from:i},replace:!0})},wb=({credits:e,showDetails:t=!1,size:n="md",variant:r="inline"})=>{const i=En(),s=()=>{i("/credits")},o=()=>{const d="flex items-center justify-between transition-all duration-200";switch(r){case"sidebar":return`${d} bg-background-tertiary rounded-lg p-3 border border-gray-700 hover:border-primary-500/50`;case"card":return`${d} bg-background-secondary rounded-lg p-4 border border-gray-600`;case"inline":default:return`${d} bg-gray-800/50 rounded-lg p-2`}},l=(()=>{switch(n){case"sm":return{primary:"text-sm",secondary:"text-xs",icon:"w-4 h-4"};case"lg":return{primary:"text-lg",secondary:"text-sm",icon:"w-6 h-6"};case"md":default:return{primary:"text-sm",secondary:"text-xs",icon:"w-5 h-5"}}})(),u=e<5,c=e===0;return v.jsxs("div",{className:o(),children:[v.jsxs("div",{className:"flex items-center space-x-2 flex-1 min-w-0",children:[v.jsx("div",{className:`
          flex items-center justify-center rounded-full p-1.5
          ${c?"bg-red-500/20 text-red-400":u?"bg-yellow-500/20 text-yellow-400":"bg-primary-500/20 text-primary-400"}
        `,children:v.jsx(fb,{className:l.icon})}),v.jsxs("div",{className:"flex-1 min-w-0",children:[v.jsxs("div",{className:"flex items-center space-x-1",children:[v.jsx("span",{className:`${l.primary} font-medium text-white`,children:e}),v.jsxs("span",{className:`${l.secondary} text-gray-400`,children:["credit",e!==1?"s":""]})]}),r==="sidebar"&&v.jsx("div",{className:`${l.secondary} text-gray-500 truncate`,children:c?"No credits remaining":u?"Running low":"Available for AI generation"})]})]}),(u||t)&&v.jsx("button",{onClick:s,className:`
            flex items-center justify-center rounded-lg transition-colors
            ${r==="sidebar"?"p-2 hover:bg-primary-500/20 text-primary-400 hover:text-primary-300":"p-1.5 hover:bg-gray-700 text-gray-400 hover:text-white"}
          `,title:"Buy more credits","aria-label":"Buy more credits",children:v.jsx(hb,{className:l.icon})})]})},xb=[{id:"study-sets",label:"Study Sets",path:"/dashboard",icon:lb,description:"Manage and study your flashcard sets"},{id:"documents",label:"Documents",path:"/documents",icon:cb,description:"Manage your documents"},{id:"analytics",label:"Analytics",path:"/analytics",icon:sb,description:"Study progress and insights"},{id:"credits",label:"Credits",path:"/credits",icon:ub,description:"Manage your AI credits"},{id:"help",label:"Help",path:"/help",icon:ib,description:"Get help and support"},{id:"settings",label:"Settings",path:"/settings",icon:ab,description:"Account and app settings"}],_b=({isOpen:e,onToggle:t})=>{const n=En(),r=Cn(),[i,s]=_.useState(!1),{user:o}=Xr();_.useEffect(()=>{const h=()=>{s(window.innerWidth<768)};return h(),window.addEventListener("resize",h),()=>window.removeEventListener("resize",h)},[]);const a=h=>h==="/dashboard"?r.pathname==="/dashboard"||r.pathname==="/":r.pathname.startsWith(h),l=h=>{n(h),i&&t()},u={open:{x:0,transition:{type:"spring",stiffness:300,damping:30}},closed:{x:"-100%",transition:{type:"spring",stiffness:300,damping:30}}},c={open:{opacity:1,transition:{duration:.2}},closed:{opacity:0,transition:{duration:.2}}},d={open:{opacity:1,x:0,transition:{type:"spring",stiffness:300,damping:30}},closed:{opacity:0,x:-20,transition:{duration:.2}}};return v.jsxs(v.Fragment,{children:[v.jsx(xv,{children:i&&e&&v.jsx(Q.div,{className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-40 md:hidden",variants:c,initial:"closed",animate:"open",exit:"closed",onClick:t})}),v.jsx(Q.aside,{className:`
          fixed top-0 left-0 h-full bg-background-secondary border-r border-border-primary z-50
          ${i?"w-80":"w-64"}
          md:relative md:translate-x-0
        `,variants:u,initial:i?"closed":"open",animate:e?"open":"closed","aria-label":"Main navigation",children:v.jsxs("div",{className:"flex flex-col h-full",children:[v.jsxs("div",{className:"flex items-center justify-between p-6 border-b border-border-primary",children:[v.jsxs("div",{className:"flex items-center space-x-3",children:[v.jsx("div",{className:"w-8 h-8 bg-primary-500 rounded-lg flex items-center justify-center",children:v.jsx("span",{className:"text-white font-bold text-lg",children:"C"})}),v.jsx("h1",{className:"text-xl font-bold text-white",children:"ChewyAI"})]}),i&&v.jsx("button",{onClick:t,className:"p-2 rounded-lg hover:bg-background-tertiary transition-colors","aria-label":"Close navigation",children:v.jsx(pb,{className:"w-5 h-5 text-gray-400"})})]}),v.jsx("nav",{className:"flex-1 px-4 py-6 overflow-y-auto",children:v.jsx("ul",{className:"space-y-2",role:"list",children:xb.map((h,f)=>{const g=h.icon,w=a(h.path);return v.jsx(Q.li,{variants:d,initial:"closed",animate:e?"open":"closed",transition:{delay:f*.05},children:v.jsxs("button",{onClick:()=>l(h.path),className:`
                        w-full flex items-center space-x-3 px-3 py-3 rounded-lg text-left
                        transition-all duration-200 group
                        ${w?"bg-primary-500/20 text-primary-400 border border-primary-500/30":"text-gray-300 hover:bg-background-tertiary hover:text-white"}
                      `,"aria-current":w?"page":void 0,"aria-describedby":`${h.id}-description`,children:[v.jsx(g,{className:`
                          w-5 h-5 transition-colors
                          ${w?"text-primary-400":"text-gray-400 group-hover:text-white"}
                        `}),v.jsxs("div",{className:"flex-1 min-w-0",children:[v.jsx("span",{className:"font-medium truncate block",children:h.label}),v.jsx("span",{id:`${h.id}-description`,className:"text-xs text-gray-500 group-hover:text-gray-400 truncate block",children:h.description})]}),w&&v.jsx(Q.div,{className:"w-2 h-2 bg-primary-400 rounded-full",layoutId:"activeIndicator",transition:{type:"spring",stiffness:300,damping:30}})]})},h.id)})})}),v.jsxs("div",{className:"p-4 border-t border-border-primary space-y-3",children:[o&&v.jsx(wb,{credits:o.credits_remaining||0,variant:"sidebar",size:"sm",showDetails:!0}),v.jsxs("div",{className:"text-xs text-gray-500 text-center",children:[v.jsx("p",{children:"ChewyAI v1.0.0"}),v.jsx("p",{className:"mt-1",children:"AI-Powered Study Materials"})]})]})]})}),i&&!e&&v.jsx("button",{onClick:t,className:"fixed top-4 left-4 z-40 p-3 bg-background-secondary border border-border-primary rounded-lg shadow-lg md:hidden","aria-label":"Open navigation menu",children:v.jsx(db,{className:"w-6 h-6 text-white"})})]})},Ct=({children:e})=>{const[t,n]=_.useState(!1),[r,i]=_.useState(!1);_.useEffect(()=>{const o=()=>{const a=window.innerWidth<768;i(a),n(!a)};return o(),window.addEventListener("resize",o),()=>window.removeEventListener("resize",o)},[]);const s=()=>{n(!t)};return v.jsxs("div",{className:"min-h-screen bg-background-primary flex",children:[v.jsx(_b,{isOpen:t,onToggle:s}),v.jsx("main",{className:`
          flex-1 transition-all duration-300 ease-in-out
          ml-0
          ${r?"w-full":""}
        `,id:"main-content",children:v.jsx("div",{className:"w-full h-full",children:e})})]})},Sb=({links:e})=>v.jsx("nav",{className:"sr-only focus-within:not-sr-only","aria-label":"Skip navigation",children:v.jsx("div",{className:"fixed top-0 left-0 z-50 bg-primary-600 text-white p-4 rounded-br-lg shadow-lg",children:v.jsx("ul",{className:"flex flex-col space-y-2",children:e.map((t,n)=>v.jsx("li",{children:v.jsx("a",{href:t.href,className:"block px-3 py-2 text-sm font-medium rounded-md hover:bg-primary-700 focus:bg-primary-700 focus:outline-none focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-primary-600",onClick:r=>{r.preventDefault();const i=document.querySelector(t.href);i&&(i.scrollIntoView({behavior:"smooth"}),i instanceof HTMLElement&&i.focus())},children:t.label})},n))})})}),kb=[{keys:["?"],description:"Show keyboard shortcuts",context:"Global"},{keys:["Escape"],description:"Close modal or cancel action",context:"Global"},{keys:["←","→"],description:"Navigate between cards/questions",context:"Study"},{keys:["Space","↑","↓"],description:"Flip flashcard or select answer",context:"Study"},{keys:["F"],description:"Flag current item for review",context:"Study"},{keys:["Enter"],description:"Submit answer or continue",context:"Study"},{keys:["Ctrl","Z"],description:"Undo last action",context:"Study"},{keys:["Ctrl","Y"],description:"Redo last action",context:"Study"},{keys:["1","2","3","4"],description:"Select quiz answer option",context:"Quiz"},{keys:["G","D"],description:"Go to Dashboard",context:"Navigation"},{keys:["G","O"],description:"Go to Documents",context:"Navigation"},{keys:["G","S"],description:"Go to Study Sets",context:"Navigation"},{keys:["G","A"],description:"Go to Analytics",context:"Navigation"},{keys:["Ctrl","U"],description:"Upload new document",context:"Documents"},{keys:["Ctrl","F"],description:"Search documents",context:"Documents"},{keys:["Delete"],description:"Delete selected document",context:"Documents"}],Cv=_.memo(({keys:e})=>v.jsx("div",{className:"flex items-center space-x-1",children:e.map((t,n)=>v.jsxs(Fe.Fragment,{children:[n>0&&v.jsx("span",{className:"text-gray-400 text-xs",children:"+"}),v.jsx("kbd",{className:"px-2 py-1 text-xs font-semibold text-gray-800 bg-gray-200 border border-gray-300 rounded-lg",children:t})]},t))}));Cv.displayName="KeyboardKey";const Ev=_.memo(({isOpen:e,onClose:t})=>{if(_.useEffect(()=>{const r=i=>{i.key==="Escape"&&t()};return e&&(document.addEventListener("keydown",r),document.body.style.overflow="hidden"),()=>{document.removeEventListener("keydown",r),document.body.style.overflow="unset"}},[e,t]),!e)return null;const n=kb.reduce((r,i)=>{const s=i.context||"General";return r[s]||(r[s]=[]),r[s].push(i),r},{});return v.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",onClick:t,role:"dialog","aria-modal":"true","aria-labelledby":"shortcuts-title",children:v.jsxs("div",{className:"bg-background-secondary border border-gray-600 rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[80vh] overflow-y-auto",onClick:r=>r.stopPropagation(),children:[v.jsxs("div",{className:"flex items-center justify-between mb-6",children:[v.jsx("h2",{id:"shortcuts-title",className:"text-xl font-semibold text-white",children:"Keyboard Shortcuts"}),v.jsx(zt,{onClick:t,variant:"secondary",size:"sm","aria-label":"Close shortcuts modal",children:"✕"})]}),v.jsx("div",{className:"space-y-6",children:Object.entries(n).map(([r,i])=>v.jsxs("div",{children:[v.jsx("h3",{className:"text-lg font-medium text-gray-300 mb-3 border-b border-gray-600 pb-1",children:r}),v.jsx("div",{className:"space-y-2",children:i.map((s,o)=>v.jsxs("div",{className:"flex items-center justify-between py-2",children:[v.jsx("span",{className:"text-gray-400 flex-1",children:s.description}),v.jsx(Cv,{keys:s.keys})]},o))})]},r))}),v.jsx("div",{className:"mt-6 pt-4 border-t border-gray-600",children:v.jsxs("p",{className:"text-sm text-gray-500 text-center",children:["Press ",v.jsx("kbd",{className:"px-1 py-0.5 text-xs bg-gray-200 text-gray-800 rounded",children:"?"})," anytime to show this help"]})})]})})});Ev.displayName="KeyboardShortcutsModal";const Cb=(e={})=>{const{enableGlobalShortcuts:t=!0,enableNavigationShortcuts:n=!0}=e,r=En(),[i,s]=_.useState(!1),[o,a]=_.useState([]);return _.useEffect(()=>{if(!t)return;const l=u=>{var c;if(!(u.target instanceof HTMLInputElement||u.target instanceof HTMLTextAreaElement||u.target instanceof HTMLSelectElement||((c=u.target)==null?void 0:c.contentEditable)==="true")){if(u.key==="?"&&!u.ctrlKey&&!u.metaKey&&!u.altKey){u.preventDefault(),s(!0);return}if(u.key==="Escape"){s(!1),a([]);return}if(n){if(u.key.toLowerCase()==="g"&&!u.ctrlKey&&!u.metaKey&&!u.altKey){u.preventDefault(),a(["g"]),setTimeout(()=>a([]),2e3);return}if(o.includes("g")){switch(u.preventDefault(),u.key.toLowerCase()){case"d":case"s":r("/dashboard");break;case"o":r("/documents");break;case"a":r("/analytics");break}a([]);return}}if(u.ctrlKey||u.metaKey)switch(u.key.toLowerCase()){case"u":if(window.location.pathname==="/documents"){u.preventDefault();const d=new CustomEvent("trigger-upload");document.dispatchEvent(d)}break;case"f":if(window.location.pathname==="/documents"){u.preventDefault();const d=document.querySelector('input[placeholder*="search" i]');d&&d.focus()}break}}};return document.addEventListener("keydown",l),()=>{document.removeEventListener("keydown",l)}},[t,n,r,o]),{showShortcutsModal:i,setShowShortcutsModal:s,lastKeySequence:o}},id=({isOpen:e,onClose:t,children:n,title:r,className:i="",closeOnOverlayClick:s=!0,closeOnEscape:o=!0,size:a="md"})=>{const l=_.useRef(null),u=_.useRef(null),c={sm:"max-w-sm",md:"max-w-md",lg:"max-w-lg"};_.useEffect(()=>(e?(u.current=document.activeElement,document.body.style.overflow="hidden",setTimeout(()=>{var f;(f=l.current)==null||f.focus()},100)):(document.body.style.overflow="unset",u.current&&u.current.focus()),()=>{document.body.style.overflow="unset"}),[e]),_.useEffect(()=>{const f=g=>{var w;if(g.key==="Escape"&&o&&t(),g.key==="Tab"&&e){const x=(w=l.current)==null?void 0:w.querySelectorAll('button, [href], input, select, textarea, [tabindex]:not([tabindex="-1"])');if(x&&x.length>0){const y=x[0],p=x[x.length-1];g.shiftKey?document.activeElement===y&&(g.preventDefault(),p.focus()):document.activeElement===p&&(g.preventDefault(),y.focus())}}};return e&&document.addEventListener("keydown",f),()=>{document.removeEventListener("keydown",f)}},[e,o,t]);const d={hidden:{opacity:0},visible:{opacity:1,transition:{duration:.2,ease:"easeOut"}},exit:{opacity:0,transition:{duration:.15,ease:"easeIn"}}},h={hidden:{opacity:0,scale:.9,y:-30,rotateX:15},visible:{opacity:1,scale:1,y:0,rotateX:0,transition:{type:"spring",damping:20,stiffness:300,mass:.8,opacity:{duration:.2}}},exit:{opacity:0,scale:.95,y:-10,transition:{duration:.15,ease:"easeIn"}}};return v.jsx(xv,{children:e&&v.jsxs(Q.div,{className:"fixed inset-0 z-50 flex items-center justify-center p-4",initial:"hidden",animate:"visible",exit:"exit",children:[v.jsx(Q.div,{className:"absolute inset-0 bg-black bg-opacity-50 backdrop-blur-sm",onClick:s?t:void 0,variants:d,initial:"hidden",animate:"visible",exit:"exit"}),v.jsxs(Q.div,{ref:l,className:`
              relative w-full ${c[a]} bg-background-secondary 
              border border-gray-600 rounded-lg shadow-xl
              ${i}
            `,variants:h,initial:"hidden",animate:"visible",exit:"exit",role:"dialog","aria-modal":"true","aria-labelledby":r?"dialog-title":void 0,"aria-describedby":"dialog-content",tabIndex:-1,children:[r&&v.jsx("div",{className:"px-6 py-4 border-b border-gray-600",children:v.jsx("h2",{id:"dialog-title",className:"text-lg font-semibold text-white",children:r})}),v.jsx("div",{id:"dialog-content",className:"px-6 py-4",children:n})]})]})})},Eb=({message:e,priority:t="polite",clearAfter:n=3e3})=>{const[r,i]=_.useState("");return _.useEffect(()=>{if(e&&(i(e),n>0)){const s=setTimeout(()=>{i("")},n);return()=>clearTimeout(s)}},[e,n]),v.jsx("div",{"aria-live":t,"aria-atomic":"true",className:"sr-only",role:"status",children:r})},bb=({isOpen:e,onClose:t,title:n,message:r,confirmText:i="OK",variant:s="info"})=>{const o=()=>{switch(s){case"success":return v.jsx("div",{className:"flex-shrink-0 w-10 h-10 mx-auto bg-green-100 rounded-full flex items-center justify-center",children:v.jsx("svg",{className:"w-6 h-6 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:v.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})})});case"warning":return v.jsx("div",{className:"flex-shrink-0 w-10 h-10 mx-auto bg-yellow-100 rounded-full flex items-center justify-center",children:v.jsx("svg",{className:"w-6 h-6 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:v.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})});case"error":return v.jsx("div",{className:"flex-shrink-0 w-10 h-10 mx-auto bg-red-100 rounded-full flex items-center justify-center",children:v.jsx("svg",{className:"w-6 h-6 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:v.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})});default:return v.jsx("div",{className:"flex-shrink-0 w-10 h-10 mx-auto bg-blue-100 rounded-full flex items-center justify-center",children:v.jsx("svg",{className:"w-6 h-6 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:v.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})})}},a=()=>{switch(s){case"error":return"danger";default:return"primary"}},l={hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1,delayChildren:.1}}},u={hidden:{opacity:0,y:10},visible:{opacity:1,y:0,transition:{duration:.3,ease:"easeOut"}}},c=()=>`${n?`${n}: `:""}${r}`;return v.jsxs(v.Fragment,{children:[v.jsx(Eb,{message:e?c():"",priority:s==="error"?"assertive":"polite"}),v.jsx(id,{isOpen:e,onClose:t,title:n,size:"sm",closeOnOverlayClick:!1,children:v.jsxs(Q.div,{className:"text-center",variants:l,initial:"hidden",animate:"visible",children:[v.jsx(Q.div,{variants:u,children:o()}),v.jsx(Q.div,{className:"mt-4",variants:u,children:v.jsx("p",{className:"text-white leading-relaxed whitespace-pre-line",children:r})}),v.jsx(Q.div,{className:"mt-6",variants:u,children:v.jsx(zt,{onClick:t,variant:a(),className:"w-full",autoFocus:!0,children:i})})]})})]})},Pb=({isOpen:e,onClose:t,onConfirm:n,title:r,message:i,confirmText:s="Confirm",cancelText:o="Cancel",variant:a="info",isLoading:l=!1,buttonLayout:u="default",additionalContent:c})=>{const d=()=>{n()},h=()=>{switch(a){case"warning":return v.jsx("div",{className:"flex-shrink-0 w-10 h-10 mx-auto bg-yellow-100 rounded-full flex items-center justify-center",children:v.jsx("svg",{className:"w-6 h-6 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:v.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})});case"danger":return v.jsx("div",{className:"flex-shrink-0 w-10 h-10 mx-auto bg-red-100 rounded-full flex items-center justify-center",children:v.jsx("svg",{className:"w-6 h-6 text-red-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:v.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})});default:return v.jsx("div",{className:"flex-shrink-0 w-10 h-10 mx-auto bg-blue-100 rounded-full flex items-center justify-center",children:v.jsx("svg",{className:"w-6 h-6 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24","aria-hidden":"true",children:v.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"})})})}},f=()=>{switch(a){case"danger":return"danger";case"warning":return"primary";default:return"primary"}},g={hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1,delayChildren:.1}}},w={hidden:{opacity:0,y:10},visible:{opacity:1,y:0,transition:{duration:.3,ease:"easeOut"}}};return v.jsx(id,{isOpen:e,onClose:t,title:r,size:"sm",closeOnOverlayClick:!1,children:v.jsxs(Q.div,{className:"text-center",variants:g,initial:"hidden",animate:"visible",children:[v.jsx(Q.div,{variants:w,children:h()}),v.jsx(Q.div,{className:"mt-4",variants:w,children:v.jsx("p",{className:"text-white leading-relaxed whitespace-pre-line",children:i})}),c&&v.jsx(Q.div,{className:"mt-4",variants:w,children:c}),v.jsxs(Q.div,{className:`mt-6 ${u==="corners"?"flex justify-between items-center":"flex flex-col-reverse sm:flex-row sm:gap-3"}`,variants:w,children:[v.jsx(zt,{onClick:t,variant:"secondary",className:u==="corners"?"px-6":"w-full sm:w-auto mt-3 sm:mt-0",disabled:l,children:o}),v.jsx(zt,{onClick:d,variant:f(),className:u==="corners"?"px-6":"w-full sm:w-auto",isLoading:l,autoFocus:!0,children:s})]})]})})},Tb=({isOpen:e,onClose:t,onConfirm:n,title:r,message:i,placeholder:s="",defaultValue:o="",confirmText:a="OK",cancelText:l="Cancel",inputType:u="text",validation:c,isLoading:d=!1})=>{const[h,f]=_.useState(o),[g,w]=_.useState(null),x=_.useRef(null);_.useEffect(()=>{e&&(f(o),w(null),setTimeout(()=>{var E,b;(E=x.current)==null||E.focus(),(b=x.current)==null||b.select()},150))},[e,o]);const y=()=>{const E=h.trim();if(c){const b=c(E);if(b){w(b);return}}n(E)},p=E=>{E.key==="Enter"&&!E.shiftKey&&(E.preventDefault(),y())},m=E=>{const b=E.target.value;f(b),g&&w(null)},S=!g&&h.trim().length>0,k={hidden:{opacity:0},visible:{opacity:1,transition:{staggerChildren:.1,delayChildren:.1}}},C={hidden:{opacity:0,y:10},visible:{opacity:1,y:0,transition:{duration:.3,ease:"easeOut"}}};return v.jsx(id,{isOpen:e,onClose:t,title:r,size:"md",closeOnOverlayClick:!1,children:v.jsxs(Q.div,{variants:k,initial:"hidden",animate:"visible",children:[v.jsx(Q.div,{className:"mb-4",variants:C,children:v.jsx("p",{className:"text-white leading-relaxed whitespace-pre-line",children:i})}),v.jsxs(Q.div,{className:"mb-4",variants:C,children:[v.jsx(Q.input,{ref:x,type:u,value:h,onChange:m,onKeyDown:p,placeholder:s,disabled:d,className:`
              w-full px-3 py-2 bg-background-primary border rounded-md text-white 
              placeholder-gray-400 focus:outline-none focus:ring-2 transition-colors
              ${g?"border-red-500 focus:ring-red-500":"border-gray-600 focus:ring-primary-500"}
              disabled:opacity-50 disabled:cursor-not-allowed
            `,"aria-describedby":g?"input-error":void 0,"aria-invalid":!!g,"aria-required":"true","aria-label":s||"Input field",initial:{scale:.95},animate:{scale:1},transition:{delay:.2,duration:.2}}),g&&v.jsx(Q.p,{id:"input-error",className:"mt-2 text-sm text-red-400",role:"alert",initial:{opacity:0,y:-10},animate:{opacity:1,y:0},transition:{duration:.2},children:g})]}),v.jsxs(Q.div,{className:"flex flex-col-reverse sm:flex-row sm:gap-3",variants:C,children:[v.jsx(zt,{onClick:t,variant:"secondary",className:"w-full sm:w-auto mt-3 sm:mt-0",disabled:d,children:l}),v.jsx(zt,{onClick:y,variant:"primary",className:"w-full sm:w-auto",disabled:!S,isLoading:d,children:a})]})]})})},jb=({checked:e,onChange:t,disabled:n=!1,className:r=""})=>v.jsxs("div",{className:`flex items-center space-x-2 ${r}`,children:[v.jsx("input",{type:"checkbox",id:"never-ask-again",checked:e,onChange:i=>t(i.target.checked),disabled:n,className:"w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500 focus:ring-2 disabled:opacity-50 disabled:cursor-not-allowed"}),v.jsx("label",{htmlFor:"never-ask-again",className:`text-sm text-gray-300 select-none ${n?"opacity-50 cursor-not-allowed":"cursor-pointer"}`,children:"Never ask again"})]}),bv=_.createContext(void 0),NP=()=>{const e=_.useContext(bv);if(!e)throw new Error("useDialog must be used within a DialogProvider");return e},Ab=({children:e})=>{const[t,n]=_.useState({type:null,isOpen:!1,options:null,resolve:null,isLoading:!1}),[r,i]=_.useState(!1),s=_.useCallback(()=>{n(w=>({...w,isOpen:!1,isLoading:!1})),setTimeout(()=>{n({type:null,isOpen:!1,options:null,resolve:null,isLoading:!1}),i(!1)},200)},[]),o=_.useCallback(w=>new Promise(x=>{n({type:"alert",isOpen:!0,options:w,resolve:x,isLoading:!1})}),[]),a=_.useCallback(w=>new Promise(x=>{n({type:"confirm",isOpen:!0,options:w,resolve:x,isLoading:!1})}),[]),l=_.useCallback(w=>new Promise(x=>{n({type:"prompt",isOpen:!0,options:w,resolve:x,isLoading:!1})}),[]),u=_.useCallback(()=>{t.resolve&&t.resolve(void 0),s()},[t.resolve,s]),c=_.useCallback(()=>{t.resolve&&t.resolve(!1),s()},[t.resolve,s]),d=_.useCallback(()=>{t.resolve&&t.resolve(!0),s()},[t.resolve,s]),h=_.useCallback(()=>{t.resolve&&t.resolve(null),s()},[t.resolve,s]),f=_.useCallback(w=>{t.resolve&&t.resolve(w),s()},[t.resolve,s]),g={alert:o,confirm:a,prompt:l,closeDialog:s};return v.jsxs(bv.Provider,{value:g,children:[e,t.type==="alert"&&t.options&&v.jsx(bb,{isOpen:t.isOpen,onClose:u,...t.options}),t.type==="confirm"&&t.options&&(()=>{const w=t.options,x=w.showNeverAskAgain?v.jsx(jb,{checked:r,onChange:y=>{var p;i(y),(p=w.onNeverAskAgainChange)==null||p.call(w,y)},disabled:t.isLoading}):void 0;return v.jsx(Pb,{isOpen:t.isOpen,onClose:c,onConfirm:d,isLoading:t.isLoading,additionalContent:x,...w})})(),t.type==="prompt"&&t.options&&v.jsx(Tb,{isOpen:t.isOpen,onClose:h,onConfirm:f,isLoading:t.isLoading,...t.options})]})},Rb=_.lazy(()=>$e(()=>import("./Dashboard-e6a96f7d.js"),["assets/Dashboard-e6a96f7d.js","assets/index-6232208d.js","assets/documentStore-0ecab0bf.js"]).then(e=>({default:e.Dashboard}))),Lb=_.lazy(()=>$e(()=>import("./DocumentsPage-70e0f595.js"),["assets/DocumentsPage-70e0f595.js","assets/index-6232208d.js","assets/documentStore-0ecab0bf.js"]).then(e=>({default:e.DocumentsPage}))),Ob=_.lazy(()=>$e(()=>import("./CreateStudySetPage-c0b68215.js"),[]).then(e=>({default:e.CreateStudySetPage}))),Ib=_.lazy(()=>$e(()=>import("./StudySetPage-afeabde5.js"),["assets/StudySetPage-afeabde5.js","assets/studyStore-23baa9e6.js","assets/documentStore-0ecab0bf.js","assets/DifficultySelector-fefbcacc.js","assets/useUserSettings-86f1e62d.js"]).then(e=>({default:e.StudySetPage}))),Nb=_.lazy(()=>$e(()=>import("./StudyPage-976e8f95.js"),["assets/StudyPage-976e8f95.js","assets/studyStore-23baa9e6.js","assets/useUserSettings-86f1e62d.js"]).then(e=>({default:e.StudyPage}))),Mb=_.lazy(()=>$e(()=>import("./AnalyticsPage-5a31fafa.js"),["assets/AnalyticsPage-5a31fafa.js","assets/studyStore-23baa9e6.js","assets/documentStore-0ecab0bf.js"]).then(e=>({default:e.AnalyticsPage}))),Db=_.lazy(()=>$e(()=>import("./CreditsPage-3a121dc4.js"),[]).then(e=>({default:e.CreditsPage}))),Vb=_.lazy(()=>$e(()=>import("./HelpPage-2ca579b1.js"),[]).then(e=>({default:e.HelpPage}))),$b=_.lazy(()=>$e(()=>import("./SettingsPage-6b8c379f.js"),["assets/SettingsPage-6b8c379f.js","assets/DifficultySelector-fefbcacc.js"]).then(e=>({default:e.SettingsPage}))),zb=()=>{const e=Cn(),{showShortcutsModal:t,setShowShortcutsModal:n}=Cb(),{checkAuth:r,isLoading:i,isAuthenticated:s}=Xr();_.useEffect(()=>{r()},[r]);const o=()=>{const a=e.pathname;return a==="/dashboard"?[{href:"#main-content",label:"Skip to main content"},{href:"#study-sets",label:"Skip to study sets list"},{href:"#create-study-set",label:"Skip to create study set"}]:a.startsWith("/study/")?[{href:"#main-content",label:"Skip to main content"},{href:"#flashcard",label:"Skip to flashcard"},{href:"#navigation-controls",label:"Skip to navigation controls"}]:a==="/documents"?[{href:"#main-content",label:"Skip to main content"},{href:"#upload-section",label:"Skip to upload section"},{href:"#document-list",label:"Skip to document list"}]:[{href:"#main-content",label:"Skip to main content"}]};return i?v.jsx("div",{className:"min-h-screen bg-background-primary flex items-center justify-center",children:v.jsxs("div",{className:"text-center",children:[v.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"}),v.jsx("p",{className:"text-gray-400",children:"Checking authentication..."})]})}):v.jsxs(v.Fragment,{children:[v.jsx(Sb,{links:o()}),v.jsx(_.Suspense,{fallback:v.jsx("div",{className:"min-h-screen bg-background-primary flex items-center justify-center",children:v.jsxs("div",{className:"text-center",children:[v.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary-500 mx-auto mb-4"}),v.jsx("p",{className:"text-gray-400",children:"Loading..."})]})}),children:v.jsxs(qw,{children:[v.jsx(Re,{path:"/",element:s?v.jsx(ou,{to:"/dashboard",replace:!0}):v.jsx(ou,{to:"/login",replace:!0})}),v.jsx(Re,{path:"/login",element:v.jsx(gb,{})}),v.jsx(Re,{path:"/signup",element:v.jsx(vb,{})}),v.jsx(Re,{path:"/auth/callback",element:v.jsx(yb,{})}),v.jsx(Re,{path:"/dashboard",element:v.jsx(kt,{children:v.jsx(Ct,{children:v.jsx(Rb,{})})})}),v.jsx(Re,{path:"/documents",element:v.jsx(kt,{children:v.jsx(Ct,{children:v.jsx(Lb,{})})})}),v.jsx(Re,{path:"/create-study-set",element:v.jsx(kt,{children:v.jsx(Ct,{children:v.jsx(Ob,{})})})}),v.jsx(Re,{path:"/study-sets/:id",element:v.jsx(kt,{children:v.jsx(Ct,{children:v.jsx(Ib,{})})})}),v.jsx(Re,{path:"/study/:id/:mode",element:v.jsx(kt,{children:v.jsx(Ct,{children:v.jsx(Nb,{})})})}),v.jsx(Re,{path:"/analytics",element:v.jsx(kt,{children:v.jsx(Ct,{children:v.jsx(Mb,{})})})}),v.jsx(Re,{path:"/credits",element:v.jsx(kt,{children:v.jsx(Ct,{children:v.jsx(Db,{})})})}),v.jsx(Re,{path:"/help",element:v.jsx(kt,{children:v.jsx(Ct,{children:v.jsx(Vb,{})})})}),v.jsx(Re,{path:"/settings",element:v.jsx(kt,{children:v.jsx(Ct,{children:v.jsx($b,{})})})})]})}),v.jsx(Ev,{isOpen:t,onClose:()=>n(!1)})]})};function Bb(){return v.jsx(tx,{future:{v7_startTransition:!0,v7_relativeSplatPath:!0},children:v.jsx(Ab,{children:v.jsx(zb,{})})})}hl.createRoot(document.getElementById("root")).render(v.jsx(Fe.StrictMode,{children:v.jsx(Bb,{})}));export{ab as $,dP as A,zt as B,mb as C,bP as D,hP as E,ub as F,sP as G,mP as H,Cr as I,kP as J,hb as K,ob as L,TP as M,uP as N,SP as O,fP as P,oP as Q,Fe as R,EP as S,yP as T,AP as U,pb as V,PP as W,aP as X,wP as Y,IP as Z,$e as _,NP as a,tP as a0,cP as a1,OP as a2,_P as a3,Zb as a4,Xr as b,Ax as c,pP as d,Fb as e,lb as f,Hf as g,cb as h,lP as i,v as j,ib as k,rP as l,fb as m,Q as n,LP as o,iP as p,xP as q,_ as r,sb as s,RP as t,En as u,vP as v,nP as w,jP as x,CP as y,gP as z};
